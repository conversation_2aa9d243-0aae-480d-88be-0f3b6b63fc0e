from django.urls import path
from . import views

app_name = 'sales'

urlpatterns = [
    path('', views.index, name='index'),
    path('new/', views.new_sale, name='new_sale'),
    path('view/<int:sale_id>/', views.view_sale, name='view_sale'),
    path('edit/<int:sale_id>/', views.edit_sale, name='edit_sale'),
    path('delete/<int:sale_id>/', views.delete_sale, name='delete_sale'),
    path('invoice/<int:sale_id>/', views.invoice, name='invoice'),
    path('email-invoice/<int:sale_id>/', views.email_invoice, name='email_invoice'),
    path('add-payment/<int:sale_id>/', views.add_payment, name='add_payment'),
    path('check-stock/', views.check_stock, name='check_stock'),
    path('get-promotions/', views.get_promotions, name='get_promotions'),
    path('export/excel/', views.export_sales_excel, name='export_excel'),
    path('export/pdf/', views.export_sales_pdf, name='export_pdf'),
]
