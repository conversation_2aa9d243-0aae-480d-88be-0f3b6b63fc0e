#!/usr/bin/env python
import os
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'auto_parts_pos.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from inventory.barcode_models import Barcode

def test_barcode_view():
    # إنشاء client للاختبار
    client = Client()
    
    # تسجيل الدخول
    user = User.objects.get(username='admin')
    client.force_login(user)
    
    # اختبار الباركود
    barcode_number = '1282235856942741'
    
    print(f"Testing barcode: {barcode_number}")
    
    # التحقق من وجود الباركود في قاعدة البيانات
    try:
        barcode = Barcode.objects.get(barcode_number=barcode_number)
        print(f"Barcode found in database: {barcode.barcode_number} for product: {barcode.product.name}")
    except Barcode.DoesNotExist:
        print("Barcode not found in database")
        return
    
    # إرسال POST request
    response = client.post('/inventory/barcode/scan/', {
        'barcode_number': barcode_number
    })
    
    print(f"Response status: {response.status_code}")
    print(f"Response content type: {response.get('Content-Type', 'Unknown')}")
    print(f"Response content: {response.content.decode('utf-8')}")
    
    if response.status_code == 200:
        try:
            import json
            data = json.loads(response.content)
            print(f"JSON Response: {json.dumps(data, indent=2, ensure_ascii=False)}")
        except:
            print("Could not parse as JSON")

if __name__ == '__main__':
    test_barcode_view()
