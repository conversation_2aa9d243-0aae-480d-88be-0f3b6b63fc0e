from django.db import models
from django.utils.translation import gettext_lazy as _

class CustomerCategory(models.Model):
    name = models.CharField(_('الاسم'), max_length=100)
    description = models.TextField(_('الوصف'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('فئة العملاء')
        verbose_name_plural = _('فئات العملاء')
        ordering = ['name']

    def __str__(self):
        return self.name

class Customer(models.Model):
    name = models.CharField(_('الاسم'), max_length=200)
    phone = models.CharField(_('رقم الهاتف'), max_length=20, unique=True)
    email = models.EmailField(_('البريد الإلكتروني'), blank=True, null=True, unique=True)
    address = models.TextField(_('العنوان'), blank=True, null=True)
    city = models.CharField(_('المدينة'), max_length=100, blank=True, null=True)
    category = models.ForeignKey(CustomerCategory, on_delete=models.SET_NULL, null=True, blank=True, related_name='customers', verbose_name=_('الفئة'))
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    is_active = models.BooleanField(_('نشط'), default=True)
    credit_limit = models.DecimalField(_('حد الائتمان'), max_digits=10, decimal_places=2, default=0)
    balance = models.DecimalField(_('الرصيد الحالي'), max_digits=10, decimal_places=2, default=0)
    last_purchase_date = models.DateField(_('تاريخ آخر شراء'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('عميل')
        verbose_name_plural = _('العملاء')
        ordering = ['name']

    def __str__(self):
        return self.name

    @property
    def total_purchases(self):
        from sales.models import Sale
        return Sale.objects.filter(customer=self).count()

    @property
    def total_amount_spent(self):
        from sales.models import Sale
        return Sale.objects.filter(customer=self).aggregate(models.Sum('total_amount'))['total_amount__sum'] or 0

    @property
    def pending_sales(self):
        from sales.models import Sale
        return Sale.objects.filter(customer=self, status='pending').count()

    @property
    def pending_amount(self):
        from sales.models import Sale
        return Sale.objects.filter(customer=self, status='pending').aggregate(models.Sum('total_amount'))['total_amount__sum'] or 0

    @property
    def last_purchase(self):
        from sales.models import Sale
        last_sale = Sale.objects.filter(customer=self).order_by('-date').first()
        return last_sale

    def update_balance(self):
        """تحديث رصيد العميل بناءً على المبيعات المعلقة"""
        self.balance = self.pending_amount
        if self.last_purchase:
            self.last_purchase_date = self.last_purchase.date
        self.save(update_fields=['balance', 'last_purchase_date'])

    def can_purchase_on_credit(self, amount):
        """التحقق مما إذا كان العميل يمكنه الشراء بالائتمان"""
        if not self.is_active:
            return False
        if self.credit_limit <= 0:
            return False
        return (self.balance + amount) <= self.credit_limit

class CustomerVehicle(models.Model):
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='vehicles', verbose_name=_('العميل'))
    make = models.CharField(_('الشركة المصنعة'), max_length=100)
    model = models.CharField(_('الموديل'), max_length=100)
    year = models.PositiveIntegerField(_('سنة الصنع'))
    license_plate = models.CharField(_('رقم اللوحة'), max_length=20, blank=True, null=True)
    vin = models.CharField(_('رقم الهيكل (VIN)'), max_length=50, blank=True, null=True)
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('مركبة العميل')
        verbose_name_plural = _('مركبات العملاء')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.customer.name} - {self.make} {self.model} ({self.year})"


class CustomerInteraction(models.Model):
    INTERACTION_TYPES = [
        ('call', _('مكالمة هاتفية')),
        ('email', _('بريد إلكتروني')),
        ('visit', _('زيارة')),
        ('purchase', _('عملية شراء')),
        ('return', _('عملية إرجاع')),
        ('complaint', _('شكوى')),
        ('other', _('أخرى')),
    ]

    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='interactions', verbose_name=_('العميل'))
    interaction_type = models.CharField(_('نوع التفاعل'), max_length=20, choices=INTERACTION_TYPES)
    date = models.DateTimeField(_('التاريخ'), auto_now_add=True)
    notes = models.TextField(_('ملاحظات'))
    employee = models.ForeignKey('employees.EmployeeProfile', on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_('الموظف'))
    related_sale = models.ForeignKey('sales.Sale', on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_('المبيعات المرتبطة'))

    class Meta:
        verbose_name = _('تفاعل العميل')
        verbose_name_plural = _('تفاعلات العملاء')
        ordering = ['-date']

    def __str__(self):
        return f"{self.customer.name} - {self.get_interaction_type_display()} - {self.date.strftime('%Y-%m-%d %H:%M')}"


class CustomerImport(models.Model):
    STATUS_CHOICES = [
        ('pending', _('قيد الانتظار')),
        ('processing', _('قيد المعالجة')),
        ('completed', _('مكتمل')),
        ('failed', _('فشل')),
    ]

    file = models.FileField(_('ملف الاستيراد'), upload_to='customer_imports/')
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)
    processed_records = models.PositiveIntegerField(_('السجلات المعالجة'), default=0)
    total_records = models.PositiveIntegerField(_('إجمالي السجلات'), default=0)
    error_log = models.TextField(_('سجل الأخطاء'), blank=True, null=True)
    imported_by = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, verbose_name=_('تم الاستيراد بواسطة'))

    class Meta:
        verbose_name = _('استيراد العملاء')
        verbose_name_plural = _('عمليات استيراد العملاء')
        ordering = ['-created_at']

    def __str__(self):
        return f"استيراد العملاء #{self.id} - {self.get_status_display()} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"
