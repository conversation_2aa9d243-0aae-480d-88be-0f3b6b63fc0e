{% extends 'base.html' %}
{% load i18n %}
{% load static %}
{% load report_filters %}

{% block title %}{% trans "التقرير المالي" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/daterangepicker.css' %}">
<style>
    .chart-container {
        position: relative;
        height: 300px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "التقرير المالي" %}</h1>
        <div>
            <a href="{% url 'reports:index' %}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i> {% trans "العودة" %}
            </a>
            <div class="btn-group">
                <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-download me-1"></i> {% trans "تصدير" %}
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="#">PDF</a></li>
                    <li><a class="dropdown-item" href="#">Excel</a></li>
                    <li><a class="dropdown-item" href="#">CSV</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "تصفية حسب التاريخ" %}</h6>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3 align-items-center">
                <div class="col-md-4">
                    <label for="daterange" class="form-label">{% trans "نطاق التاريخ" %}</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="daterange" name="daterange" value="{{ start_date|date:'Y-m-d' }} - {{ end_date|date:'Y-m-d' }}">
                        <button class="btn btn-outline-secondary" type="button" id="daterange-btn">
                            <i class="fas fa-calendar"></i>
                        </button>
                    </div>
                    <input type="hidden" id="start_date" name="start_date" value="{{ start_date|date:'Y-m-d' }}">
                    <input type="hidden" id="end_date" name="end_date" value="{{ end_date|date:'Y-m-d' }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary d-block">{% trans "تطبيق" %}</button>
                </div>
                <div class="col-md-6">
                    <div class="btn-group float-end mt-4" role="group">
                        <a href="?start_date={{ today|date:'Y-m-d' }}&end_date={{ today|date:'Y-m-d' }}" class="btn btn-outline-secondary">{% trans "اليوم" %}</a>
                        <a href="?start_date={{ week_start|date:'Y-m-d' }}&end_date={{ today|date:'Y-m-d' }}" class="btn btn-outline-secondary">{% trans "هذا الأسبوع" %}</a>
                        <a href="?start_date={{ month_start|date:'Y-m-d' }}&end_date={{ today|date:'Y-m-d' }}" class="btn btn-outline-secondary">{% trans "هذا الشهر" %}</a>
                        <a href="?start_date={{ year_start|date:'Y-m-d' }}&end_date={{ today|date:'Y-m-d' }}" class="btn btn-outline-secondary">{% trans "هذا العام" %}</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Financial Summary Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                {% trans "إجمالي المبيعات" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_sales|floatformat:2 }} {% trans "د.م." %}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                {% trans "إجمالي الإيرادات" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_incomes|floatformat:2 }} {% trans "د.م." %}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                {% trans "إجمالي المصروفات" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_expenses|floatformat:2 }} {% trans "د.م." %}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                {% trans "صافي الربح" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ profit|floatformat:2 }} {% trans "د.م." %}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Income vs Expenses Chart -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "الإيرادات مقابل المصروفات" %}</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="incomeExpensesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profit Pie Chart -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "توزيع الإيرادات" %}</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="profitPieChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Expenses by Category -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "المصروفات حسب الفئة" %}</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="expensesByCategoryTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>{% trans "الفئة" %}</th>
                                    <th>{% trans "المبلغ" %}</th>
                                    <th>{% trans "النسبة" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for expense in expenses_by_category %}
                                <tr>
                                    <td>{{ expense.category__name }}</td>
                                    <td>{{ expense.total|floatformat:2 }} {% trans "د.م." %}</td>
                                    <td>
                                        {% if total_expenses > 0 %}
                                            {{ expense.total|div:total_expenses|mul:100|floatformat:1 }}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="3" class="text-center">{% trans "لا توجد بيانات" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Incomes by Category -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "الإيرادات حسب الفئة" %}</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="incomesByCategoryTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>{% trans "الفئة" %}</th>
                                    <th>{% trans "المبلغ" %}</th>
                                    <th>{% trans "النسبة" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for income in incomes_by_category %}
                                <tr>
                                    <td>{{ income.category__name }}</td>
                                    <td>{{ income.total|floatformat:2 }} {% trans "د.م." %}</td>
                                    <td>
                                        {% if total_incomes > 0 %}
                                            {{ income.total|div:total_incomes|mul:100|floatformat:1 }}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="3" class="text-center">{% trans "لا توجد بيانات" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Expenses Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "قائمة المصروفات" %}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="expensesTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>{% trans "التاريخ" %}</th>
                            <th>{% trans "الوصف" %}</th>
                            <th>{% trans "الفئة" %}</th>
                            <th>{% trans "المبلغ" %}</th>
                            <th>{% trans "طريقة الدفع" %}</th>
                            <th>{% trans "ملاحظات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for expense in expenses %}
                        <tr>
                            <td>{{ expense.date|date:"Y-m-d" }}</td>
                            <td>{{ expense.description }}</td>
                            <td>{{ expense.category.name }}</td>
                            <td>{{ expense.amount|floatformat:2 }} {% trans "د.م." %}</td>
                            <td>{{ expense.get_payment_method_display }}</td>
                            <td>{{ expense.notes|default:"-" }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">{% trans "لا توجد مصروفات" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Incomes Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "قائمة الإيرادات" %}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="incomesTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>{% trans "التاريخ" %}</th>
                            <th>{% trans "الوصف" %}</th>
                            <th>{% trans "الفئة" %}</th>
                            <th>{% trans "المبلغ" %}</th>
                            <th>{% trans "طريقة الدفع" %}</th>
                            <th>{% trans "ملاحظات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for income in incomes %}
                        <tr>
                            <td>{{ income.date|date:"Y-m-d" }}</td>
                            <td>{{ income.description }}</td>
                            <td>{{ income.category.name }}</td>
                            <td>{{ income.amount|floatformat:2 }} {% trans "د.م." %}</td>
                            <td>{{ income.get_payment_method_display }}</td>
                            <td>{{ income.notes|default:"-" }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">{% trans "لا توجد إيرادات" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/moment.min.js' %}"></script>
<script src="{% static 'js/daterangepicker.js' %}"></script>
<script src="{% static 'js/chart.min.js' %}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize DataTables
        $('#expensesTable').DataTable({
            "language": {
                "url": "{% static 'js/dataTables.arabic.json' %}"
            },
            "order": [[0, "desc"]]
        });

        $('#incomesTable').DataTable({
            "language": {
                "url": "{% static 'js/dataTables.arabic.json' %}"
            },
            "order": [[0, "desc"]]
        });

        // Initialize Date Range Picker
        $('#daterange').daterangepicker({
            opens: 'left',
            locale: {
                format: 'YYYY-MM-DD',
                applyLabel: '{% trans "تطبيق" %}',
                cancelLabel: '{% trans "إلغاء" %}',
                fromLabel: '{% trans "من" %}',
                toLabel: '{% trans "إلى" %}',
                customRangeLabel: '{% trans "مخصص" %}',
                daysOfWeek: ['{% trans "أحد" %}', '{% trans "إثنين" %}', '{% trans "ثلاثاء" %}', '{% trans "أربعاء" %}', '{% trans "خميس" %}', '{% trans "جمعة" %}', '{% trans "سبت" %}'],
                monthNames: ['{% trans "يناير" %}', '{% trans "فبراير" %}', '{% trans "مارس" %}', '{% trans "أبريل" %}', '{% trans "مايو" %}', '{% trans "يونيو" %}', '{% trans "يوليو" %}', '{% trans "أغسطس" %}', '{% trans "سبتمبر" %}', '{% trans "أكتوبر" %}', '{% trans "نوفمبر" %}', '{% trans "ديسمبر" %}'],
                firstDay: 0
            }
        }, function(start, end, label) {
            $('#start_date').val(start.format('YYYY-MM-DD'));
            $('#end_date').val(end.format('YYYY-MM-DD'));
        });

        // Income vs Expenses Chart
        const ctx = document.getElementById('incomeExpensesChart').getContext('2d');
        const incomeExpensesChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['{% trans "المبيعات" %}', '{% trans "الإيرادات الأخرى" %}', '{% trans "المصروفات" %}', '{% trans "صافي الربح" %}'],
                datasets: [{
                    label: '{% trans "المبلغ" %}',
                    data: [
                        {{ total_sales }},
                        {{ total_incomes }},
                        {{ total_expenses }},
                        {{ profit }}
                    ],
                    backgroundColor: [
                        'rgba(78, 115, 223, 0.8)',
                        'rgba(28, 200, 138, 0.8)',
                        'rgba(231, 74, 59, 0.8)',
                        'rgba(54, 185, 204, 0.8)'
                    ],
                    borderColor: [
                        'rgba(78, 115, 223, 1)',
                        'rgba(28, 200, 138, 1)',
                        'rgba(231, 74, 59, 1)',
                        'rgba(54, 185, 204, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value + ' {% trans "د.م." %}';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                var label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += context.parsed.y + ' {% trans "د.م." %}';
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });

        // Profit Pie Chart
        const pieCtx = document.getElementById('profitPieChart').getContext('2d');
        const profitPieChart = new Chart(pieCtx, {
            type: 'pie',
            data: {
                labels: ['{% trans "المبيعات" %}', '{% trans "الإيرادات الأخرى" %}', '{% trans "المصروفات" %}'],
                datasets: [{
                    data: [
                        {{ total_sales }},
                        {{ total_incomes }},
                        {{ total_expenses }}
                    ],
                    backgroundColor: [
                        '#4e73df',
                        '#1cc88a',
                        '#e74a3b'
                    ],
                    hoverBackgroundColor: [
                        '#2e59d9',
                        '#17a673',
                        '#be2617'
                    ],
                    hoverBorderColor: "rgba(234, 236, 244, 1)",
                }]
            },
            options: {
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: "rgb(255,255,255)",
                        bodyColor: "#858796",
                        borderColor: '#dddfeb',
                        borderWidth: 1,
                        xPadding: 15,
                        yPadding: 15,
                        displayColors: false,
                        caretPadding: 10,
                        callbacks: {
                            label: function(context) {
                                var label = context.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed !== null) {
                                    label += context.parsed + ' {% trans "د.م." %}';
                                }
                                return label;
                            }
                        }
                    }
                },
                cutout: '70%'
            }
        });
    });
</script>
{% endblock %}
