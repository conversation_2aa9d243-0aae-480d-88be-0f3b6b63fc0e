{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "عرض تفاصيل البيع" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .sale-header {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .sale-info {
        margin-bottom: 0;
    }

    .sale-info dt {
        font-weight: bold;
    }

    .sale-info dd {
        margin-bottom: 0.5rem;
    }

    .status-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }

    .payment-badge {
        font-size: 0.75rem;
    }

    .product-row {
        transition: all 0.2s;
    }

    .product-row:hover {
        background-color: #f8f9fa;
    }

    .product-image-small {
        width: 40px;
        height: 40px;
        object-fit: contain;
        border-radius: 4px;
    }

    .payment-card {
        transition: all 0.3s;
    }

    .payment-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .no-print {
        display: block;
    }

    .print-only {
        display: none;
    }

    @media print {
        .no-print {
            display: none !important;
        }

        .print-only {
            display: block !important;
        }

        body {
            background-color: #fff;
        }

        .container {
            width: 100%;
            max-width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4 no-print">
    <h1 class="h3 mb-0 text-gray-800">{% trans "عرض تفاصيل البيع" %}</h1>
    <div>
        <a href="{% url 'sales:invoice' sale.id %}" class="btn btn-primary me-2">
            <i class="fas fa-file-invoice me-1"></i> {% trans "عرض الفاتورة" %}
        </a>
        <div class="btn-group">
            <button type="button" class="btn btn-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-cog me-1"></i> {% trans "خيارات" %}
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                {% if sale.status != 'cancelled' %}
                <li>
                    <a class="dropdown-item" href="{% url 'sales:edit_sale' sale.id %}">
                        <i class="fas fa-edit me-1"></i> {% trans "تعديل" %}
                    </a>
                </li>
                {% endif %}
                <li>
                    <a class="dropdown-item" href="#" onclick="window.print(); return false;">
                        <i class="fas fa-print me-1"></i> {% trans "طباعة" %}
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="#" id="emailInvoiceBtn">
                        <i class="fas fa-envelope me-1"></i> {% trans "إرسال بالبريد الإلكتروني" %}
                    </a>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                    <a class="dropdown-item text-danger" href="{% url 'sales:delete_sale' sale.id %}">
                        <i class="fas fa-trash me-1"></i> {% trans "حذف" %}
                    </a>
                </li>
            </ul>
        </div>
        <a href="{% url 'sales:index' %}" class="btn btn-light ms-2">
            <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى المبيعات" %}
        </a>
    </div>
</div>

<!-- Print Header -->
<div class="print-only mb-4">
    <div class="text-center">
        <h2>{% trans "فاتورة مبيعات" %}</h2>
        <p>{% trans "نظام إدارة متجر قطع غيار السيارات" %}</p>
    </div>
</div>

{% if messages %}
    <div class="no-print">
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    </div>
{% endif %}

<!-- Sale Header -->
<div class="sale-header">
    <div class="row">
        <div class="col-md-6">
            <dl class="row sale-info">
                <dt class="col-sm-4">{% trans "رقم الفاتورة:" %}</dt>
                <dd class="col-sm-8">{{ sale.invoice_number }}</dd>

                <dt class="col-sm-4">{% trans "العميل:" %}</dt>
                <dd class="col-sm-8">
                    {{ sale.customer.name }}
                    <a href="{% url 'customers:view_customer' sale.customer.id %}" class="ms-2 no-print">
                        <i class="fas fa-external-link-alt"></i>
                    </a>
                </dd>

                <dt class="col-sm-4">{% trans "تاريخ البيع:" %}</dt>
                <dd class="col-sm-8">{{ sale.date|date:"Y-m-d H:i" }}</dd>

                <dt class="col-sm-4">{% trans "الموظف:" %}</dt>
                <dd class="col-sm-8">{{ sale.employee.get_full_name|default:sale.employee.username }}</dd>
            </dl>
        </div>
        <div class="col-md-6">
            <dl class="row sale-info">
                <dt class="col-sm-4">{% trans "الحالة:" %}</dt>
                <dd class="col-sm-8">
                    {% if sale.status == 'completed' %}
                    <span class="badge bg-success status-badge">{% trans "مكتمل" %}</span>
                    {% elif sale.status == 'pending' %}
                    <span class="badge bg-warning status-badge">{% trans "معلق" %}</span>
                    {% elif sale.status == 'cancelled' %}
                    <span class="badge bg-danger status-badge">{% trans "ملغي" %}</span>
                    {% endif %}
                </dd>

                <dt class="col-sm-4">{% trans "طريقة الدفع:" %}</dt>
                <dd class="col-sm-8">
                    {% if sale.payment_method == 'cash' %}
                    <span class="badge bg-success payment-badge">{% trans "نقدي" %}</span>
                    {% elif sale.payment_method == 'card' %}
                    <span class="badge bg-info payment-badge">{% trans "بطاقة ائتمان" %}</span>
                    {% elif sale.payment_method == 'transfer' %}
                    <span class="badge bg-primary payment-badge">{% trans "تحويل بنكي" %}</span>
                    {% elif sale.payment_method == 'check' %}
                    <span class="badge bg-secondary payment-badge">{% trans "شيك" %}</span>
                    {% elif sale.payment_method == 'credit' %}
                    <span class="badge bg-warning payment-badge">{% trans "آجل" %}</span>
                    {% endif %}
                </dd>

                <dt class="col-sm-4">{% trans "تاريخ الإنشاء:" %}</dt>
                <dd class="col-sm-8">{{ sale.created_at|date:"Y-m-d H:i" }}</dd>

                <dt class="col-sm-4">{% trans "آخر تحديث:" %}</dt>
                <dd class="col-sm-8">{{ sale.updated_at|date:"Y-m-d H:i" }}</dd>
            </dl>
        </div>
    </div>

    {% if sale.notes %}
    <div class="mt-3">
        <strong>{% trans "ملاحظات:" %}</strong>
        <p class="mb-0">{{ sale.notes }}</p>
    </div>
    {% endif %}
</div>

<!-- Sale Items -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "المنتجات" %}</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th width="50px">{% trans "صورة" %}</th>
                        <th>{% trans "المنتج" %}</th>
                        <th width="100px">{% trans "الكمية" %}</th>
                        <th width="150px">{% trans "سعر الوحدة" %}</th>
                        <th width="150px">{% trans "المجموع" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in items %}
                    <tr class="product-row">
                        <td>
                            {% if item.product.image %}
                            <img src="{{ item.product.image.url }}" alt="{{ item.product.name }}" class="product-image-small">
                            {% else %}
                            <div class="text-center">
                                <i class="fas fa-box text-muted"></i>
                            </div>
                            {% endif %}
                        </td>
                        <td>
                            <strong>{{ item.product.code }}</strong> - {{ item.product.name }}
                            <div class="small text-muted no-print">
                                <a href="{% url 'inventory:product_detail' item.product.id %}">
                                    <i class="fas fa-external-link-alt me-1"></i> {% trans "عرض المنتج" %}
                                </a>
                            </div>
                        </td>
                        <td>{{ item.quantity }}</td>
                        <td>{{ item.unit_price|floatformat:2 }} د.م</td>
                        <td>{{ item.subtotal|floatformat:2 }} د.م</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="text-center">{% trans "لا توجد منتجات" %}</td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="4" class="text-end"><strong>{% trans "المجموع الفرعي:" %}</strong></td>
                        <td>{{ sale.subtotal|floatformat:2 }} د.م</td>
                    </tr>
                    <tr>
                        <td colspan="4" class="text-end"><strong>{% trans "ضريبة القيمة المضافة (15%):" %}</strong></td>
                        <td>{{ sale.tax_amount|floatformat:2 }} د.م</td>
                    </tr>
                    {% if sale.discount > 0 %}
                    <tr>
                        <td colspan="4" class="text-end"><strong>{% trans "الخصم:" %}</strong></td>
                        <td>{{ sale.discount|floatformat:2 }} د.م</td>
                    </tr>
                    {% endif %}
                    <tr class="table-primary">
                        <td colspan="4" class="text-end"><strong>{% trans "المجموع الكلي:" %}</strong></td>
                        <td><strong>{{ sale.total_amount|floatformat:2 }} د.م</strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>

<!-- Payments -->
<div class="card shadow mb-4 no-print">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "المدفوعات" %}</h6>
        {% if sale.status != 'cancelled' and sale.payment_method == 'credit' %}
        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addPaymentModal">
            <i class="fas fa-plus me-1"></i> {% trans "إضافة دفعة" %}
        </button>
        {% endif %}
    </div>
    <div class="card-body">
        {% if payments %}
        <div class="row">
            {% for payment in payments %}
            <div class="col-md-4 mb-4">
                <div class="card payment-card h-100">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold">{% trans "دفعة" %} #{{ forloop.counter }}</h6>
                    </div>
                    <div class="card-body">
                        <dl class="row mb-0">
                            <dt class="col-sm-5">{% trans "المبلغ:" %}</dt>
                            <dd class="col-sm-7">{{ payment.amount|floatformat:2 }} د.م</dd>

                            <dt class="col-sm-5">{% trans "التاريخ:" %}</dt>
                            <dd class="col-sm-7">{{ payment.date|date:"Y-m-d" }}</dd>

                            <dt class="col-sm-5">{% trans "طريقة الدفع:" %}</dt>
                            <dd class="col-sm-7">
                                {% if payment.payment_method == 'cash' %}
                                <span class="badge bg-success payment-badge">{% trans "نقدي" %}</span>
                                {% elif payment.payment_method == 'card' %}
                                <span class="badge bg-info payment-badge">{% trans "بطاقة ائتمان" %}</span>
                                {% elif payment.payment_method == 'transfer' %}
                                <span class="badge bg-primary payment-badge">{% trans "تحويل بنكي" %}</span>
                                {% elif payment.payment_method == 'check' %}
                                <span class="badge bg-secondary payment-badge">{% trans "شيك" %}</span>
                                {% endif %}
                            </dd>

                            {% if payment.reference_number %}
                            <dt class="col-sm-5">{% trans "رقم المرجع:" %}</dt>
                            <dd class="col-sm-7">{{ payment.reference_number }}</dd>
                            {% endif %}

                            {% if payment.notes %}
                            <dt class="col-sm-5">{% trans "ملاحظات:" %}</dt>
                            <dd class="col-sm-7">{{ payment.notes }}</dd>
                            {% endif %}
                        </dl>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <div class="row mt-3">
            <div class="col-md-6 offset-md-6">
                <div class="card">
                    <div class="card-body">
                        <dl class="row mb-0">
                            <dt class="col-sm-6">{% trans "المبلغ الكلي:" %}</dt>
                            <dd class="col-sm-6">{{ sale.total_amount|floatformat:2 }} د.م</dd>

                            <dt class="col-sm-6">{% trans "المبلغ المدفوع:" %}</dt>
                            <dd class="col-sm-6">{{ total_paid|floatformat:2 }} د.م</dd>

                            <dt class="col-sm-6">{% trans "المبلغ المتبقي:" %}</dt>
                            <dd class="col-sm-6">{{ remaining_amount|floatformat:2 }} د.م</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="text-center">
            <p>{% trans "لا توجد مدفوعات مسجلة" %}</p>
            {% if sale.status != 'cancelled' and sale.payment_method == 'credit' %}
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPaymentModal">
                <i class="fas fa-plus me-1"></i> {% trans "إضافة دفعة" %}
            </button>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Add Payment Modal -->
<div class="modal fade" id="addPaymentModal" tabindex="-1" aria-labelledby="addPaymentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addPaymentModalLabel">{% trans "إضافة دفعة" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'sales:add_payment' sale.id %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="payment_amount" class="form-label required-field">{% trans "المبلغ" %}</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="payment_amount" name="amount" step="0.01" min="0.01" max="{{ remaining_amount }}" value="{{ remaining_amount }}" required>
                            <span class="input-group-text">د.م</span>
                        </div>
                        <div class="form-text">{% trans "المبلغ المتبقي:" %} {{ remaining_amount|floatformat:2 }} د.م</div>
                    </div>
                    <div class="mb-3">
                        <label for="payment_date" class="form-label required-field">{% trans "تاريخ الدفع" %}</label>
                        <input type="date" class="form-control" id="payment_date" name="date" value="{{ today|date:'Y-m-d' }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="payment_method" class="form-label required-field">{% trans "طريقة الدفع" %}</label>
                        <select class="form-select" id="payment_method" name="payment_method" required>
                            <option value="cash">{% trans "نقدي" %}</option>
                            <option value="card">{% trans "بطاقة ائتمان" %}</option>
                            <option value="transfer">{% trans "تحويل بنكي" %}</option>
                            <option value="check">{% trans "شيك" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="reference_number" class="form-label">{% trans "رقم المرجع" %}</label>
                        <input type="text" class="form-control" id="reference_number" name="reference_number">
                        <div class="form-text">{% trans "مطلوب لطرق الدفع: بطاقة ائتمان، تحويل بنكي، شيك" %}</div>
                    </div>
                    <div class="mb-3">
                        <label for="payment_notes" class="form-label">{% trans "ملاحظات" %}</label>
                        <textarea class="form-control" id="payment_notes" name="notes" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "حفظ الدفعة" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Email Invoice Modal -->
<div class="modal fade" id="emailInvoiceModal" tabindex="-1" aria-labelledby="emailInvoiceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="emailInvoiceModalLabel">{% trans "إرسال الفاتورة بالبريد الإلكتروني" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'sales:email_invoice' sale.id %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="email_to" class="form-label required-field">{% trans "البريد الإلكتروني" %}</label>
                        <input type="email" class="form-control" id="email_to" name="email_to" value="{{ sale.customer.email }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="email_subject" class="form-label required-field">{% trans "الموضوع" %}</label>
                        <input type="text" class="form-control" id="email_subject" name="email_subject" value="{% trans 'فاتورة رقم' %} {{ sale.invoice_number }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="email_message" class="form-label">{% trans "الرسالة" %}</label>
                        <textarea class="form-control" id="email_message" name="email_message" rows="4">{% trans "مرفق فاتورة المبيعات الخاصة بكم. شكراً لتعاملكم معنا." %}</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "إرسال" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Print Footer -->
<div class="print-only mt-5">
    <div class="text-center">
        <p>{% trans "شكراً لتعاملكم معنا" %}</p>
        <p>{% trans "تمت الطباعة في" %}: {% now "Y-m-d H:i" %}</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Email Invoice Button
        $('#emailInvoiceBtn').click(function(e) {
            e.preventDefault();
            $('#emailInvoiceModal').modal('show');
        });

        // Payment Method Change
        $('#payment_method').change(function() {
            var method = $(this).val();
            if (method === 'card' || method === 'transfer' || method === 'check') {
                $('#reference_number').attr('required', true);
            } else {
                $('#reference_number').attr('required', false);
            }
        });
    });
</script>
{% endblock %}
