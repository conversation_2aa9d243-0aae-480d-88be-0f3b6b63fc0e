# Generated by Django 5.2 on 2025-04-23 13:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('settings_app', '0005_invoicesetting_auto_send_email_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='invoicesetting',
            name='email_body_template',
            field=models.TextField(blank=True, default='مرحباً {customer_name},\n\nمرفق فاتورة المبيعات الخاصة بكم رقم {invoice_number}.\n\nشكراً لتعاملكم معنا.\n\n{company_name}', null=True, verbose_name='قالب نص البريد'),
        ),
        migrations.AlterField(
            model_name='invoicesetting',
            name='email_subject_template',
            field=models.CharField(blank=True, default='فاتورة رقم {invoice_number}', max_length=255, null=True, verbose_name='قالب عنوان البريد'),
        ),
    ]
