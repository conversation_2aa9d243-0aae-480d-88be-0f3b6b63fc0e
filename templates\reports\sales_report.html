{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "تقرير المبيعات" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/daterangepicker.css' %}">
<style>
    .chart-container {
        position: relative;
        height: 300px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "تقرير المبيعات" %}</h1>
        <div>
            <a href="{% url 'reports:index' %}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i> {% trans "العودة" %}
            </a>
            <div class="btn-group">
                <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-download me-1"></i> {% trans "تصدير" %}
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="{% url 'reports:export_sales_report' %}?format=pdf&start_date={{ start_date|date:'Y-m-d' }}&end_date={{ end_date|date:'Y-m-d' }}">PDF</a></li>
                    <li><a class="dropdown-item" href="{% url 'reports:export_sales_report' %}?format=excel&start_date={{ start_date|date:'Y-m-d' }}&end_date={{ end_date|date:'Y-m-d' }}">Excel</a></li>
                    <li><a class="dropdown-item" href="{% url 'reports:export_sales_report' %}?format=csv&start_date={{ start_date|date:'Y-m-d' }}&end_date={{ end_date|date:'Y-m-d' }}">CSV</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "خيارات التصفية" %}</h6>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <!-- Date Range Filter -->
                <div class="col-md-4 mb-3">
                    <label for="daterange" class="form-label">{% trans "نطاق التاريخ" %}</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="daterange" name="daterange" value="{{ start_date|date:'Y-m-d' }} - {{ end_date|date:'Y-m-d' }}">
                        <button class="btn btn-outline-secondary" type="button" id="daterange-btn">
                            <i class="fas fa-calendar"></i>
                        </button>
                    </div>
                    <input type="hidden" id="start_date" name="start_date" value="{{ start_date|date:'Y-m-d' }}">
                    <input type="hidden" id="end_date" name="end_date" value="{{ end_date|date:'Y-m-d' }}">
                </div>

                <!-- Category Filter -->
                <div class="col-md-4 mb-3">
                    <label for="category" class="form-label">{% trans "فئة المنتج" %}</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">{% trans "جميع الفئات" %}</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if selected_category == category.id %}selected{% endif %}>{{ category.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Product Filter -->
                <div class="col-md-4 mb-3">
                    <label for="product" class="form-label">{% trans "المنتج" %}</label>
                    <select class="form-select" id="product" name="product">
                        <option value="">{% trans "جميع المنتجات" %}</option>
                        {% for product in all_products %}
                        <option value="{{ product.id }}" {% if selected_product == product.id %}selected{% endif %}>{{ product.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Customer Filter -->
                <div class="col-md-4 mb-3">
                    <label for="customer" class="form-label">{% trans "العميل" %}</label>
                    <select class="form-select" id="customer" name="customer">
                        <option value="">{% trans "جميع العملاء" %}</option>
                        {% for customer in all_customers %}
                        <option value="{{ customer.id }}" {% if selected_customer == customer.id %}selected{% endif %}>{{ customer.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Payment Method Filter -->
                <div class="col-md-4 mb-3">
                    <label for="payment_method" class="form-label">{% trans "طريقة الدفع" %}</label>
                    <select class="form-select" id="payment_method" name="payment_method">
                        <option value="">{% trans "جميع طرق الدفع" %}</option>
                        <option value="cash" {% if selected_payment_method == 'cash' %}selected{% endif %}>{% trans "نقدي" %}</option>
                        <option value="card" {% if selected_payment_method == 'card' %}selected{% endif %}>{% trans "بطاقة ائتمان" %}</option>
                        <option value="transfer" {% if selected_payment_method == 'transfer' %}selected{% endif %}>{% trans "تحويل بنكي" %}</option>
                        <option value="check" {% if selected_payment_method == 'check' %}selected{% endif %}>{% trans "شيك" %}</option>
                    </select>
                </div>

                <!-- Apply Filters Button -->
                <div class="col-md-4 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-1"></i> {% trans "تطبيق التصفية" %}
                    </button>
                </div>

                <!-- Quick Date Filters -->
                <div class="col-12 mt-2">
                    <div class="btn-group" role="group">
                        <a href="?start_date={{ today|date:'Y-m-d' }}&end_date={{ today|date:'Y-m-d' }}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_product %}&product={{ selected_product }}{% endif %}{% if selected_customer %}&customer={{ selected_customer }}{% endif %}{% if selected_payment_method %}&payment_method={{ selected_payment_method }}{% endif %}" class="btn btn-outline-secondary btn-sm">
                            {% trans "اليوم" %}
                        </a>
                        <a href="?start_date={{ week_start|date:'Y-m-d' }}&end_date={{ today|date:'Y-m-d' }}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_product %}&product={{ selected_product }}{% endif %}{% if selected_customer %}&customer={{ selected_customer }}{% endif %}{% if selected_payment_method %}&payment_method={{ selected_payment_method }}{% endif %}" class="btn btn-outline-secondary btn-sm">
                            {% trans "هذا الأسبوع" %}
                        </a>
                        <a href="?start_date={{ month_start|date:'Y-m-d' }}&end_date={{ today|date:'Y-m-d' }}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_product %}&product={{ selected_product }}{% endif %}{% if selected_customer %}&customer={{ selected_customer }}{% endif %}{% if selected_payment_method %}&payment_method={{ selected_payment_method }}{% endif %}" class="btn btn-outline-secondary btn-sm">
                            {% trans "هذا الشهر" %}
                        </a>
                        <a href="?start_date={{ year_start|date:'Y-m-d' }}&end_date={{ today|date:'Y-m-d' }}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_product %}&product={{ selected_product }}{% endif %}{% if selected_customer %}&customer={{ selected_customer }}{% endif %}{% if selected_payment_method %}&payment_method={{ selected_payment_method }}{% endif %}" class="btn btn-outline-secondary btn-sm">
                            {% trans "هذا العام" %}
                        </a>
                        <a href="?" class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-times me-1"></i> {% trans "إعادة ضبط" %}
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Sales Summary Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                {% trans "إجمالي المبيعات" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_sales }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                {% trans "إجمالي المبلغ" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_amount|floatformat:2 }} {% trans "د.م." %}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                {% trans "متوسط قيمة البيع" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% if total_sales > 0 %}
                                    {{ total_amount|floatformat:2|default:0|stringformat:"s"|slice:":-3" }}
                                {% else %}
                                    0
                                {% endif %} {% trans "د.م." %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calculator fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                {% trans "عدد المنتجات المباعة" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% if top_products %}
                                    {{ top_products.total_quantity__sum|default:0 }}
                                {% else %}
                                    0
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-box fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Sales Chart -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "المبيعات حسب اليوم" %}</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="salesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Products -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "أفضل المنتجات مبيعًا" %}</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-sm" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>{% trans "المنتج" %}</th>
                                    <th>{% trans "الكمية" %}</th>
                                    <th>{% trans "المبلغ" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in top_products %}
                                <tr>
                                    <td>{{ product.product__name }}</td>
                                    <td>{{ product.total_quantity }}</td>
                                    <td>{{ product.total_sales|floatformat:2 }} {% trans "د.م." %}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="3" class="text-center">{% trans "لا توجد بيانات" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sales Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "قائمة المبيعات" %}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="salesTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>{% trans "رقم الفاتورة" %}</th>
                            <th>{% trans "العميل" %}</th>
                            <th>{% trans "التاريخ" %}</th>
                            <th>{% trans "المبلغ" %}</th>
                            <th>{% trans "الضريبة" %}</th>
                            <th>{% trans "الإجمالي" %}</th>
                            <th>{% trans "الموظف" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for sale in sales %}
                        <tr>
                            <td>{{ sale.invoice_number }}</td>
                            <td>{{ sale.customer.name }}</td>
                            <td>{{ sale.date|date:"Y-m-d H:i" }}</td>
                            <td>{{ sale.subtotal|floatformat:2 }}</td>
                            <td>{{ sale.tax_amount|floatformat:2 }}</td>
                            <td>{{ sale.total_amount|floatformat:2 }}</td>
                            <td>{{ sale.employee.get_full_name }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center">{% trans "لا توجد مبيعات" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/moment.min.js' %}"></script>
<script src="{% static 'js/daterangepicker.js' %}"></script>
<script src="{% static 'js/chart.min.js' %}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize DataTable
        $('#salesTable').DataTable({
            "language": {
                "url": "{% static 'js/dataTables.arabic.json' %}"
            },
            "order": [[2, "desc"]]
        });

        // Initialize Date Range Picker
        $('#daterange').daterangepicker({
            opens: 'left',
            locale: {
                format: 'YYYY-MM-DD',
                applyLabel: '{% trans "تطبيق" %}',
                cancelLabel: '{% trans "إلغاء" %}',
                fromLabel: '{% trans "من" %}',
                toLabel: '{% trans "إلى" %}',
                customRangeLabel: '{% trans "مخصص" %}',
                daysOfWeek: ['{% trans "أحد" %}', '{% trans "إثنين" %}', '{% trans "ثلاثاء" %}', '{% trans "أربعاء" %}', '{% trans "خميس" %}', '{% trans "جمعة" %}', '{% trans "سبت" %}'],
                monthNames: ['{% trans "يناير" %}', '{% trans "فبراير" %}', '{% trans "مارس" %}', '{% trans "أبريل" %}', '{% trans "مايو" %}', '{% trans "يونيو" %}', '{% trans "يوليو" %}', '{% trans "أغسطس" %}', '{% trans "سبتمبر" %}', '{% trans "أكتوبر" %}', '{% trans "نوفمبر" %}', '{% trans "ديسمبر" %}'],
                firstDay: 0
            }
        }, function(start, end, label) {
            $('#start_date').val(start.format('YYYY-MM-DD'));
            $('#end_date').val(end.format('YYYY-MM-DD'));
        });

        // Sales Chart
        const ctx = document.getElementById('salesChart').getContext('2d');
        const salesChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [
                    {% for item in sales_by_day %}
                        '{{ item.date__date|date:"Y-m-d" }}',
                    {% endfor %}
                ],
                datasets: [{
                    label: '{% trans "المبيعات" %}',
                    data: [
                        {% for item in sales_by_day %}
                            {{ item.total }},
                        {% endfor %}
                    ],
                    backgroundColor: 'rgba(78, 115, 223, 0.05)',
                    borderColor: 'rgba(78, 115, 223, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(78, 115, 223, 1)',
                    pointBorderColor: '#fff',
                    pointHoverRadius: 5,
                    pointHoverBackgroundColor: 'rgba(78, 115, 223, 1)',
                    pointHoverBorderColor: '#fff',
                    pointHitRadius: 10,
                    pointBorderWidth: 2,
                    tension: 0.3
                }]
            },
            options: {
                maintainAspectRatio: false,
                layout: {
                    padding: {
                        left: 10,
                        right: 25,
                        top: 25,
                        bottom: 0
                    }
                },
                scales: {
                    x: {
                        time: {
                            unit: 'day'
                        },
                        grid: {
                            display: false,
                            drawBorder: false
                        },
                        ticks: {
                            maxTicksLimit: 7
                        }
                    },
                    y: {
                        ticks: {
                            maxTicksLimit: 5,
                            padding: 10,
                            callback: function(value, index, values) {
                                return value + ' {% trans "د.م." %}';
                            }
                        },
                        grid: {
                            color: "rgb(234, 236, 244)",
                            zeroLineColor: "rgb(234, 236, 244)",
                            drawBorder: false,
                            borderDash: [2],
                            zeroLineBorderDash: [2]
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: "rgb(255,255,255)",
                        bodyColor: "#858796",
                        titleMarginBottom: 10,
                        titleColor: '#6e707e',
                        titleFontSize: 14,
                        borderColor: '#dddfeb',
                        borderWidth: 1,
                        xPadding: 15,
                        yPadding: 15,
                        displayColors: false,
                        intersect: false,
                        mode: 'index',
                        caretPadding: 10,
                        callbacks: {
                            label: function(context) {
                                var label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += context.parsed.y + ' {% trans "د.م." %}';
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });
    });
</script>
{% endblock %}
