{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "إضافة مصروف جديد" %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card shadow">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0">{% trans "إضافة مصروف جديد" %}</h5>
        </div>
        <div class="card-body">
            <form method="post" action="{% url 'finance:add_expense' %}">
                {% csrf_token %}
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="date">{% trans "التاريخ" %} <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="date" name="date" value="{{ today|date:'Y-m-d' }}" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="amount">{% trans "المبلغ" %} <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" step="0.01" min="0.01" class="form-control" id="amount" name="amount" required>
                                <div class="input-group-append">
                                    <span class="input-group-text">{% trans "د.م" %}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="category">{% trans "الفئة" %} <span class="text-danger">*</span></label>
                            <select class="form-control" id="category" name="category" required>
                                <option value="">{% trans "اختر فئة المصروف" %}</option>
                                {% for category in expense_categories %}
                                <option value="{{ category.id }}">{{ category.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="account">{% trans "الحساب" %} <span class="text-danger">*</span></label>
                            <select class="form-control" id="account" name="account" required>
                                <option value="">{% trans "اختر الحساب" %}</option>
                                {% for account in accounts %}
                                <option value="{{ account.id }}">{{ account.name }} ({{ account.current_balance }} {% trans "د.م" %})</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="form-group mb-3">
                    <label for="description">{% trans "الوصف" %}</label>
                    <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                </div>
                
                <div class="form-group mb-3">
                    <label for="reference">{% trans "المرجع" %}</label>
                    <input type="text" class="form-control" id="reference" name="reference" placeholder="{% trans 'رقم الفاتورة أو أي مرجع آخر' %}">
                </div>
                
                <div class="form-group mt-4">
                    <button type="submit" class="btn btn-danger">{% trans "حفظ المصروف" %}</button>
                    <a href="{% url 'finance:expenses' %}" class="btn btn-secondary">{% trans "إلغاء" %}</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // تركيز تلقائي على حقل المبلغ
        $('#amount').focus();
    });
</script>
{% endblock %}
