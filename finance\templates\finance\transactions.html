{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "المعاملات المالية" %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card shadow">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{% trans "المعاملات المالية" %}</h5>
        </div>
        
        <!-- فلاتر البحث -->
        <div class="card-body border-bottom">
            <form method="get" action="{% url 'finance:transactions' %}" class="row g-3">
                <div class="col-md-2">
                    <label for="type" class="form-label">{% trans "النوع" %}</label>
                    <select class="form-select" id="type" name="type">
                        <option value="">{% trans "الكل" %}</option>
                        <option value="sale" {% if transaction_type == 'sale' %}selected{% endif %}>{% trans "مبيعات" %}</option>
                        <option value="purchase" {% if transaction_type == 'purchase' %}selected{% endif %}>{% trans "مشتريات" %}</option>
                        <option value="expense" {% if transaction_type == 'expense' %}selected{% endif %}>{% trans "مصروفات" %}</option>
                        <option value="income" {% if transaction_type == 'income' %}selected{% endif %}>{% trans "إيرادات" %}</option>
                        <option value="transfer" {% if transaction_type == 'transfer' %}selected{% endif %}>{% trans "تحويل" %}</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">{% trans "الحالة" %}</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">{% trans "الكل" %}</option>
                        <option value="paid" {% if payment_status == 'paid' %}selected{% endif %}>{% trans "مدفوعة" %}</option>
                        <option value="unpaid" {% if payment_status == 'unpaid' %}selected{% endif %}>{% trans "غير مدفوعة" %}</option>
                        <option value="partial" {% if payment_status == 'partial' %}selected{% endif %}>{% trans "مدفوعة جزئياً" %}</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label">{% trans "من تاريخ" %}</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">{% trans "إلى تاريخ" %}</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                </div>
                <div class="col-md-3">
                    <label for="q" class="form-label">{% trans "بحث" %}</label>
                    <input type="text" class="form-control" id="q" name="q" value="{{ search_query }}" placeholder="{% trans 'رقم المعاملة أو الوصف' %}">
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">{% trans "بحث" %}</button>
                </div>
            </form>
        </div>
        
        <!-- إحصائيات المعاملات -->
        <div class="card-body border-bottom bg-light">
            <div class="row text-center">
                <div class="col-md-3">
                    <h6>{% trans "إجمالي المعاملات" %}</h6>
                    <h4>{{ total_amount }} {% trans "د.م" %}</h4>
                </div>
                <div class="col-md-3">
                    <h6>{% trans "المبلغ المدفوع" %}</h6>
                    <h4 class="text-success">{{ paid_amount }} {% trans "د.م" %}</h4>
                </div>
                <div class="col-md-3">
                    <h6>{% trans "المبلغ المتبقي" %}</h6>
                    <h4 class="text-danger">{{ remaining_amount }} {% trans "د.م" %}</h4>
                </div>
                <div class="col-md-3">
                    <h6>{% trans "عدد المعاملات" %}</h6>
                    <div class="d-flex justify-content-center">
                        <span class="badge bg-success mx-1">{{ paid_count }}</span>
                        <span class="badge bg-warning mx-1">{{ partial_count }}</span>
                        <span class="badge bg-danger mx-1">{{ unpaid_count }}</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- جدول المعاملات -->
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>{% trans "رقم المعاملة" %}</th>
                            <th>{% trans "التاريخ" %}</th>
                            <th>{% trans "النوع" %}</th>
                            <th>{% trans "الوصف" %}</th>
                            <th>{% trans "المبلغ" %}</th>
                            <th>{% trans "المدفوع" %}</th>
                            <th>{% trans "المتبقي" %}</th>
                            <th>{% trans "الحالة" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for transaction in transactions %}
                        <tr>
                            <td>{{ transaction.transaction_number }}</td>
                            <td>{{ transaction.date }}</td>
                            <td>{{ transaction.get_transaction_type_display }}</td>
                            <td>{{ transaction.description|default:"-"|truncatechars:30 }}</td>
                            <td>{{ transaction.amount }} {% trans "د.م" %}</td>
                            <td>{{ transaction.paid_amount }} {% trans "د.م" %}</td>
                            <td>{{ transaction.remaining_amount }} {% trans "د.م" %}</td>
                            <td>
                                {% if transaction.payment_status == 'paid' %}
                                <span class="badge bg-success">{% trans "مدفوعة" %}</span>
                                {% elif transaction.payment_status == 'partial' %}
                                <span class="badge bg-warning">{% trans "جزئية" %}</span>
                                {% else %}
                                <span class="badge bg-danger">{% trans "غير مدفوعة" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'finance:transaction_detail' transaction.id %}" class="btn btn-outline-primary" title="{% trans 'عرض التفاصيل' %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if transaction.payment_status != 'paid' %}
                                    <a href="{% url 'finance:add_payment_to_transaction' transaction.id %}" class="btn btn-outline-success" title="{% trans 'إضافة دفعة' %}">
                                        <i class="fas fa-money-bill"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="9" class="text-center">{% trans "لا توجد معاملات مطابقة لمعايير البحث" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- ترقيم الصفحات -->
            {% if transactions.has_other_pages %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center mt-4">
                    {% if transactions.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if transaction_type %}&type={{ transaction_type }}{% endif %}{% if payment_status %}&status={{ payment_status }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ transactions.previous_page_number }}{% if transaction_type %}&type={{ transaction_type }}{% endif %}{% if payment_status %}&status={{ payment_status }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for i in transactions.paginator.page_range %}
                        {% if transactions.number == i %}
                        <li class="page-item active"><span class="page-link">{{ i }}</span></li>
                        {% elif i > transactions.number|add:'-3' and i < transactions.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ i }}{% if transaction_type %}&type={{ transaction_type }}{% endif %}{% if payment_status %}&status={{ payment_status }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}">{{ i }}</a>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if transactions.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ transactions.next_page_number }}{% if transaction_type %}&type={{ transaction_type }}{% endif %}{% if payment_status %}&status={{ payment_status }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ transactions.paginator.num_pages }}{% if transaction_type %}&type={{ transaction_type }}{% endif %}{% if payment_status %}&status={{ payment_status }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
