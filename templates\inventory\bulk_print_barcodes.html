{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "طباعة دفعة باركود" %}{% endblock %}

{% block styles %}
<style>
    .barcode-preview {
        border: 1px dashed #ccc;
        padding: 10px;
        margin-bottom: 15px;
        text-align: center;
    }
    .barcode-settings {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
    }
    .barcode-item {
        border: 1px solid #eee;
        border-radius: 5px;
        padding: 10px;
        margin-bottom: 10px;
    }
    .barcode-item.selected {
        border-color: #007bff;
        background-color: rgba(0, 123, 255, 0.05);
    }
    .print-options {
        border-top: 1px solid #eee;
        padding-top: 15px;
        margin-top: 15px;
    }
    .selected-products-list {
        max-height: 250px;
        overflow-y: auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-print me-2"></i>{% trans "طباعة دفعة باركود" %}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- نموذج اختيار المنتجات -->
                        <div class="col-md-5">
                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">{% trans "اختيار المنتجات" %}</h6>
                                </div>
                                <div class="card-body">
                                    <form id="product-selection-form">
                                        <!-- البحث عن المنتجات -->
                                        <div class="mb-3">
                                            <label for="product-search" class="form-label">{% trans "البحث عن المنتجات" %}</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="product-search" placeholder="{% trans 'اسم المنتج أو الرمز...' %}">
                                                <button class="btn btn-outline-secondary" type="button" id="search-btn">
                                                    <i class="fas fa-search"></i>
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <!-- اختيار الفئة -->
                                        <div class="mb-3">
                                            <label for="category-filter" class="form-label">{% trans "تصفية حسب الفئة" %}</label>
                                            <select class="form-select" id="category-filter">
                                                <option value="">{% trans "جميع الفئات" %}</option>
                                                {% for category in categories %}
                                                <option value="{{ category.id }}">{{ category.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        
                                        <!-- قائمة المنتجات -->
                                        <div class="mb-3">
                                            <label class="form-label">{% trans "المنتجات المتاحة" %}</label>
                                            <div class="product-list border rounded p-2" style="height: 250px; overflow-y: auto;">
                                                <div class="list-group" id="available-products">
                                                    {% for product in products %}
                                                    <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <strong>{{ product.name }}</strong>
                                                            <small class="d-block text-muted">{{ product.code }}</small>
                                                        </div>
                                                        <button type="button" class="btn btn-sm btn-outline-primary add-product" data-product-id="{{ product.id }}">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                    </div>
                                                    {% empty %}
                                                    <div class="text-center py-3 text-muted">
                                                        <i class="fas fa-box fa-2x mb-2"></i>
                                                        <p>{% trans "لا توجد منتجات متاحة" %}</p>
                                                    </div>
                                                    {% endfor %}
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <!-- المنتجات المختارة وإعدادات الطباعة -->
                        <div class="col-md-7">
                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">{% trans "المنتجات المختارة" %}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="selected-products-list border rounded p-2 mb-3" style="height: 200px; overflow-y: auto;">
                                        <div class="list-group" id="selected-products">
                                            <!-- ستتم إضافة المنتجات المختارة هنا بواسطة JavaScript -->
                                            <div class="text-center py-3 text-muted empty-selection">
                                                <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                                                <p>{% trans "لم يتم اختيار أي منتجات بعد" %}</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- إعدادات الطباعة -->
                                    <div class="print-settings barcode-settings">
                                        <h6 class="border-bottom pb-2">{% trans "إعدادات الطباعة" %}</h6>
                                        
                                        <div class="row mb-3">
                                            <!-- نوع الباركود -->
                                            <div class="col-md-6">
                                                <label for="barcode-type" class="form-label">{% trans "نوع الباركود" %}</label>
                                                <select class="form-select" id="barcode-type">
                                                    {% for type in barcode_types %}
                                                    <option value="{{ type.id }}">{{ type.name }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                            
                                            <!-- حجم الباركود -->
                                            <div class="col-md-6">
                                                <label for="barcode-size" class="form-label">{% trans "حجم الباركود" %}</label>
                                                <div class="input-group">
                                                    <input type="number" class="form-control" id="barcode-width" value="2" min="1" max="5" step="0.5" placeholder="العرض">
                                                    <span class="input-group-text">×</span>
                                                    <input type="number" class="form-control" id="barcode-height" value="50" min="20" max="100" placeholder="الارتفاع">
                                                </div>
                                                <small class="text-muted">{% trans "العرض × الارتفاع (مم)" %}</small>
                                            </div>
                                        </div>
                                        
                                        <div class="row mb-3">
                                            <!-- عدد النسخ -->
                                            <div class="col-md-6">
                                                <label for="copies" class="form-label">{% trans "عدد النسخ لكل منتج" %}</label>
                                                <input type="number" class="form-control" id="copies" min="1" value="1">
                                            </div>
                                            
                                            <!-- تنسيق الطباعة -->
                                            <div class="col-md-6">
                                                <label for="print-format" class="form-label">{% trans "تنسيق الطباعة" %}</label>
                                                <select class="form-select" id="print-format">
                                                    <option value="labels">{% trans "ملصقات" %}</option>
                                                    <option value="sheet">{% trans "صفحة كاملة" %}</option>
                                                    <option value="receipt">{% trans "إيصال" %}</option>
                                                    <option value="labels-2x7">{% trans "ملصقات (2×7)" %}</option>
                                                    <option value="labels-3x8">{% trans "ملصقات (3×8)" %}</option>
                                                    <option value="custom">{% trans "تخطيط مخصص" %}</option>
                                                </select>
                                            </div>
                                        </div>
                                        
                                        <div class="row mb-3">
                                            <!-- خيارات إضافية -->
                                            <div class="col-12">
                                                <label class="form-label">{% trans "خيارات إضافية" %}</label>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="include-price" checked>
                                                    <label class="form-check-label" for="include-price">
                                                        {% trans "إظهار السعر" %}
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="include-name" checked>
                                                    <label class="form-check-label" for="include-name">
                                                        {% trans "إظهار اسم المنتج" %}
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="include-code">
                                                    <label class="form-check-label" for="include-code">
                                                        {% trans "إظهار رمز المنتج" %}
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="show-barcode-text" checked>
                                                    <label class="form-check-label" for="show-barcode-text">
                                                        {% trans "إظهار رقم الباركود" %}
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- معاينة وطباعة -->
                                    <div class="d-flex justify-content-between mt-3">
                                        <button type="button" class="btn btn-outline-secondary" id="preview-btn">
                                            <i class="fas fa-eye me-1"></i> {% trans "معاينة" %}
                                        </button>
                                        <button type="button" class="btn btn-primary" id="print-btn">
                                            <i class="fas fa-print me-1"></i> {% trans "طباعة" %}
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- معاينة الباركود -->
                            <div class="card" id="preview-card" style="display: none;">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">{% trans "معاينة الباركود" %}</h6>
                                </div>
                                <div class="card-body text-center">
                                    <div id="barcode-preview" class="p-3 border rounded">
                                        <!-- ستتم إضافة معاينة الباركود هنا بواسطة JavaScript -->
                                        <img src="{% static 'img/barcode-sample.png' %}" alt="معاينة الباركود" class="img-fluid">
                                        <div class="mt-2">
                                            <strong>اسم المنتج</strong>
                                            <div>12345678</div>
                                            <div class="mt-1">99.99 ريال</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج الطباعة (مخفي) -->
<div id="print-container" style="display: none;">
    <!-- سيتم إنشاء محتوى الطباعة هنا بواسطة JavaScript -->
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // البحث عن المنتجات
        $('#search-btn').click(function() {
            const searchTerm = $('#product-search').val();
            // هنا يمكن إضافة كود AJAX للبحث عن المنتجات
            alert('البحث عن: ' + searchTerm);
        });
        
        // تصفية حسب الفئة
        $('#category-filter').change(function() {
            const categoryId = $(this).val();
            // هنا يمكن إضافة كود AJAX لتصفية المنتجات حسب الفئة
            alert('تصفية حسب الفئة: ' + categoryId);
        });
        
        // إضافة منتج إلى القائمة المختارة
        $('.add-product').click(function() {
            const productId = $(this).data('product-id');
            const productElement = $(this).closest('.list-group-item');
            const productName = productElement.find('strong').text();
            const productCode = productElement.find('small').text();
            
            // إزالة رسالة "لم يتم اختيار أي منتجات بعد"
            $('.empty-selection').hide();
            
            // إضافة المنتج إلى القائمة المختارة
            const selectedProductHtml = `
                <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" data-product-id="${productId}">
                    <div>
                        <strong>${productName}</strong>
                        <small class="d-block text-muted">${productCode}</small>
                    </div>
                    <div class="d-flex align-items-center">
                        <input type="number" class="form-control form-control-sm me-2" style="width: 60px;" value="1" min="1">
                        <button type="button" class="btn btn-sm btn-outline-danger remove-product">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;
            $('#selected-products').append(selectedProductHtml);
        });
        
        // إزالة منتج من القائمة المختارة
        $(document).on('click', '.remove-product', function() {
            $(this).closest('.list-group-item').remove();
            
            // إظهار رسالة "لم يتم اختيار أي منتجات بعد" إذا كانت القائمة فارغة
            if ($('#selected-products .list-group-item').length === 0) {
                $('.empty-selection').show();
            }
        });
        
        // معاينة الباركود
        $('#preview-btn').click(function() {
            // التحقق من وجود منتجات مختارة
            if ($('#selected-products .list-group-item').length === 0) {
                alert('الرجاء اختيار منتج واحد على الأقل للمعاينة');
                return;
            }
            
            // إظهار بطاقة المعاينة
            $('#preview-card').show();
            
            // هنا يمكن إضافة كود لتحديث معاينة الباركود بناءً على الإعدادات المختارة
        });
        
        // طباعة الباركود
        $('#print-btn').click(function() {
            // التحقق من وجود منتجات مختارة
            if ($('#selected-products .list-group-item').length === 0) {
                alert('الرجاء اختيار منتج واحد على الأقل للطباعة');
                return;
            }
            
            // جمع معلومات الطباعة
            const printInfo = {
                barcodeType: $('#barcode-type').val(),
                barcodeSize: $('#barcode-size').val(),
                copies: $('#copies').val(),
                printFormat: $('#print-format').val(),
                includePrice: $('#include-price').is(':checked'),
                includeName: $('#include-name').is(':checked'),
                includeCode: $('#include-code').is(':checked'),
                products: []
            };
            
            // جمع المنتجات المختارة
            $('#selected-products .list-group-item').each(function() {
                const productId = $(this).data('product-id');
                const quantity = $(this).find('input[type="number"]').val();
                
                printInfo.products.push({
                    id: productId,
                    quantity: quantity
                });
            });
            
            // هنا يمكن إضافة كود AJAX لإرسال معلومات الطباعة إلى الخادم
            console.log('معلومات الطباعة:', printInfo);
            alert('تم إرسال طلب الطباعة!');
        });
    });
</script>
{% endblock %}