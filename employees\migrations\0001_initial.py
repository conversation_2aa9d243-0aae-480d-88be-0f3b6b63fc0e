# Generated by Django 5.2 on 2025-04-18 16:07

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'قسم',
                'verbose_name_plural': 'الأقسام',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('employee_id', models.CharField(max_length=20, unique=True, verbose_name='رقم الموظف')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('date_of_birth', models.DateField(blank=True, null=True, verbose_name='تاريخ الميلاد')),
                ('gender', models.CharField(choices=[('male', 'ذكر'), ('female', 'أنثى')], max_length=10, verbose_name='الجنس')),
                ('national_id', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهوية')),
                ('hire_date', models.DateField(verbose_name='تاريخ التوظيف')),
                ('salary', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الراتب')),
                ('image', models.ImageField(blank=True, null=True, upload_to='employees/', verbose_name='الصورة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='employee_profile', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'ملف الموظف',
                'verbose_name_plural': 'ملفات الموظفين',
                'ordering': ['user__first_name', 'user__last_name'],
            },
        ),
        migrations.CreateModel(
            name='Leave',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('leave_type', models.CharField(choices=[('annual', 'سنوية'), ('sick', 'مرضية'), ('emergency', 'طارئة'), ('unpaid', 'غير مدفوعة'), ('other', 'أخرى')], max_length=20, verbose_name='نوع الإجازة')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ النهاية')),
                ('reason', models.TextField(verbose_name='السبب')),
                ('status', models.CharField(choices=[('pending', 'معلق'), ('approved', 'مقبولة'), ('rejected', 'مرفوضة'), ('cancelled', 'ملغاة')], default='pending', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_leaves', to=settings.AUTH_USER_MODEL, verbose_name='تمت الموافقة من قبل')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leaves', to='employees.employeeprofile', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'إجازة',
                'verbose_name_plural': 'الإجازات',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='Position',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='positions', to='employees.department', verbose_name='القسم')),
            ],
            options={
                'verbose_name': 'منصب',
                'verbose_name_plural': 'المناصب',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='employeeprofile',
            name='position',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='employees', to='employees.position', verbose_name='المنصب'),
        ),
        migrations.CreateModel(
            name='Attendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('status', models.CharField(choices=[('present', 'حاضر'), ('absent', 'غائب'), ('late', 'متأخر'), ('half_day', 'نصف يوم'), ('leave', 'إجازة')], max_length=20, verbose_name='الحالة')),
                ('check_in', models.TimeField(blank=True, null=True, verbose_name='وقت الحضور')),
                ('check_out', models.TimeField(blank=True, null=True, verbose_name='وقت الانصراف')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance', to='employees.employeeprofile', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'حضور',
                'verbose_name_plural': 'الحضور',
                'ordering': ['-date'],
                'unique_together': {('employee', 'date')},
            },
        ),
    ]
