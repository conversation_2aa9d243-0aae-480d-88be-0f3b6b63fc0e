# Generated by Django 5.2 on 2025-04-18 16:07

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Account',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('account_type', models.CharField(choices=[('cash', 'نقدي'), ('bank', 'حساب بنكي'), ('credit_card', 'بطاقة ائتمان'), ('other', 'أخرى')], max_length=20, verbose_name='نوع الحساب')),
                ('account_number', models.Char<PERSON>ield(blank=True, max_length=50, null=True, verbose_name='رقم الحساب')),
                ('bank_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='اسم البنك')),
                ('initial_balance', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الرصيد الافتتاحي')),
                ('current_balance', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الرصيد الحالي')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'حساب',
                'verbose_name_plural': 'الحسابات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ExpenseCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'فئة المصروفات',
                'verbose_name_plural': 'فئات المصروفات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='IncomeCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'فئة الإيرادات',
                'verbose_name_plural': 'فئات الإيرادات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='AccountTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='تاريخ التحويل')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('reference', models.CharField(blank=True, max_length=100, null=True, verbose_name='المرجع')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers', to=settings.AUTH_USER_MODEL, verbose_name='الموظف')),
                ('from_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers_from', to='finance.account', verbose_name='من حساب')),
                ('to_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers_to', to='finance.account', verbose_name='إلى حساب')),
            ],
            options={
                'verbose_name': 'تحويل بين الحسابات',
                'verbose_name_plural': 'تحويلات بين الحسابات',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='Expense',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='تاريخ المصروف')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('payment_method', models.CharField(choices=[('cash', 'نقدي'), ('bank_transfer', 'تحويل بنكي'), ('check', 'شيك'), ('credit_card', 'بطاقة ائتمان')], max_length=20, verbose_name='طريقة الدفع')),
                ('reference', models.CharField(blank=True, max_length=100, null=True, verbose_name='المرجع')),
                ('receipt', models.FileField(blank=True, null=True, upload_to='receipts/', verbose_name='الإيصال')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='expenses', to='finance.account', verbose_name='الحساب')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='expenses', to=settings.AUTH_USER_MODEL, verbose_name='الموظف')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='expenses', to='finance.expensecategory', verbose_name='الفئة')),
            ],
            options={
                'verbose_name': 'مصروف',
                'verbose_name_plural': 'المصروفات',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='Income',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='تاريخ الإيراد')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('payment_method', models.CharField(choices=[('cash', 'نقدي'), ('bank_transfer', 'تحويل بنكي'), ('check', 'شيك'), ('credit_card', 'بطاقة ائتمان')], max_length=20, verbose_name='طريقة الدفع')),
                ('reference', models.CharField(blank=True, max_length=100, null=True, verbose_name='المرجع')),
                ('receipt', models.FileField(blank=True, null=True, upload_to='receipts/', verbose_name='الإيصال')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='incomes', to='finance.account', verbose_name='الحساب')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='incomes', to=settings.AUTH_USER_MODEL, verbose_name='الموظف')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='incomes', to='finance.incomecategory', verbose_name='الفئة')),
            ],
            options={
                'verbose_name': 'إيراد',
                'verbose_name_plural': 'الإيرادات',
                'ordering': ['-date'],
            },
        ),
    ]
