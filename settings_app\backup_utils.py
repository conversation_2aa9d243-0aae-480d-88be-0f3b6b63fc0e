import os
import datetime
import shutil
import zipfile
import json
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from .models import BackupLog, BackupSetting
from .encryption_utils import encrypt_file, decrypt_file, calculate_checksum, verify_file_integrity
from .cloud_storage import get_storage_provider

def create_backup(backup_type='manual', include_media=True, encrypt=None, notes=None):
    """
    إنشاء نسخة احتياطية من قاعدة البيانات والملفات

    Args:
        backup_type (str): نوع النسخ الاحتياطي ('manual' أو 'auto')
        include_media (bool): تضمين ملفات الوسائط
        encrypt (bool): تشفير النسخة الاحتياطية
        notes (str): ملاحظات إضافية

    Returns:
        BackupLog: سجل النسخ الاحتياطي
    """
    try:
        # الحصول على إعدادات النسخ الاحتياطي
        backup_setting = BackupSetting.objects.first()

        # إذا لم يتم تحديد التشفير، استخدم الإعداد الافتراضي
        if encrypt is None and backup_setting:
            encrypt = backup_setting.encrypt_backups
        elif encrypt is None:
            encrypt = False

        # إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجودًا
        backup_dir = os.path.join(settings.BASE_DIR, 'backups')
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)

        # إنشاء اسم الملف بناءً على التاريخ والوقت
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f"backup_{timestamp}.zip"
        backup_path = os.path.join(backup_dir, backup_filename)

        # إنشاء ملف الضغط
        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # إضافة ملف قاعدة البيانات
            db_file = settings.DATABASES['default']['NAME']
            if os.path.exists(db_file):
                zipf.write(db_file, os.path.basename(db_file))

            # إضافة ملفات الوسائط إذا تم تحديد ذلك
            if include_media and os.path.exists(settings.MEDIA_ROOT):
                for root, dirs, files in os.walk(settings.MEDIA_ROOT):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, settings.BASE_DIR)
                        zipf.write(file_path, arcname)

            # إضافة ملف المعلومات
            info = {
                'timestamp': timestamp,
                'include_media': include_media,
                'encrypted': encrypt,
                'django_version': settings.DJANGO_VERSION if hasattr(settings, 'DJANGO_VERSION') else 'unknown',
                'backup_type': backup_type,
                'created_by': 'Backup System',
            }
            zipf.writestr('backup_info.json', json.dumps(info, indent=4))

        # حساب الـ checksum للملف
        checksum = calculate_checksum(backup_path)

        # تشفير الملف إذا تم تحديد ذلك
        encrypted_path = None
        encryption_key = None
        if encrypt:
            from .encryption_utils import generate_key
            encryption_key = generate_key()
            encrypted_path = encrypt_file(backup_path, encryption_key)
            # استخدام الملف المشفر بدلاً من الملف الأصلي
            if os.path.exists(encrypted_path):
                # حذف الملف الأصلي
                os.remove(backup_path)
                backup_path = encrypted_path
                backup_filename = os.path.basename(encrypted_path)

        # حساب حجم الملف
        file_size = os.path.getsize(backup_path)

        # رفع الملف إلى التخزين السحابي إذا تم تكوينه
        cloud_info = None
        if backup_setting and backup_setting.storage_type != 'local':
            storage_provider = get_storage_provider()
            if storage_provider:
                cloud_path = backup_setting.storage_path or 'backups'
                cloud_info = storage_provider.upload_file(backup_path, f"{cloud_path}/{backup_filename}")

        # إنشاء سجل النسخ الاحتياطي
        backup_log = BackupLog.objects.create(
            file_name=backup_filename,
            file_path=backup_path,
            file_size=file_size,
            backup_type=backup_type,
            status='success',
            notes=notes,
            checksum=checksum,
            is_encrypted=encrypt,
            encryption_key=encryption_key,
            cloud_storage_type=backup_setting.storage_type if backup_setting else 'local',
            cloud_storage_id=cloud_info.get('id') if cloud_info else None,
            cloud_storage_link=cloud_info.get('link') if cloud_info else None
        )

        return backup_log

    except Exception as e:
        # تسجيل الخطأ
        error_message = str(e)
        backup_log = BackupLog.objects.create(
            file_name=f"failed_backup_{timestamp}.zip" if 'timestamp' in locals() else "failed_backup.zip",
            file_path="",
            file_size=0,
            backup_type=backup_type,
            status='failed',
            notes=f"خطأ: {error_message}"
        )

        return backup_log

def restore_backup(backup_log_id, partial=False, selected_items=None):
    """
    استعادة نسخة احتياطية

    Args:
        backup_log_id (int): معرف سجل النسخ الاحتياطي
        partial (bool): استعادة جزئية (عناصر محددة فقط)
        selected_items (list): قائمة العناصر المحددة للاستعادة الجزئية

    Returns:
        bool: نجاح أو فشل عملية الاستعادة
    """
    try:
        backup_log = BackupLog.objects.get(id=backup_log_id)

        # التحقق من وجود ملف النسخة الاحتياطية أو تنزيله من التخزين السحابي
        backup_file_path = backup_log.file_path
        temp_file_path = None

        # إذا كان الملف مخزنًا في السحابة
        if backup_log.cloud_storage_id and not os.path.exists(backup_file_path):
            storage_provider = get_storage_provider()
            if storage_provider:
                # تنزيل الملف من التخزين السحابي
                temp_dir = os.path.join(settings.BASE_DIR, 'temp')
                os.makedirs(temp_dir, exist_ok=True)
                temp_file_path = os.path.join(temp_dir, backup_log.file_name)
                downloaded_path = storage_provider.download_file(backup_log.cloud_storage_id, temp_file_path)
                if downloaded_path:
                    backup_file_path = downloaded_path
                else:
                    return False

        # التحقق من وجود الملف
        if not os.path.exists(backup_file_path):
            return False

        # التحقق من سلامة الملف إذا كان لدينا checksum
        if backup_log.checksum and not verify_file_integrity(backup_file_path, backup_log.checksum):
            if temp_file_path and os.path.exists(temp_file_path):
                os.remove(temp_file_path)
            return False

        # فك تشفير الملف إذا كان مشفرًا
        decrypted_path = None
        if backup_log.is_encrypted and backup_log.encryption_key:
            temp_dir = os.path.join(settings.BASE_DIR, 'temp')
            os.makedirs(temp_dir, exist_ok=True)
            decrypted_path = os.path.join(temp_dir, f"decrypted_{backup_log.file_name}")
            try:
                decrypt_file(backup_file_path, backup_log.encryption_key, decrypted_path)
                backup_file_path = decrypted_path
            except Exception as e:
                print(f"خطأ في فك تشفير الملف: {str(e)}")
                if temp_file_path and os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
                if decrypted_path and os.path.exists(decrypted_path):
                    os.remove(decrypted_path)
                return False

        # استخراج ملفات النسخة الاحتياطية
        with zipfile.ZipFile(backup_file_path, 'r') as zipf:
            # قراءة ملف المعلومات إذا كان موجودًا
            backup_info = {}
            try:
                if 'backup_info.json' in zipf.namelist():
                    backup_info = json.loads(zipf.read('backup_info.json').decode('utf-8'))
            except Exception:
                pass

            # استخراج ملف قاعدة البيانات
            db_file = None
            for file_info in zipf.infolist():
                if file_info.filename.endswith('.sqlite3'):
                    db_file = file_info.filename
                    break

            # استعادة قاعدة البيانات إذا لم تكن استعادة جزئية أو إذا تم تحديد قاعدة البيانات
            if db_file and (not partial or 'database' in selected_items):
                # نسخ قاعدة البيانات الحالية كنسخة احتياطية قبل الاستعادة
                current_db = settings.DATABASES['default']['NAME']
                backup_current_db = f"{current_db}.bak"
                shutil.copy2(current_db, backup_current_db)

                # استخراج ملف قاعدة البيانات
                zipf.extract(db_file, settings.BASE_DIR)
                extracted_db = os.path.join(settings.BASE_DIR, db_file)

                # استبدال قاعدة البيانات الحالية بالنسخة المستعادة
                shutil.copy2(extracted_db, current_db)
                os.remove(extracted_db)

            # استخراج ملفات الوسائط
            if not partial or 'media' in selected_items:
                for file_info in zipf.infolist():
                    if file_info.filename.startswith('media/'):
                        zipf.extract(file_info, settings.BASE_DIR)

            # استعادة جزئية لجداول محددة
            if partial and selected_items:
                # هنا يمكن إضافة منطق لاستعادة جداول محددة من قاعدة البيانات
                # مثل استعادة جدول العملاء فقط أو جدول المنتجات فقط
                pass

        # تنظيف الملفات المؤقتة
        if temp_file_path and os.path.exists(temp_file_path):
            os.remove(temp_file_path)
        if decrypted_path and os.path.exists(decrypted_path):
            os.remove(decrypted_path)

        # تسجيل عملية الاستعادة
        from .models import RestoreLog
        RestoreLog.objects.create(
            backup_log=backup_log,
            status='success',
            is_partial=partial,
            restored_items=json.dumps(selected_items) if selected_items else None,
            notes=f"تمت استعادة النسخة الاحتياطية بنجاح"
        )

        return True

    except Exception as e:
        # تسجيل الخطأ
        error_message = str(e)
        print(f"خطأ أثناء استعادة النسخة الاحتياطية: {error_message}")

        try:
            from .models import RestoreLog
            RestoreLog.objects.create(
                backup_log_id=backup_log_id,
                status='failed',
                is_partial=partial,
                restored_items=json.dumps(selected_items) if selected_items else None,
                notes=f"خطأ: {error_message}"
            )
        except Exception:
            pass

        return False

def delete_backup(backup_log_id):
    """
    حذف نسخة احتياطية

    Args:
        backup_log_id (int): معرف سجل النسخ الاحتياطي

    Returns:
        bool: نجاح أو فشل عملية الحذف
    """
    try:
        backup_log = BackupLog.objects.get(id=backup_log_id)

        # حذف ملف النسخة الاحتياطية من التخزين المحلي إذا كان موجودًا
        if backup_log.file_path and os.path.exists(backup_log.file_path):
            os.remove(backup_log.file_path)

        # حذف الملف من التخزين السحابي إذا كان مخزنًا هناك
        if backup_log.cloud_storage_id and backup_log.cloud_storage_type != 'local':
            storage_provider = get_storage_provider()
            if storage_provider:
                storage_provider.delete_file(backup_log.cloud_storage_id)

        # تسجيل عملية الحذف
        from .models import DeleteLog
        DeleteLog.objects.create(
            backup_filename=backup_log.file_name,
            backup_type=backup_log.backup_type,
            backup_date=backup_log.created_at,
            status='success',
            notes=f"تم حذف النسخة الاحتياطية بنجاح"
        )

        # حذف سجل النسخ الاحتياطي
        backup_log.delete()

        return True

    except Exception as e:
        # تسجيل الخطأ
        error_message = str(e)
        print(f"خطأ أثناء حذف النسخة الاحتياطية: {error_message}")

        try:
            from .models import DeleteLog
            DeleteLog.objects.create(
                backup_filename=backup_log.file_name if 'backup_log' in locals() else f"unknown_{backup_log_id}",
                backup_type="unknown",
                status='failed',
                notes=f"خطأ: {error_message}"
            )
        except Exception:
            pass

        return False

def cleanup_old_backups(days=30, keep_min=5):
    """
    حذف النسخ الاحتياطية القديمة

    Args:
        days (int): عدد الأيام للاحتفاظ بالنسخ الاحتياطية
        keep_min (int): الحد الأدنى لعدد النسخ الاحتياطية للاحتفاظ بها

    Returns:
        int: عدد النسخ الاحتياطية التي تم حذفها
    """
    try:
        # التحقق من عدد النسخ الاحتياطية الموجودة
        total_backups = BackupLog.objects.count()

        # إذا كان عدد النسخ الاحتياطية أقل من الحد الأدنى، لا تحذف شيئًا
        if total_backups <= keep_min:
            return 0

        # حساب التاريخ الأقدم للاحتفاظ بالنسخ الاحتياطية
        cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days)

        # الحصول على النسخ الاحتياطية القديمة
        old_backups = BackupLog.objects.filter(created_at__lt=cutoff_date).order_by('created_at')

        # التأكد من أن عدد النسخ المتبقية لن يقل عن الحد الأدنى
        backups_to_keep = total_backups - old_backups.count()
        if backups_to_keep < keep_min:
            # تقليل عدد النسخ القديمة التي سيتم حذفها
            limit = total_backups - keep_min
            old_backups = old_backups[:limit]

        count = len(old_backups)
        deleted_count = 0

        # حذف النسخ القديمة
        for backup in old_backups:
            try:
                # حذف ملف النسخة الاحتياطية من التخزين المحلي
                if backup.file_path and os.path.exists(backup.file_path):
                    os.remove(backup.file_path)

                # حذف الملف من التخزين السحابي
                if backup.cloud_storage_id and backup.cloud_storage_type != 'local':
                    storage_provider = get_storage_provider()
                    if storage_provider:
                        storage_provider.delete_file(backup.cloud_storage_id)

                # تسجيل عملية الحذف
                from .models import DeleteLog
                DeleteLog.objects.create(
                    backup_filename=backup.file_name,
                    backup_type=backup.backup_type,
                    backup_date=backup.created_at,
                    status='success',
                    notes=f"تم حذف النسخة الاحتياطية القديمة بنجاح"
                )

                # حذف سجل النسخ الاحتياطي
                backup.delete()
                deleted_count += 1

            except Exception as e:
                print(f"خطأ أثناء حذف النسخة الاحتياطية {backup.file_name}: {str(e)}")

        return deleted_count

    except Exception as e:
        error_message = str(e)
        print(f"خطأ أثناء تنظيف النسخ الاحتياطية القديمة: {error_message}")

        try:
            from .models import DeleteLog
            DeleteLog.objects.create(
                backup_filename="multiple_old_backups",
                backup_type="cleanup",
                status='failed',
                notes=f"خطأ أثناء تنظيف النسخ القديمة: {error_message}"
            )
        except Exception:
            pass

        return 0
