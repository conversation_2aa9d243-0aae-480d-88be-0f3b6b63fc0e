from django.urls import path, include
from . import views as main_views

urlpatterns = [
    path('', main_views.index, name='index'),
    path('add/', main_views.add_product, name='add_product'),
    path('edit/<int:product_id>/', main_views.edit_product, name='edit_product'),
    path('delete/<int:product_id>/', main_views.delete_product, name='delete_product'),
    path('product/<int:product_id>/', main_views.product_detail, name='product_detail'),
    path('product/<int:product_id>/print-barcode/', main_views.print_barcode, name='print_barcode'),
    path('product/<int:product_id>/movements/', main_views.get_product_movements, name='get_product_movements'),
    path('product/<int:product_id>/details/', main_views.product_details_json, name='product_details_json'),
    path('product/<int:product_id>/add-movement/', main_views.add_movement, name='add_movement'),

    path('categories/', main_views.categories, name='categories'),
    path('categories/add/', main_views.add_category, name='add_category'),
    path('categories/edit/<int:category_id>/', main_views.edit_category, name='edit_category'),
    path('categories/delete/<int:category_id>/', main_views.delete_category, name='delete_category'),
    path('categories/add-ajax/', main_views.add_category_ajax, name='add_category_ajax'),

    path('storage-locations/', main_views.storage_locations, name='storage_locations'),
    path('storage-locations/add/', main_views.add_storage_location, name='add_storage_location'),
    path('storage-locations/edit/<int:location_id>/', main_views.edit_storage_location, name='edit_storage_location'),
    path('storage-locations/delete/<int:location_id>/', main_views.delete_storage_location, name='delete_storage_location'),
    path('storage-locations/add-ajax/', main_views.add_storage_location_ajax, name='add_storage_location_ajax'),

    path('search/', main_views.search_products, name='search_products'),
    path('export/', main_views.export_products, name='export_products'),
    path('export-template/', main_views.export_template, name='export_template'),
    path('import/', main_views.import_products, name='import_products'),
    path('bulk-delete/', main_views.bulk_delete, name='bulk_delete'),
    path('stock-movement/add/', main_views.stock_movement_ajax, name='stock_movement_ajax'),
    path('alerts/', main_views.stock_alerts, name='stock_alerts'),
    path('create-purchase-order/', main_views.create_purchase_order, name='create_purchase_order'),
    path('select-products-for-barcode/', main_views.select_products_for_barcode, name='select_products_for_barcode'),

    # إضافة مسارات الباركود
    path('barcode/', include('inventory.barcode_urls')),
]
