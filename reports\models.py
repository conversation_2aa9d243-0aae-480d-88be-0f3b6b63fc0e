from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.models import User

class SavedReport(models.Model):
    REPORT_TYPE_CHOICES = (
        ('sales', _('المبيعات')),
        ('inventory', _('المخزون')),
        ('customers', _('العملاء')),
        ('purchases', _('المشتريات')),
        ('financial', _('المالية')),
        ('employees', _('الموظفين')),
        ('custom', _('مخصص')),
    )

    name = models.CharField(_('الاسم'), max_length=100)
    report_type = models.CharField(_('نوع التقرير'), max_length=20, choices=REPORT_TYPE_CHOICES)
    parameters = models.JSONField(_('المعاملات'), blank=True, null=True)
    description = models.TextField(_('الوصف'), blank=True, null=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='saved_reports', verbose_name=_('تم الإنشاء بواسطة'))
    is_public = models.BooleanField(_('عام'), default=False)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('تقرير محفوظ')
        verbose_name_plural = _('التقارير المحفوظة')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.get_report_type_display()})"

class ScheduledReport(models.Model):
    FREQUENCY_CHOICES = (
        ('daily', _('يومي')),
        ('weekly', _('أسبوعي')),
        ('monthly', _('شهري')),
        ('quarterly', _('ربع سنوي')),
    )

    saved_report = models.ForeignKey(SavedReport, on_delete=models.CASCADE, related_name='schedules', verbose_name=_('التقرير المحفوظ'))
    frequency = models.CharField(_('التكرار'), max_length=20, choices=FREQUENCY_CHOICES)
    recipients = models.TextField(_('المستلمون'), help_text=_('قائمة بريد إلكتروني مفصولة بفواصل'))
    subject = models.CharField(_('الموضوع'), max_length=200)
    message = models.TextField(_('الرسالة'), blank=True, null=True)
    is_active = models.BooleanField(_('نشط'), default=True)
    last_sent = models.DateTimeField(_('آخر إرسال'), blank=True, null=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='scheduled_reports', verbose_name=_('تم الإنشاء بواسطة'))
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('تقرير مجدول')
        verbose_name_plural = _('التقارير المجدولة')
        ordering = ['saved_report__name']

    def __str__(self):
        return f"{self.saved_report.name} ({self.get_frequency_display()})"

class ReportExport(models.Model):
    FORMAT_CHOICES = (
        ('pdf', 'PDF'),
        ('excel', 'Excel'),
        ('csv', 'CSV'),
    )

    report_type = models.CharField(_('نوع التقرير'), max_length=20, choices=SavedReport.REPORT_TYPE_CHOICES)
    parameters = models.JSONField(_('المعاملات'), blank=True, null=True)
    format = models.CharField(_('التنسيق'), max_length=10, choices=FORMAT_CHOICES)
    file = models.FileField(_('الملف'), upload_to='reports/')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='report_exports', verbose_name=_('تم الإنشاء بواسطة'))
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)

    class Meta:
        verbose_name = _('تصدير التقرير')
        verbose_name_plural = _('تصديرات التقارير')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.get_report_type_display()} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"
