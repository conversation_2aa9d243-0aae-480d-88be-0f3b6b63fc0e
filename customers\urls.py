from django.urls import path
from . import views

app_name = 'customers'

urlpatterns = [
    # صفحات العملاء الرئيسية
    path('', views.index, name='index'),
    path('add/', views.add_customer, name='add_customer'),
    path('edit/<int:customer_id>/', views.edit_customer, name='edit_customer'),
    path('view/<int:customer_id>/', views.view_customer, name='view_customer'),
    path('delete/<int:customer_id>/', views.delete_customer, name='delete_customer'),

    # مسارات المركبات
    path('vehicle/add/<int:customer_id>/', views.add_vehicle, name='add_vehicle'),
    path('vehicle/delete/<int:vehicle_id>/', views.delete_vehicle, name='delete_vehicle'),

    # مسارات التفاعلات
    path('interaction/add/<int:customer_id>/', views.add_interaction, name='add_interaction'),

    # مسارات الاستيراد والتصدير
    path('import/', views.import_customers, name='import_customers'),
    path('export/', views.export_customers, name='export_customers'),
]
