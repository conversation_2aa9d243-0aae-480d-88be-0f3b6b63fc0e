# Generated by Django 5.2 on 2025-04-18 16:07

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CarType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('origin', models.CharField(choices=[('american', 'أمريكي'), ('european', 'أوروبي'), ('japanese', 'ياباني'), ('korean', 'كوري'), ('chinese', 'صيني'), ('other', 'أخرى')], max_length=20, verbose_name='المنشأ')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'نوع سيارة',
                'verbose_name_plural': 'أنواع السيارات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'فئة',
                'verbose_name_plural': 'الفئات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='الكود')),
                ('name', models.CharField(max_length=200, verbose_name='الاسم')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('purchase_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الشراء')),
                ('selling_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر البيع')),
                ('quantity', models.PositiveIntegerField(verbose_name='الكمية')),
                ('min_quantity', models.PositiveIntegerField(default=1, verbose_name='الحد الأدنى للكمية')),
                ('image', models.ImageField(blank=True, null=True, upload_to='products/', verbose_name='الصورة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('car_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='inventory.cartype', verbose_name='نوع السيارة')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='inventory.category', verbose_name='الفئة')),
            ],
            options={
                'verbose_name': 'منتج',
                'verbose_name_plural': 'المنتجات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ProductMovement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('movement_type', models.CharField(choices=[('in', 'وارد'), ('out', 'صادر'), ('adjustment', 'تعديل')], max_length=20, verbose_name='نوع الحركة')),
                ('quantity', models.PositiveIntegerField(verbose_name='الكمية')),
                ('reference', models.CharField(blank=True, max_length=100, null=True, verbose_name='المرجع')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='movements', to='inventory.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'حركة المنتج',
                'verbose_name_plural': 'حركات المنتجات',
                'ordering': ['-created_at'],
            },
        ),
    ]
