{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/settings.css' %}">
{% endblock %}

{% block title %}{% trans "إعدادات الإشعارات" %} | {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "إعدادات الإشعارات" %}</h1>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "إعدادات الإشعارات" %}</h6>
        </div>
        <div class="card-body">
            <form method="post" action="{% url 'settings_app:notification_settings' %}">
                {% csrf_token %}
                
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="notificationsTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>{% trans "نوع الإشعار" %}</th>
                                <th>{% trans "إشعار بريد إلكتروني" %}</th>
                                <th>{% trans "إشعار نظام" %}</th>
                                <th>{% trans "المستلمون" %}</th>
                                <th>{% trans "قالب البريد" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for notification in notifications %}
                            <tr>
                                <td>{{ notification.get_event_display }}</td>
                                <td>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="email_{{ notification.id }}" name="email_{{ notification.id }}" {% if notification.email_notification %}checked{% endif %}>
                                    </div>
                                </td>
                                <td>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="system_{{ notification.id }}" name="system_{{ notification.id }}" {% if notification.system_notification %}checked{% endif %}>
                                    </div>
                                </td>
                                <td>
                                    <input type="text" class="form-control form-control-sm" id="recipients_{{ notification.id }}" name="recipients_{{ notification.id }}" value="{{ notification.recipients|default:'' }}" placeholder="{% trans 'بريد إلكتروني مفصول بفواصل' %}">
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-outline-primary edit-template" data-id="{{ notification.id }}" data-event="{{ notification.get_event_display }}" data-template="{{ notification.email_template|default:'' }}">
                                        <i class="fas fa-edit me-1"></i> {% trans "تحرير القالب" %}
                                    </button>
                                    <input type="hidden" id="template_{{ notification.id }}" name="template_{{ notification.id }}" value="{{ notification.email_template|default:'' }}">
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center">{% trans "لا توجد إشعارات مضافة" %}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> {% trans "حفظ الإعدادات" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "أنواع الإشعارات" %}</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>{% trans "نوع الإشعار" %}</th>
                                    <th>{% trans "الوصف" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>{% trans "مخزون منخفض" %}</td>
                                    <td>{% trans "إشعارات عندما ينخفض مخزون منتج عن الحد الأدنى المحدد." %}</td>
                                </tr>
                                <tr>
                                    <td>{% trans "طلب جديد" %}</td>
                                    <td>{% trans "إشعارات عند إنشاء طلب جديد في النظام." %}</td>
                                </tr>
                                <tr>
                                    <td>{% trans "تم استلام الدفع" %}</td>
                                    <td>{% trans "إشعارات عند تسجيل دفعة جديدة في النظام." %}</td>
                                </tr>
                                <tr>
                                    <td>{% trans "تغيير حالة الطلب" %}</td>
                                    <td>{% trans "إشعارات عند تغيير حالة طلب (مثل: قيد التنفيذ، تم الشحن، تم التسليم)." %}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "معلومات مساعدة" %}</h6>
                </div>
                <div class="card-body">
                    <h5 class="text-gray-800 mb-3">{% trans "حول إعدادات الإشعارات" %}</h5>
                    <p>{% trans "تتيح لك إعدادات الإشعارات تحديد كيفية إرسال الإشعارات المختلفة في النظام." %}</p>
                    
                    <h6 class="text-gray-800 mb-2">{% trans "إشعارات البريد الإلكتروني" %}</h6>
                    <p>{% trans "عند تفعيل إشعارات البريد الإلكتروني، سيتم إرسال بريد إلكتروني إلى المستلمين المحددين عند حدوث الحدث المعني." %}</p>
                    
                    <h6 class="text-gray-800 mb-2">{% trans "إشعارات النظام" %}</h6>
                    <p>{% trans "عند تفعيل إشعارات النظام، سيتم عرض إشعار داخل النظام للمستخدمين المعنيين عند حدوث الحدث المعني." %}</p>
                    
                    <h6 class="text-gray-800 mb-2">{% trans "المستلمون" %}</h6>
                    <p>{% trans "يمكنك تحديد عناوين البريد الإلكتروني للمستلمين مفصولة بفواصل. إذا تركت هذا الحقل فارغًا، سيتم إرسال الإشعارات إلى المستخدمين المعنيين فقط." %}</p>
                    
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-1"></i> {% trans "ملاحظة: يجب تكوين إعدادات البريد الإلكتروني بشكل صحيح لتلقي إشعارات البريد الإلكتروني." %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Template Modal -->
<div class="modal fade" id="editTemplateModal" tabindex="-1" aria-labelledby="editTemplateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editTemplateModalLabel">{% trans "تحرير قالب البريد الإلكتروني" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="template_event" class="form-label">{% trans "نوع الإشعار" %}</label>
                    <input type="text" class="form-control" id="template_event" readonly>
                </div>
                <div class="mb-3">
                    <label for="template_content" class="form-label">{% trans "محتوى القالب" %}</label>
                    <textarea class="form-control" id="template_content" rows="10"></textarea>
                    <div class="form-text">
                        {% trans "يمكنك استخدام العلامات التالية في القالب:" %}<br>
                        <code>{name}</code> - {% trans "اسم المستلم" %}<br>
                        <code>{company}</code> - {% trans "اسم الشركة" %}<br>
                        <code>{date}</code> - {% trans "تاريخ الإشعار" %}<br>
                        <code>{details}</code> - {% trans "تفاصيل الإشعار" %}
                    </div>
                </div>
                <input type="hidden" id="template_id">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-primary" id="saveTemplateBtn">{% trans "حفظ القالب" %}</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Edit Template
        const editTemplateModal = new bootstrap.Modal(document.getElementById('editTemplateModal'));
        const templateEvent = document.getElementById('template_event');
        const templateContent = document.getElementById('template_content');
        const templateId = document.getElementById('template_id');
        const saveTemplateBtn = document.getElementById('saveTemplateBtn');
        
        document.querySelectorAll('.edit-template').forEach(function(button) {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const event = this.getAttribute('data-event');
                const template = this.getAttribute('data-template');
                
                templateEvent.value = event;
                templateContent.value = template || getDefaultTemplate(event);
                templateId.value = id;
                
                editTemplateModal.show();
            });
        });
        
        saveTemplateBtn.addEventListener('click', function() {
            const id = templateId.value;
            const template = templateContent.value;
            
            document.getElementById('template_' + id).value = template;
            
            editTemplateModal.hide();
        });
        
        // Get default template based on event type
        function getDefaultTemplate(event) {
            let template = '';
            
            if (event === 'مخزون منخفض') {
                template = `عزيزي {name}،

نود إعلامك أن المنتج "{details}" قد وصل إلى مستوى المخزون المنخفض.
الكمية المتبقية: {quantity}

يرجى التحقق من المخزون وإجراء طلب شراء جديد إذا لزم الأمر.

مع أطيب التحيات،
{company}`;
            } else if (event === 'طلب جديد') {
                template = `عزيزي {name}،

نود إعلامك أنه تم إنشاء طلب جديد في النظام.
رقم الطلب: {order_number}
العميل: {customer}
المبلغ الإجمالي: {total}

يمكنك عرض تفاصيل الطلب من خلال النظام.

مع أطيب التحيات،
{company}`;
            } else if (event === 'تم استلام الدفع') {
                template = `عزيزي {name}،

نود إعلامك أنه تم استلام دفعة جديدة.
رقم الفاتورة: {invoice_number}
المبلغ: {amount}
طريقة الدفع: {payment_method}
التاريخ: {date}

مع أطيب التحيات،
{company}`;
            } else if (event === 'تغيير حالة الطلب') {
                template = `عزيزي {name}،

نود إعلامك أنه تم تغيير حالة الطلب الخاص بك.
رقم الطلب: {order_number}
الحالة الجديدة: {status}

{additional_info}

مع أطيب التحيات،
{company}`;
            }
            
            return template;
        }
    });
</script>
{% endblock %}
