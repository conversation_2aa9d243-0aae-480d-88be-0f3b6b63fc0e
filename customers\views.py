import csv
import io
import xlwt
from datetime import datetime

from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse, HttpResponse
from django.db.models import Q, Count, Sum, Max
from django.core.paginator import Paginator
from django.views.decorators.http import require_POST
from django.template.loader import render_to_string

from .models import Customer, CustomerCategory, CustomerVehicle, CustomerInteraction, CustomerImport
from .forms import CustomerForm, CustomerVehicleForm, CustomerInteractionForm, CustomerImportForm, CustomerSearchForm, CustomerExportForm
from sales.models import Sale

@login_required
def index(request):
    # إنشاء نموذج البحث
    search_form = CustomerSearchForm(request.GET or None)

    # الحصول على جميع العملاء مع الإحصائيات
    customers = Customer.objects.all().select_related('category')

    # تطبيق عوامل التصفية إذا تم تقديم النموذج
    if search_form.is_valid():
        search_query = search_form.cleaned_data.get('search')
        category = search_form.cleaned_data.get('category')
        is_active = search_form.cleaned_data.get('is_active')
        has_purchases = search_form.cleaned_data.get('has_purchases')
        city = search_form.cleaned_data.get('city')

        # تصفية حسب البحث
        if search_query:
            customers = customers.filter(
                Q(name__icontains=search_query) |
                Q(phone__icontains=search_query) |
                Q(email__icontains=search_query)
            )

        # تصفية حسب الفئة
        if category:
            customers = customers.filter(category=category)

        # تصفية حسب الحالة
        if is_active == '1':
            customers = customers.filter(is_active=True)
        elif is_active == '0':
            customers = customers.filter(is_active=False)

        # تصفية حسب المدينة
        if city:
            customers = customers.filter(city__icontains=city)

        # تصفية حسب وجود مشتريات
        if has_purchases == '1':
            customers = customers.annotate(sales_count=Count('sales')).filter(sales_count__gt=0)
        elif has_purchases == '0':
            customers = customers.annotate(sales_count=Count('sales')).filter(sales_count=0)

    # ترتيب العملاء
    sort_by = request.GET.get('sort_by', 'name')
    sort_order = request.GET.get('sort_order', 'asc')

    if sort_by == 'name':
        order_field = 'name'
    elif sort_by == 'created_at':
        order_field = 'created_at'
    elif sort_by == 'last_purchase':
        order_field = 'last_purchase_date'
    else:
        order_field = 'name'

    if sort_order == 'desc':
        order_field = f'-{order_field}'

    customers = customers.order_by(order_field)

    # تقسيم الصفحات
    paginator = Paginator(customers, 10)  # 10 عملاء في كل صفحة
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # الإحصائيات
    total_customers = Customer.objects.count()
    active_customers = Customer.objects.filter(is_active=True).count()
    inactive_customers = total_customers - active_customers

    # العملاء الأكثر شراءً
    top_customers = Customer.objects.annotate(
        purchase_count=Count('sales'),
        total_spent=Sum('sales__total_amount')
    ).filter(purchase_count__gt=0).order_by('-total_spent')[:5]

    # الفئات
    categories = CustomerCategory.objects.all()

    context = {
        'page_obj': page_obj,
        'customers': page_obj,
        'categories': categories,
        'search_form': search_form,
        'total_customers': total_customers,
        'active_customers': active_customers,
        'inactive_customers': inactive_customers,
        'top_customers': top_customers,
        'sort_by': sort_by,
        'sort_order': sort_order,
    }

    # التعامل مع طلبات AJAX
    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        html = render_to_string(
            template_name='customers/partials/customer_list.html',
            context=context
        )
        return JsonResponse({'html': html})

    return render(request, 'customers/index.html', context)

@login_required
def add_customer(request):
    if request.method == 'POST':
        form = CustomerForm(request.POST)
        if form.is_valid():
            customer = form.save()
            messages.success(request, _('تم إضافة العميل بنجاح'))

            # إذا كان الطلب من نافذة منبثقة
            if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': True,
                    'customer_id': customer.id,
                    'customer_name': customer.name
                })

            # إذا تم تقديم النموذج مع البقاء في الصفحة
            if 'save_and_add_another' in request.POST:
                return redirect('customers:add_customer')

            # إذا تم تقديم النموذج مع الانتقال إلى صفحة التفاصيل
            if 'save_and_view' in request.POST:
                return redirect('customers:view_customer', customer_id=customer.id)

            # العودة إلى قائمة العملاء
            return redirect('customers:index')
        else:
            # إذا كان الطلب من نافذة منبثقة
            if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': False,
                    'errors': form.errors.as_json()
                }, status=400)
    else:
        form = CustomerForm()

    categories = CustomerCategory.objects.all()

    context = {
        'form': form,
        'categories': categories,
        'title': _('إضافة عميل جديد'),
    }

    # إذا كان الطلب من نافذة منبثقة
    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        return render(request, 'customers/partials/customer_form.html', context)

    return render(request, 'customers/add_customer.html', context)

@login_required
def edit_customer(request, customer_id):
    customer = get_object_or_404(Customer, id=customer_id)

    if request.method == 'POST':
        form = CustomerForm(request.POST, instance=customer)
        if form.is_valid():
            customer = form.save()
            messages.success(request, _('تم تحديث بيانات العميل بنجاح'))

            # إذا تم تقديم النموذج مع الانتقال إلى صفحة التفاصيل
            if 'save_and_view' in request.POST:
                return redirect('customers:view_customer', customer_id=customer.id)

            # العودة إلى قائمة العملاء
            return redirect('customers:index')
    else:
        form = CustomerForm(instance=customer)

    # الحصول على المبيعات المرتبطة بالعميل
    sales = Sale.objects.filter(customer=customer).order_by('-date')[:5]

    # الحصول على مركبات العميل
    vehicles = customer.vehicles.all()

    # الحصول على تفاعلات العميل
    interactions = customer.interactions.all().order_by('-date')[:5]

    context = {
        'form': form,
        'customer': customer,
        'sales': sales,
        'vehicles': vehicles,
        'interactions': interactions,
        'title': _('تعديل بيانات العميل'),
    }
    return render(request, 'customers/edit_customer.html', context)

@login_required
def view_customer(request, customer_id):
    customer = get_object_or_404(Customer, id=customer_id)

    # تحديث رصيد العميل وتاريخ آخر شراء
    customer.update_balance()

    # الحصول على المبيعات المرتبطة بالعميل
    sales = Sale.objects.filter(customer=customer).order_by('-date')

    # تقسيم المبيعات إلى صفحات
    paginator = Paginator(sales, 10)  # 10 مبيعات في كل صفحة
    page_number = request.GET.get('page')
    sales_page = paginator.get_page(page_number)

    # الحصول على مركبات العميل
    vehicles = customer.vehicles.all()

    # الحصول على تفاعلات العميل
    interactions = customer.interactions.all().order_by('-date')

    # نموذج إضافة تفاعل جديد
    interaction_form = CustomerInteractionForm()

    # نموذج إضافة مركبة جديدة
    vehicle_form = CustomerVehicleForm()

    # الحصول على التاريخ الحالي
    from django.utils import timezone
    today = timezone.now().date()

    context = {
        'customer': customer,
        'sales': sales_page,
        'vehicles': vehicles,
        'interactions': interactions,
        'interaction_form': interaction_form,
        'vehicle_form': vehicle_form,
        'total_sales': sales.count(),
        'total_amount': customer.total_amount_spent,
        'pending_sales': customer.pending_sales,
        'pending_amount': customer.pending_amount,
        'today': today,
    }
    return render(request, 'customers/view_customer.html', context)

@login_required
def delete_customer(request, customer_id):
    customer = get_object_or_404(Customer, id=customer_id)

    # التحقق من وجود مبيعات مرتبطة بالعميل
    has_sales = Sale.objects.filter(customer=customer).exists()

    if request.method == 'POST':
        # إذا كان لديه مبيعات ولم يتم تأكيد الحذف
        if has_sales and not request.POST.get('confirm_delete_with_sales'):
            messages.error(request, _('لا يمكن حذف العميل لأنه لديه مبيعات مرتبطة. يرجى تأكيد الحذف.'))
            return redirect('customers:delete_customer', customer_id=customer.id)

        # إذا تم تأكيد الحذف أو لم يكن لديه مبيعات
        try:
            # إذا كان الطلب من AJAX
            if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                customer.delete()
                return JsonResponse({'success': True, 'message': _('تم حذف العميل بنجاح')})

            # إذا كان الطلب عادي
            customer.delete()
            messages.success(request, _('تم حذف العميل بنجاح'))
            return redirect('customers:index')
        except Exception as e:
            # إذا كان الطلب من AJAX
            if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                return JsonResponse({'success': False, 'message': str(e)}, status=400)

            # إذا كان الطلب عادي
            messages.error(request, _('حدث خطأ أثناء حذف العميل: ') + str(e))
            return redirect('customers:delete_customer', customer_id=customer.id)

    # الحصول على عدد المبيعات المرتبطة
    sales_count = Sale.objects.filter(customer=customer).count()

    context = {
        'customer': customer,
        'has_sales': has_sales,
        'sales_count': sales_count,
    }

    # إذا كان الطلب من AJAX
    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        return render(request, 'customers/partials/delete_customer_modal.html', context)

    return render(request, 'customers/delete_customer.html', context)


@login_required
@require_POST
def add_vehicle(request, customer_id):
    """إضافة مركبة جديدة للعميل"""
    customer = get_object_or_404(Customer, id=customer_id)
    form = CustomerVehicleForm(request.POST)

    if form.is_valid():
        vehicle = form.save(commit=False)
        vehicle.customer = customer
        vehicle.save()

        # إذا كان الطلب من AJAX
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': True,
                'message': _('تم إضافة المركبة بنجاح'),
                'vehicle_id': vehicle.id,
                'vehicle_name': str(vehicle)
            })

        messages.success(request, _('تم إضافة المركبة بنجاح'))
        return redirect('customers:view_customer', customer_id=customer.id)
    else:
        # إذا كان الطلب من AJAX
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'errors': form.errors.as_json()
            }, status=400)

        messages.error(request, _('يرجى تصحيح الأخطاء التالية'))
        return redirect('customers:view_customer', customer_id=customer.id)


@login_required
@require_POST
def delete_vehicle(request, vehicle_id):
    """حذف مركبة العميل"""
    vehicle = get_object_or_404(CustomerVehicle, id=vehicle_id)
    customer_id = vehicle.customer.id

    try:
        vehicle.delete()

        # إذا كان الطلب من AJAX
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': True,
                'message': _('تم حذف المركبة بنجاح')
            })

        messages.success(request, _('تم حذف المركبة بنجاح'))
    except Exception as e:
        # إذا كان الطلب من AJAX
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'message': str(e)
            }, status=400)

        messages.error(request, _('حدث خطأ أثناء حذف المركبة: ') + str(e))

    return redirect('customers:view_customer', customer_id=customer_id)


@login_required
@require_POST
def add_interaction(request, customer_id):
    """إضافة تفاعل جديد مع العميل"""
    customer = get_object_or_404(Customer, id=customer_id)
    form = CustomerInteractionForm(request.POST)

    if form.is_valid():
        interaction = form.save(commit=False)
        interaction.customer = customer
        interaction.employee = request.user.employee_profile if hasattr(request.user, 'employee_profile') else None
        interaction.save()

        # إذا كان الطلب من AJAX
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            html = render_to_string(
                template_name='customers/partials/interaction_item.html',
                context={'interaction': interaction}
            )
            return JsonResponse({
                'success': True,
                'message': _('تم إضافة التفاعل بنجاح'),
                'html': html
            })

        messages.success(request, _('تم إضافة التفاعل بنجاح'))
        return redirect('customers:view_customer', customer_id=customer.id)
    else:
        # إذا كان الطلب من AJAX
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'errors': form.errors.as_json()
            }, status=400)

        messages.error(request, _('يرجى تصحيح الأخطاء التالية'))
        return redirect('customers:view_customer', customer_id=customer.id)


@login_required
def import_customers(request):
    """استيراد العملاء من ملف CSV أو Excel"""
    if request.method == 'POST':
        form = CustomerImportForm(request.POST, request.FILES)
        if form.is_valid():
            customer_import = form.save(commit=False)
            customer_import.imported_by = request.user
            customer_import.status = 'processing'
            customer_import.save()

            # معالجة الملف واستيراد العملاء
            try:
                # التحقق من نوع الملف
                file_ext = customer_import.file.name.split('.')[-1].lower()

                if file_ext == 'csv':
                    # معالجة ملف CSV
                    imported_count = process_csv_import(customer_import)
                elif file_ext in ['xlsx', 'xls']:
                    # معالجة ملف Excel
                    imported_count = process_excel_import(customer_import)
                else:
                    raise ValueError(_('نوع الملف غير مدعوم'))

                # تحديث حالة الاستيراد
                customer_import.status = 'completed'
                customer_import.processed_records = imported_count
                customer_import.save()

                messages.success(request, _('تم استيراد {} عميل بنجاح').format(imported_count))
            except Exception as e:
                # تحديث حالة الاستيراد في حالة الخطأ
                customer_import.status = 'failed'
                customer_import.error_log = str(e)
                customer_import.save()

                messages.error(request, _('حدث خطأ أثناء استيراد العملاء: ') + str(e))

            return redirect('customers:index')
    else:
        form = CustomerImportForm()

    # الحصول على عمليات الاستيراد السابقة
    previous_imports = CustomerImport.objects.all().order_by('-created_at')[:10]

    context = {
        'form': form,
        'previous_imports': previous_imports,
        'title': _('استيراد العملاء'),
    }
    return render(request, 'customers/import_customers.html', context)


@login_required
def export_customers(request):
    """تصدير العملاء إلى ملف CSV أو Excel"""
    form = CustomerExportForm(request.GET or None)

    # الحصول على جميع العملاء
    customers = Customer.objects.all().select_related('category')

    # تطبيق عوامل التصفية إذا تم تقديم النموذج
    if form.is_valid() and 'export' in request.GET:
        category = form.cleaned_data.get('category')
        is_active = form.cleaned_data.get('is_active')
        export_format = form.cleaned_data.get('format')

        # تصفية حسب الفئة
        if category:
            customers = customers.filter(category=category)

        # تصفية حسب الحالة
        if is_active == '1':
            customers = customers.filter(is_active=True)
        elif is_active == '0':
            customers = customers.filter(is_active=False)

        # تصدير العملاء حسب التنسيق المطلوب
        if export_format == 'csv':
            return export_customers_to_csv(customers)
        elif export_format == 'excel':
            return export_customers_to_excel(customers)
        else:
            messages.error(request, _('تنسيق غير مدعوم'))

    context = {
        'form': form,
        'title': _('تصدير العملاء'),
    }
    return render(request, 'customers/export_customers.html', context)


# دوال مساعدة للاستيراد والتصدير

def process_csv_import(customer_import):
    """معالجة ملف CSV لاستيراد العملاء"""
    imported_count = 0
    errors = []

    # فتح الملف وقراءته
    with customer_import.file.open('r') as f:
        reader = csv.DictReader(f)
        total_rows = sum(1 for row in reader)
        customer_import.total_records = total_rows
        customer_import.save()

        # إعادة المؤشر إلى بداية الملف
        f.seek(0)
        reader = csv.DictReader(f)

        for row in reader:
            try:
                # التحقق من الحقول المطلوبة
                if not row.get('name') or not row.get('phone'):
                    errors.append(f"Missing required fields for row: {row}")
                    continue

                # التحقق من عدم وجود عميل بنفس رقم الهاتف
                if Customer.objects.filter(phone=row['phone']).exists():
                    errors.append(f"Customer with phone {row['phone']} already exists")
                    continue

                # إنشاء العميل
                customer = Customer(
                    name=row['name'],
                    phone=row['phone'],
                    email=row.get('email'),
                    address=row.get('address'),
                    city=row.get('city'),
                    notes=row.get('notes'),
                    is_active=row.get('is_active', 'True').lower() in ['true', '1', 'yes'],
                )

                # إضافة الفئة إذا كانت موجودة
                if row.get('category'):
                    category, created = CustomerCategory.objects.get_or_create(name=row['category'])
                    customer.category = category

                customer.save()
                imported_count += 1

            except Exception as e:
                errors.append(f"Error importing row {row}: {str(e)}")

    # تحديث سجل الأخطاء
    if errors:
        customer_import.error_log = "\n".join(errors)
        customer_import.save()

    return imported_count


def process_excel_import(customer_import):
    """معالجة ملف Excel لاستيراد العملاء"""
    # هنا يمكن استخدام مكتبة pandas أو openpyxl لقراءة ملفات Excel
    # للتبسيط، سنفترض أن المنطق مشابه لمعالجة CSV
    return 0  # يجب تنفيذ هذه الدالة بشكل كامل


def export_customers_to_csv(customers):
    """تصدير العملاء إلى ملف CSV"""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="customers.csv"'

    # إنشاء كاتب CSV
    writer = csv.writer(response)

    # كتابة رأس الجدول
    writer.writerow(['ID', 'Name', 'Phone', 'Email', 'Address', 'City', 'Category', 'Is Active', 'Credit Limit', 'Balance', 'Created At'])

    # كتابة بيانات العملاء
    for customer in customers:
        writer.writerow([
            customer.id,
            customer.name,
            customer.phone,
            customer.email or '',
            customer.address or '',
            customer.city or '',
            customer.category.name if customer.category else '',
            'Yes' if customer.is_active else 'No',
            customer.credit_limit,
            customer.balance,
            customer.created_at.strftime('%Y-%m-%d')
        ])

    return response


def export_customers_to_excel(customers):
    """تصدير العملاء إلى ملف Excel"""
    response = HttpResponse(content_type='application/ms-excel')
    response['Content-Disposition'] = 'attachment; filename="customers.xls"'

    # إنشاء ملف Excel
    wb = xlwt.Workbook(encoding='utf-8')
    ws = wb.add_sheet('Customers')

    # أنماط الخلايا
    header_style = xlwt.easyxf('font: bold on; align: wrap on, vert centre, horiz center')
    date_style = xlwt.easyxf(num_format_str='yyyy-mm-dd')

    # كتابة رأس الجدول
    headers = ['ID', 'Name', 'Phone', 'Email', 'Address', 'City', 'Category', 'Is Active', 'Credit Limit', 'Balance', 'Created At']
    for col_num, header in enumerate(headers):
        ws.write(0, col_num, header, header_style)

    # كتابة بيانات العملاء
    for row_num, customer in enumerate(customers, 1):
        ws.write(row_num, 0, customer.id)
        ws.write(row_num, 1, customer.name)
        ws.write(row_num, 2, customer.phone)
        ws.write(row_num, 3, customer.email or '')
        ws.write(row_num, 4, customer.address or '')
        ws.write(row_num, 5, customer.city or '')
        ws.write(row_num, 6, customer.category.name if customer.category else '')
        ws.write(row_num, 7, 'Yes' if customer.is_active else 'No')
        ws.write(row_num, 8, float(customer.credit_limit))
        ws.write(row_num, 9, float(customer.balance))
        ws.write(row_num, 10, customer.created_at, date_style)

    # ضبط عرض الأعمدة
    for col_num in range(len(headers)):
        ws.col(col_num).width = 256 * 20  # 20 حرفًا لكل عمود

    wb.save(response)
    return response
