#!/usr/bin/env python
import requests
import json

def test_barcode_api():
    url = "http://127.0.0.1:8000/inventory/barcode/scan/"
    
    # Test without CSRF token first
    data = {
        'barcode_number': '1282235856942741'
    }
    
    print(f"Testing URL: {url}")
    print(f"Data: {data}")
    
    try:
        response = requests.post(url, data=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Text: {response.text}")
        
        if response.headers.get('content-type', '').startswith('application/json'):
            try:
                json_data = response.json()
                print(f"JSON Response: {json.dumps(json_data, indent=2, ensure_ascii=False)}")
            except:
                print("Could not parse JSON")
                
    except Exception as e:
        print(f"Error: {e}")

if __name__ == '__main__':
    test_barcode_api()
