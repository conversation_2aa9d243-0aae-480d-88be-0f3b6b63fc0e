{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/settings.css' %}">
{% endblock %}

{% block title %}{% trans "إعدادات العملة" %} | {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "إعدادات العملة" %}</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCurrencyModal">
            <i class="fas fa-plus-circle me-1"></i> {% trans "إضافة عملة جديدة" %}
        </button>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "العملات المتاحة" %}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="currenciesTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>{% trans "العملة" %}</th>
                            <th>{% trans "الرمز" %}</th>
                            <th>{% trans "موضع الرمز" %}</th>
                            <th>{% trans "منازل عشرية" %}</th>
                            <th>{% trans "افتراضي" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for currency in currencies %}
                        <tr>
                            <td>{{ currency.get_currency_display }}</td>
                            <td>{{ currency.symbol }}</td>
                            <td>{{ currency.get_position_display }}</td>
                            <td>{{ currency.decimal_places }}</td>
                            <td>
                                {% if currency.is_default %}
                                <span class="badge bg-success">{% trans "نعم" %}</span>
                                {% else %}
                                <span class="badge bg-secondary">{% trans "لا" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-info edit-currency" 
                                    data-id="{{ currency.id }}"
                                    data-currency="{{ currency.currency }}"
                                    data-symbol="{{ currency.symbol }}"
                                    data-position="{{ currency.position }}"
                                    data-decimal-places="{{ currency.decimal_places }}"
                                    data-thousands-separator="{{ currency.thousands_separator }}"
                                    data-decimal-separator="{{ currency.decimal_separator }}"
                                    data-is-default="{{ currency.is_default|yesno:'true,false' }}"
                                    data-bs-toggle="modal" data-bs-target="#editCurrencyModal">
                                    <i class="fas fa-edit"></i>
                                </button>
                                {% if not currency.is_default %}
                                <button type="button" class="btn btn-sm btn-danger delete-currency" 
                                    data-id="{{ currency.id }}" 
                                    data-name="{{ currency.get_currency_display }}"
                                    data-bs-toggle="modal" data-bs-target="#deleteCurrencyModal">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">{% trans "لا توجد عملات مضافة" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Currency Modal -->
<div class="modal fade" id="addCurrencyModal" tabindex="-1" aria-labelledby="addCurrencyModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{% url 'settings_app:currency_settings' %}">
                {% csrf_token %}
                <input type="hidden" name="action" value="add">
                <div class="modal-header">
                    <h5 class="modal-title" id="addCurrencyModalLabel">{% trans "إضافة عملة جديدة" %}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="currency" class="form-label">{% trans "العملة" %}</label>
                        <select class="form-select" id="currency" name="currency" required>
                            <option value="MAD">{% trans "درهم مغربي" %}</option>
                            <option value="USD">{% trans "دولار أمريكي" %}</option>
                            <option value="EUR">{% trans "يورو" %}</option>
                            <option value="SAR">{% trans "ريال سعودي" %}</option>
                            <option value="AED">{% trans "درهم إماراتي" %}</option>
                            <option value="EGP">{% trans "جنيه مصري" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="symbol" class="form-label">{% trans "الرمز" %}</label>
                        <input type="text" class="form-control" id="symbol" name="symbol" required>
                    </div>
                    <div class="mb-3">
                        <label for="position" class="form-label">{% trans "موضع الرمز" %}</label>
                        <select class="form-select" id="position" name="position" required>
                            <option value="before">{% trans "قبل المبلغ" %}</option>
                            <option value="after" selected>{% trans "بعد المبلغ" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="decimal_places" class="form-label">{% trans "منازل عشرية" %}</label>
                        <input type="number" class="form-control" id="decimal_places" name="decimal_places" min="0" max="4" value="2" required>
                    </div>
                    <div class="mb-3">
                        <label for="thousands_separator" class="form-label">{% trans "فاصل الآلاف" %}</label>
                        <input type="text" class="form-control" id="thousands_separator" name="thousands_separator" maxlength="1" value="," required>
                    </div>
                    <div class="mb-3">
                        <label for="decimal_separator" class="form-label">{% trans "فاصل العشري" %}</label>
                        <input type="text" class="form-control" id="decimal_separator" name="decimal_separator" maxlength="1" value="." required>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="is_default" name="is_default">
                        <label class="form-check-label" for="is_default">
                            {% trans "تعيين كعملة افتراضية" %}
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "إضافة" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Currency Modal -->
<div class="modal fade" id="editCurrencyModal" tabindex="-1" aria-labelledby="editCurrencyModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{% url 'settings_app:currency_settings' %}">
                {% csrf_token %}
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="currency_id" id="edit_currency_id">
                <div class="modal-header">
                    <h5 class="modal-title" id="editCurrencyModalLabel">{% trans "تعديل العملة" %}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_currency" class="form-label">{% trans "العملة" %}</label>
                        <select class="form-select" id="edit_currency" name="currency" required>
                            <option value="MAD">{% trans "درهم مغربي" %}</option>
                            <option value="USD">{% trans "دولار أمريكي" %}</option>
                            <option value="EUR">{% trans "يورو" %}</option>
                            <option value="SAR">{% trans "ريال سعودي" %}</option>
                            <option value="AED">{% trans "درهم إماراتي" %}</option>
                            <option value="EGP">{% trans "جنيه مصري" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_symbol" class="form-label">{% trans "الرمز" %}</label>
                        <input type="text" class="form-control" id="edit_symbol" name="symbol" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_position" class="form-label">{% trans "موضع الرمز" %}</label>
                        <select class="form-select" id="edit_position" name="position" required>
                            <option value="before">{% trans "قبل المبلغ" %}</option>
                            <option value="after">{% trans "بعد المبلغ" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_decimal_places" class="form-label">{% trans "منازل عشرية" %}</label>
                        <input type="number" class="form-control" id="edit_decimal_places" name="decimal_places" min="0" max="4" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_thousands_separator" class="form-label">{% trans "فاصل الآلاف" %}</label>
                        <input type="text" class="form-control" id="edit_thousands_separator" name="thousands_separator" maxlength="1" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_decimal_separator" class="form-label">{% trans "فاصل العشري" %}</label>
                        <input type="text" class="form-control" id="edit_decimal_separator" name="decimal_separator" maxlength="1" required>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="edit_is_default" name="is_default">
                        <label class="form-check-label" for="edit_is_default">
                            {% trans "تعيين كعملة افتراضية" %}
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "حفظ التغييرات" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Currency Modal -->
<div class="modal fade" id="deleteCurrencyModal" tabindex="-1" aria-labelledby="deleteCurrencyModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" id="deleteCurrencyForm">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteCurrencyModalLabel">{% trans "حذف العملة" %}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>{% trans "هل أنت متأكد من رغبتك في حذف العملة" %} <span id="currency_name_to_delete"></span>؟</p>
                    <p class="text-danger">{% trans "هذا الإجراء لا يمكن التراجع عنه." %}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-danger">{% trans "حذف" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Edit Currency
        document.querySelectorAll('.edit-currency').forEach(function(button) {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const currency = this.getAttribute('data-currency');
                const symbol = this.getAttribute('data-symbol');
                const position = this.getAttribute('data-position');
                const decimalPlaces = this.getAttribute('data-decimal-places');
                const thousandsSeparator = this.getAttribute('data-thousands-separator');
                const decimalSeparator = this.getAttribute('data-decimal-separator');
                const isDefault = this.getAttribute('data-is-default') === 'true';
                
                document.getElementById('edit_currency_id').value = id;
                document.getElementById('edit_currency').value = currency;
                document.getElementById('edit_symbol').value = symbol;
                document.getElementById('edit_position').value = position;
                document.getElementById('edit_decimal_places').value = decimalPlaces;
                document.getElementById('edit_thousands_separator').value = thousandsSeparator;
                document.getElementById('edit_decimal_separator').value = decimalSeparator;
                document.getElementById('edit_is_default').checked = isDefault;
            });
        });
        
        // Delete Currency
        document.querySelectorAll('.delete-currency').forEach(function(button) {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');
                
                document.getElementById('currency_name_to_delete').textContent = name;
                document.getElementById('deleteCurrencyForm').action = "{% url 'settings_app:delete_currency' 0 %}".replace('0', id);
            });
        });
    });
</script>
{% endblock %}
