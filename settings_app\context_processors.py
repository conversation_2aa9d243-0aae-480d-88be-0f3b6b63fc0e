from .models import CompanyInfo, CurrencySetting, TaxSetting
import datetime

def company_info(request):
    """
    إضافة معلومات الشركة إلى سياق جميع القوالب
    """
    try:
        company_info = CompanyInfo.objects.first()
        if not company_info:
            # إنشاء معلومات شركة افتراضية إذا لم تكن موجودة
            company_info = CompanyInfo.objects.create(
                name="نظام إدارة متجر قطع غيار السيارات",
                address="العنوان الافتراضي",
                phone="*********",
                email="<EMAIL>"
            )
    except Exception:
        # في حالة حدوث خطأ، إرجاع قاموس فارغ
        return {'company_info': None, 'current_year': datetime.datetime.now().year}

    return {
        'company_info': company_info,
        'current_year': datetime.datetime.now().year
    }

def currency_settings(request):
    """
    إضافة إعدادات العملة إلى سياق جميع القوالب
    """
    try:
        currency = CurrencySetting.objects.filter(is_default=True).first()
        if not currency:
            # إنشاء إعدادات عملة افتراضية إذا لم تكن موجودة
            currency = CurrencySetting.objects.create(
                currency='MAD',
                symbol='د.م.',
                position='after',
                is_default=True
            )
    except Exception:
        # في حالة حدوث خطأ، إرجاع قاموس فارغ
        return {'currency': None}

    return {
        'currency': currency
    }

def tax_settings(request):
    """
    إضافة إعدادات الضريبة إلى سياق جميع القوالب
    """
    try:
        tax = TaxSetting.objects.filter(is_enabled=True).first()
        if not tax:
            # إنشاء إعدادات ضريبة افتراضية إذا لم تكن موجودة
            tax = TaxSetting.objects.create(
                name='ضريبة القيمة المضافة',
                rate=15.00,
                tax_type='exclusive',
                is_enabled=True
            )
    except Exception:
        # في حالة حدوث خطأ، إرجاع قاموس فارغ
        return {'tax': None}

    return {
        'tax': tax
    }
