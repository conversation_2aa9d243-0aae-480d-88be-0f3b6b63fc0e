{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "التقارير" %} | {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "التقارير" %}</h1>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                {% trans "تقرير المبيعات" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{% trans "تحليل المبيعات" %}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-top-0">
                    <a href="{% url 'reports:sales_report' %}" class="btn btn-primary btn-sm btn-block">
                        {% trans "عرض التقرير" %}
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                {% trans "تقرير المخزون" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{% trans "حالة المخزون" %}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-boxes fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-top-0">
                    <a href="{% url 'reports:inventory_report' %}" class="btn btn-success btn-sm btn-block">
                        {% trans "عرض التقرير" %}
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                {% trans "تقرير العملاء" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{% trans "تحليل العملاء" %}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-top-0">
                    <a href="{% url 'reports:customers_report' %}" class="btn btn-info btn-sm btn-block">
                        {% trans "عرض التقرير" %}
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                {% trans "التقرير المالي" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{% trans "الأرباح والمصروفات" %}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-top-0">
                    <a href="{% url 'reports:financial_report' %}" class="btn btn-warning btn-sm btn-block">
                        {% trans "عرض التقرير" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "التقارير المحفوظة" %}</h6>
                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#saveReportModal">
                        <i class="fas fa-plus-circle me-1"></i> {% trans "حفظ تقرير جديد" %}
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="savedReportsTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>{% trans "اسم التقرير" %}</th>
                                    <th>{% trans "النوع" %}</th>
                                    <th>{% trans "تاريخ الإنشاء" %}</th>
                                    <th>{% trans "عام" %}</th>
                                    <th>{% trans "الإجراءات" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for report in saved_reports %}
                                <tr>
                                    <td>{{ report.name }}</td>
                                    <td>{{ report.get_report_type_display }}</td>
                                    <td>{{ report.created_at|date:"Y-m-d" }}</td>
                                    <td>
                                        {% if report.is_public %}
                                        <span class="badge bg-success">{% trans "نعم" %}</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{% trans "لا" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="#" class="btn btn-sm btn-info view-report" data-id="{{ report.id }}">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if report.created_by == request.user %}
                                        <a href="#" class="btn btn-sm btn-warning edit-report" data-id="{{ report.id }}">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-danger delete-report" data-id="{{ report.id }}" data-name="{{ report.name }}">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center">{% trans "لا توجد تقارير محفوظة" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "التقارير المجدولة" %}</h6>
                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#scheduleReportModal">
                        <i class="fas fa-calendar-plus me-1"></i> {% trans "جدولة تقرير" %}
                    </button>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        {% for schedule in scheduled_reports %}
                        <div class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">{{ schedule.saved_report.name }}</h5>
                                <small>{{ schedule.get_frequency_display }}</small>
                            </div>
                            <p class="mb-1">{{ schedule.saved_report.get_report_type_display }}</p>
                            <small>{% trans "آخر إرسال:" %} {% if schedule.last_sent %}{{ schedule.last_sent|date:"Y-m-d H:i" }}{% else %}{% trans "لم يتم الإرسال بعد" %}{% endif %}</small>
                            <div class="mt-2">
                                <button type="button" class="btn btn-sm btn-outline-info edit-schedule" data-id="{{ schedule.id }}">
                                    <i class="fas fa-edit"></i> {% trans "تعديل" %}
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger delete-schedule" data-id="{{ schedule.id }}" data-name="{{ schedule.saved_report.name }}">
                                    <i class="fas fa-trash"></i> {% trans "حذف" %}
                                </button>
                            </div>
                        </div>
                        {% empty %}
                        <div class="text-center py-3">
                            <p class="text-muted">{% trans "لا توجد تقارير مجدولة" %}</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Save Report Modal -->
<div class="modal fade" id="saveReportModal" tabindex="-1" aria-labelledby="saveReportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="saveReportModalLabel">{% trans "حفظ تقرير جديد" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="saveReportForm" method="post" action="#">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="report_name" class="form-label">{% trans "اسم التقرير" %}</label>
                        <input type="text" class="form-control" id="report_name" name="report_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="report_type" class="form-label">{% trans "نوع التقرير" %}</label>
                        <select class="form-select" id="report_type" name="report_type" required>
                            <option value="sales">{% trans "المبيعات" %}</option>
                            <option value="inventory">{% trans "المخزون" %}</option>
                            <option value="customers">{% trans "العملاء" %}</option>
                            <option value="financial">{% trans "المالية" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="report_description" class="form-label">{% trans "الوصف" %}</label>
                        <textarea class="form-control" id="report_description" name="report_description" rows="3"></textarea>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="is_public" name="is_public">
                        <label class="form-check-label" for="is_public">
                            {% trans "جعل التقرير عامًا (متاح لجميع المستخدمين)" %}
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-primary" id="saveReportBtn">{% trans "حفظ التقرير" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Schedule Report Modal -->
<div class="modal fade" id="scheduleReportModal" tabindex="-1" aria-labelledby="scheduleReportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="scheduleReportModalLabel">{% trans "جدولة تقرير" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="scheduleReportForm" method="post" action="#">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="saved_report" class="form-label">{% trans "التقرير" %}</label>
                        <select class="form-select" id="saved_report" name="saved_report" required>
                            {% for report in saved_reports %}
                            <option value="{{ report.id }}">{{ report.name }} ({{ report.get_report_type_display }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="frequency" class="form-label">{% trans "التكرار" %}</label>
                        <select class="form-select" id="frequency" name="frequency" required>
                            <option value="daily">{% trans "يومي" %}</option>
                            <option value="weekly">{% trans "أسبوعي" %}</option>
                            <option value="monthly">{% trans "شهري" %}</option>
                            <option value="quarterly">{% trans "ربع سنوي" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="recipients" class="form-label">{% trans "المستلمون" %}</label>
                        <input type="text" class="form-control" id="recipients" name="recipients" placeholder="{% trans "بريد إلكتروني مفصول بفواصل" %}" required>
                    </div>
                    <div class="mb-3">
                        <label for="subject" class="form-label">{% trans "الموضوع" %}</label>
                        <input type="text" class="form-control" id="subject" name="subject" required>
                    </div>
                    <div class="mb-3">
                        <label for="message" class="form-label">{% trans "الرسالة" %}</label>
                        <textarea class="form-control" id="message" name="message" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-primary" id="scheduleReportBtn">{% trans "جدولة التقرير" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize DataTable
        $('#savedReportsTable').DataTable({
            "language": {
                "url": "{% static 'js/dataTables.arabic.json' %}",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "pageLength": 10,
            "lengthMenu": [[5, 10, 15, 20, 25, 50, -1], [5, 10, 15, 20, 25, 50, "الكل"]],
            "dom": 'lfrtip'
        });

        // Save Report Button
        const saveReportBtn = document.getElementById('saveReportBtn');
        if (saveReportBtn) {
            saveReportBtn.addEventListener('click', function() {
                document.getElementById('saveReportForm').submit();
            });
        }

        // Schedule Report Button
        const scheduleReportBtn = document.getElementById('scheduleReportBtn');
        if (scheduleReportBtn) {
            scheduleReportBtn.addEventListener('click', function() {
                document.getElementById('scheduleReportForm').submit();
            });
        }
    });
</script>
{% endblock %}
