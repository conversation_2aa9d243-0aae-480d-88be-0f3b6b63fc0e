{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "المصروفات" %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card shadow">
        <div class="card-header bg-danger text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{% trans "المصروفات" %}</h5>
            <a href="{% url 'finance:add_expense' %}" class="btn btn-light btn-sm">
                <i class="fas fa-plus"></i> {% trans "إضافة مصروف جديد" %}
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>{% trans "التاريخ" %}</th>
                            <th>{% trans "الفئة" %}</th>
                            <th>{% trans "الوصف" %}</th>
                            <th>{% trans "المرجع" %}</th>
                            <th>{% trans "الحساب" %}</th>
                            <th>{% trans "المبلغ" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for expense in expenses %}
                        <tr>
                            <td>{{ expense.date }}</td>
                            <td>{{ expense.category.name }}</td>
                            <td>{{ expense.description|default:"-" }}</td>
                            <td>{{ expense.reference|default:"-" }}</td>
                            <td>{{ expense.account.name }}</td>
                            <td class="text-danger">{{ expense.amount }} {% trans "د.م" %}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'finance:edit_expense' expense.id %}" class="btn btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'finance:delete_expense' expense.id %}" class="btn btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center">{% trans "لا توجد مصروفات مسجلة بعد" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
