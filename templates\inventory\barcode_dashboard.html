{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "لوحة تحكم الباركود" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-barcode me-2"></i>{% trans "لوحة تحكم الباركود" %}</h5>
                </div>
                <div class="card-body">
                    <!-- إحصائيات الباركود -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3>{{ total_barcodes }}</h3>
                                    <p class="mb-0">{% trans "إجمالي الباركودات" %}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3>{{ barcode_types_count }}</h3>
                                    <p class="mb-0">{% trans "أنواع الباركود" %}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h3>{{ barcodes_printed }}</h3>
                                    <p class="mb-0">{% trans "الباركودات المطبوعة" %}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h3>{{ barcodes_scanned }}</h3>
                                    <p class="mb-0">{% trans "الباركودات الممسوحة" %}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- وظائف الباركود الرئيسية -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="border-bottom pb-2">{% trans "وظائف الباركود" %}</h5>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-cog me-2"></i>{% trans "إدارة الباركود" %}</h5>
                                    <p class="card-text">{% trans "إدارة أنواع الباركود وإعدادات النظام" %}</p>
                                    <div class="d-grid gap-2">
                                        <a href="{% url 'inventory:barcode:barcode_types' %}" class="btn btn-outline-primary"><i class="fas fa-tags me-2"></i>{% trans "أنواع الباركود" %}</a>
                                        <a href="{% url 'inventory:barcode:barcode_settings' %}" class="btn btn-outline-secondary"><i class="fas fa-sliders-h me-2"></i>{% trans "إعدادات الباركود" %}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-print me-2"></i>{% trans "إنشاء وطباعة الباركود" %}</h5>
                                    <p class="card-text">{% trans "إنشاء باركود جديد أو طباعة باركود لمنتجات متعددة" %}</p>
                                    <div class="d-grid gap-2">
                                        <a href="{% url 'inventory:barcode:generate_barcode' %}" class="btn btn-outline-success"><i class="fas fa-plus-circle me-2"></i>{% trans "إنشاء باركود" %}</a>
                                        <a href="{% url 'inventory:select_products_for_barcode' %}" class="btn btn-outline-info"><i class="fas fa-print me-2"></i>{% trans "طباعة دفعة باركود" %}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-qrcode me-2"></i>{% trans "مسح وتتبع الباركود" %}</h5>
                                    <p class="card-text">{% trans "مسح الباركود باستخدام الكاميرا وعرض سجلات الاستخدام" %}</p>
                                    <div class="d-grid gap-2">
                                        <a href="{% url 'inventory:barcode:scan_barcode' %}" class="btn btn-outline-danger"><i class="fas fa-camera me-2"></i>{% trans "مسح الباركود" %}</a>
                                        <a href="{% url 'inventory:barcode:barcode_logs' %}" class="btn btn-outline-dark"><i class="fas fa-history me-2"></i>{% trans "سجلات الباركود" %}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ميزات متقدمة -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="border-bottom pb-2">{% trans "ميزات متقدمة" %}</h5>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-file-export me-2"></i>{% trans "تصدير واستيراد" %}</h5>
                                    <p class="card-text">{% trans "تصدير قائمة الباركود إلى ملفات Excel/CSV أو استيراد الباركود من ملفات خارجية" %}</p>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-outline-primary" onclick="alert('سيتم تطوير هذه الميزة قريباً')"><i class="fas fa-file-export me-2"></i>{% trans "تصدير الباركود" %}</button>
                                        <button class="btn btn-outline-secondary" onclick="alert('سيتم تطوير هذه الميزة قريباً')"><i class="fas fa-file-import me-2"></i>{% trans "استيراد الباركود" %}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-cogs me-2"></i>{% trans "تكامل مع نظام نقطة البيع" %}</h5>
                                    <p class="card-text">{% trans "ربط مباشر بين مسح الباركود وإضافة المنتجات إلى سلة المبيعات" %}</p>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-outline-success" onclick="openPOSIntegration()"><i class="fas fa-link me-2"></i>{% trans "ربط مع نقطة البيع" %}</button>
                                        <button class="btn btn-outline-info" onclick="alert('سيتم تطوير هذه الميزة قريباً')"><i class="fas fa-sync me-2"></i>{% trans "تحديث المخزون تلقائياً" %}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الأنشطة الأخيرة -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-secondary text-white">
                                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>{% trans "الأنشطة الأخيرة" %}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th>{% trans "التاريخ" %}</th>
                                                    <th>{% trans "الباركود" %}</th>
                                                    <th>{% trans "العملية" %}</th>
                                                    <th>{% trans "المستخدم" %}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for log in recent_logs %}
                                                <tr>
                                                    <td>{{ log.created_at|date:"Y-m-d H:i" }}</td>
                                                    <td>{{ log.barcode.barcode_number }}</td>
                                                    <td>
                                                        {% if log.action == 'print' %}
                                                            <span class="badge bg-warning">{% trans "طباعة" %}</span>
                                                        {% elif log.action == 'scan' %}
                                                            <span class="badge bg-success">{% trans "مسح" %}</span>
                                                        {% elif log.action == 'create' %}
                                                            <span class="badge bg-primary">{% trans "إنشاء" %}</span>
                                                        {% elif log.action == 'delete' %}
                                                            <span class="badge bg-danger">{% trans "حذف" %}</span>
                                                        {% else %}
                                                            <span class="badge bg-secondary">{{ log.action }}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>{{ log.user.username }}</td>
                                                </tr>
                                                {% empty %}
                                                <tr>
                                                    <td colspan="4" class="text-center">{% trans "لا توجد أنشطة حديثة" %}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // يمكن إضافة أي سلوك JavaScript إضافي هنا
    });

    // وظيفة ربط نقطة البيع
    function openPOSIntegration() {
        // إنشاء نافذة حوار لربط نقطة البيع
        const posModal = $(`
            <div class="modal fade" id="posIntegrationModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-success text-white">
                            <h5 class="modal-title"><i class="fas fa-link me-2"></i>ربط مع نقطة البيع</h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card h-100">
                                        <div class="card-header bg-primary text-white">
                                            <h6 class="mb-0"><i class="fas fa-qrcode me-2"></i>مسح باركود للبيع</h6>
                                        </div>
                                        <div class="card-body text-center">
                                            <p class="text-muted">امسح باركود المنتج لإضافته إلى سلة المبيعات</p>
                                            <button class="btn btn-primary btn-lg" onclick="startPOSScanner()">
                                                <i class="fas fa-camera me-2"></i>بدء المسح
                                            </button>
                                            <div id="pos-scanner-result" class="mt-3"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card h-100">
                                        <div class="card-header bg-info text-white">
                                            <h6 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>سلة المبيعات</h6>
                                        </div>
                                        <div class="card-body">
                                            <div id="pos-cart-items">
                                                <p class="text-muted text-center">لا توجد منتجات في السلة</p>
                                            </div>
                                            <hr>
                                            <div class="d-flex justify-content-between">
                                                <strong>الإجمالي:</strong>
                                                <strong id="pos-total">0.00 د.م</strong>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-success" onclick="completePOSSale()">
                                <i class="fas fa-check me-2"></i>إتمام البيع
                            </button>
                            <button type="button" class="btn btn-info" onclick="openFullPOS()">
                                <i class="fas fa-external-link-alt me-2"></i>فتح نقطة البيع الكاملة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `);

        // إضافة النافذة إلى الصفحة
        $('body').append(posModal);

        // عرض النافذة
        const modal = new bootstrap.Modal(document.getElementById('posIntegrationModal'));
        modal.show();

        // إزالة النافذة عند إغلاقها
        $('#posIntegrationModal').on('hidden.bs.modal', function() {
            $(this).remove();
        });
    }

    // متغيرات عامة لسلة المبيعات
    let posCart = [];
    let posTotal = 0;

    // بدء ماسح الباركود لنقطة البيع
    function startPOSScanner() {
        $('#pos-scanner-result').html(`
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                سيتم فتح ماسح الباركود في نافذة جديدة
            </div>
        `);

        // فتح صفحة مسح الباركود في نافذة جديدة
        const scannerWindow = window.open('/inventory/barcode/scan/', '_blank', 'width=800,height=600');

        // استماع لرسائل من نافذة الماسح
        window.addEventListener('message', function(event) {
            if (event.data.type === 'barcode_scanned') {
                addProductToPOSCart(event.data.product);
                scannerWindow.close();
            }
        });
    }

    // إضافة منتج إلى سلة نقطة البيع
    function addProductToPOSCart(product) {
        // التحقق من وجود المنتج في السلة
        const existingItem = posCart.find(item => item.id === product.id);

        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            posCart.push({
                id: product.id,
                name: product.name,
                price: parseFloat(product.price),
                quantity: 1
            });
        }

        updatePOSCartDisplay();

        // عرض رسالة نجاح
        $('#pos-scanner-result').html(`
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                تم إضافة "${product.name}" إلى السلة
            </div>
        `);
    }

    // تحديث عرض سلة المبيعات
    function updatePOSCartDisplay() {
        const cartContainer = $('#pos-cart-items');

        if (posCart.length === 0) {
            cartContainer.html('<p class="text-muted text-center">لا توجد منتجات في السلة</p>');
            posTotal = 0;
        } else {
            let cartHTML = '';
            posTotal = 0;

            posCart.forEach((item, index) => {
                const itemTotal = item.price * item.quantity;
                posTotal += itemTotal;

                cartHTML += `
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                        <div>
                            <strong>${item.name}</strong><br>
                            <small class="text-muted">${item.price.toFixed(2)} د.م × ${item.quantity}</small>
                        </div>
                        <div class="text-end">
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-secondary" onclick="decreaseQuantity(${index})">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <button class="btn btn-outline-secondary" onclick="increaseQuantity(${index})">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button class="btn btn-outline-danger" onclick="removeFromCart(${index})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            <div class="mt-1">
                                <strong>${itemTotal.toFixed(2)} د.م</strong>
                            </div>
                        </div>
                    </div>
                `;
            });

            cartContainer.html(cartHTML);
        }

        $('#pos-total').text(posTotal.toFixed(2) + ' د.م');
    }

    // زيادة كمية المنتج
    function increaseQuantity(index) {
        posCart[index].quantity += 1;
        updatePOSCartDisplay();
    }

    // تقليل كمية المنتج
    function decreaseQuantity(index) {
        if (posCart[index].quantity > 1) {
            posCart[index].quantity -= 1;
            updatePOSCartDisplay();
        }
    }

    // إزالة منتج من السلة
    function removeFromCart(index) {
        posCart.splice(index, 1);
        updatePOSCartDisplay();
    }

    // إتمام عملية البيع
    function completePOSSale() {
        if (posCart.length === 0) {
            alert('لا توجد منتجات في السلة');
            return;
        }

        // هنا يمكن إرسال بيانات البيع إلى الخادم
        alert(`تم إتمام عملية البيع بقيمة ${posTotal.toFixed(2)} د.م`);

        // إعادة تعيين السلة
        posCart = [];
        posTotal = 0;
        updatePOSCartDisplay();

        // إغلاق النافذة
        $('#posIntegrationModal').modal('hide');
    }

    // فتح نقطة البيع الكاملة
    function openFullPOS() {
        // فتح صفحة نقطة البيع الرئيسية
        window.open('/sales/new/', '_blank');
    }
</script>
{% endblock %}