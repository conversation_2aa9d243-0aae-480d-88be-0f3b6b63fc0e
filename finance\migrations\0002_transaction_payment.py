# Generated by Django 5.2 on 2025-04-21 12:36

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('finance', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Transaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_number', models.CharField(max_length=50, unique=True, verbose_name='رقم المعاملة')),
                ('transaction_type', models.CharField(choices=[('sale', 'مبيعات'), ('purchase', 'مشتريات'), ('expense', 'مصروفات'), ('income', 'إيرادات'), ('transfer', 'تحويل'), ('other', 'أخرى')], max_length=20, verbose_name='نوع المعاملة')),
                ('date', models.DateField(verbose_name='تاريخ المعاملة')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ')),
                ('payment_status', models.CharField(choices=[('paid', 'مدفوعة'), ('unpaid', 'غير مدفوعة'), ('partial', 'مدفوعة جزئياً')], default='unpaid', max_length=20, verbose_name='حالة الدفع')),
                ('paid_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='المبلغ المدفوع')),
                ('object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('source_object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='finance.account', verbose_name='الحساب')),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to=settings.AUTH_USER_MODEL, verbose_name='أنشئت بواسطة')),
                ('source_content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='contenttypes.contenttype')),
            ],
            options={
                'verbose_name': 'معاملة مالية',
                'verbose_name_plural': 'معاملات مالية',
                'ordering': ['-date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='تاريخ الدفع')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ')),
                ('payment_method', models.CharField(choices=[('cash', 'نقدي'), ('bank_transfer', 'تحويل بنكي'), ('check', 'شيك'), ('credit_card', 'بطاقة ائتمان')], max_length=20, verbose_name='طريقة الدفع')),
                ('reference', models.CharField(blank=True, max_length=100, null=True, verbose_name='المرجع')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='finance.account', verbose_name='الحساب')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to=settings.AUTH_USER_MODEL, verbose_name='أنشئت بواسطة')),
                ('transaction', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='finance.transaction', verbose_name='المعاملة')),
            ],
            options={
                'verbose_name': 'دفعة',
                'verbose_name_plural': 'دفعات',
                'ordering': ['-date', '-created_at'],
            },
        ),
    ]
