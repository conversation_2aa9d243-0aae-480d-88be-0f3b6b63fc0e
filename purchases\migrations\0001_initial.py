# Generated by Django 5.2 on 2025-04-18 16:07

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('inventory', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='الاسم')),
                ('contact_person', models.CharField(blank=True, max_length=200, null=True, verbose_name='الشخص المسؤول')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('tax_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='الرقم الضريبي')),
                ('website', models.URLField(blank=True, null=True, verbose_name='الموقع الإلكتروني')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'مورد',
                'verbose_name_plural': 'الموردين',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Purchase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reference_number', models.CharField(default=uuid.uuid4, max_length=50, unique=True, verbose_name='رقم المرجع')),
                ('date', models.DateTimeField(verbose_name='تاريخ الشراء')),
                ('expected_delivery_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التسليم المتوقع')),
                ('actual_delivery_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التسليم الفعلي')),
                ('subtotal', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المجموع الفرعي')),
                ('tax_rate', models.DecimalField(decimal_places=2, default=15.0, max_digits=5, verbose_name='نسبة الضريبة')),
                ('tax_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='مبلغ الضريبة')),
                ('shipping_cost', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='تكلفة الشحن')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المجموع الكلي')),
                ('status', models.CharField(choices=[('pending', 'معلق'), ('received', 'تم الاستلام'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='حالة الطلب')),
                ('payment_status', models.CharField(choices=[('unpaid', 'غير مدفوع'), ('partial', 'مدفوع جزئياً'), ('paid', 'مدفوع بالكامل')], default='unpaid', max_length=20, verbose_name='حالة الدفع')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchases', to=settings.AUTH_USER_MODEL, verbose_name='الموظف')),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchases', to='purchases.supplier', verbose_name='المورد')),
            ],
            options={
                'verbose_name': 'شراء',
                'verbose_name_plural': 'المشتريات',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة')),
                ('subtotal', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المجموع الفرعي')),
                ('received_quantity', models.PositiveIntegerField(default=0, verbose_name='الكمية المستلمة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchase_items', to='inventory.product', verbose_name='المنتج')),
                ('purchase', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='purchases.purchase', verbose_name='الشراء')),
            ],
            options={
                'verbose_name': 'عنصر الشراء',
                'verbose_name_plural': 'عناصر الشراء',
            },
        ),
        migrations.CreateModel(
            name='SupplierPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ')),
                ('payment_method', models.CharField(choices=[('cash', 'نقدي'), ('bank_transfer', 'تحويل بنكي'), ('check', 'شيك'), ('credit_card', 'بطاقة ائتمان')], max_length=20, verbose_name='طريقة الدفع')),
                ('payment_date', models.DateTimeField(verbose_name='تاريخ الدفع')),
                ('reference', models.CharField(blank=True, max_length=100, null=True, verbose_name='المرجع')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('purchase', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='purchases.purchase', verbose_name='الشراء')),
            ],
            options={
                'verbose_name': 'دفعة للمورد',
                'verbose_name_plural': 'دفعات للموردين',
                'ordering': ['-payment_date'],
            },
        ),
    ]
