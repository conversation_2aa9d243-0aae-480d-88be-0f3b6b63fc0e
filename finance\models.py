from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.models import User
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType

class Account(models.Model):
    ACCOUNT_TYPE_CHOICES = (
        ('cash', _('نقدي')),
        ('bank', _('حساب بنكي')),
        ('credit_card', _('بطاقة ائتمان')),
        ('other', _('أخرى')),
    )

    name = models.CharField(_('الاسم'), max_length=100)
    account_type = models.CharField(_('نوع الحساب'), max_length=20, choices=ACCOUNT_TYPE_CHOICES)
    account_number = models.CharField(_('رقم الحساب'), max_length=50, blank=True, null=True)
    bank_name = models.CharField(_('اسم البنك'), max_length=100, blank=True, null=True)
    initial_balance = models.DecimalField(_('الرصيد الافتتاحي'), max_digits=10, decimal_places=2, default=0)
    current_balance = models.DecimalField(_('الرصيد الحالي'), max_digits=10, decimal_places=2, default=0)
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    is_active = models.BooleanField(_('نشط'), default=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('حساب')
        verbose_name_plural = _('الحسابات')
        ordering = ['name']

    def __str__(self):
        return self.name

class ExpenseCategory(models.Model):
    name = models.CharField(_('الاسم'), max_length=100)
    description = models.TextField(_('الوصف'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('فئة المصروفات')
        verbose_name_plural = _('فئات المصروفات')
        ordering = ['name']

    def __str__(self):
        return self.name

class IncomeCategory(models.Model):
    name = models.CharField(_('الاسم'), max_length=100)
    description = models.TextField(_('الوصف'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('فئة الإيرادات')
        verbose_name_plural = _('فئات الإيرادات')
        ordering = ['name']

    def __str__(self):
        return self.name

class Expense(models.Model):
    PAYMENT_METHOD_CHOICES = (
        ('cash', _('نقدي')),
        ('bank_transfer', _('تحويل بنكي')),
        ('check', _('شيك')),
        ('credit_card', _('بطاقة ائتمان')),
    )

    date = models.DateField(_('تاريخ المصروف'))
    category = models.ForeignKey(ExpenseCategory, on_delete=models.CASCADE, related_name='expenses', verbose_name=_('الفئة'))
    amount = models.DecimalField(_('المبلغ'), max_digits=10, decimal_places=2)
    description = models.TextField(_('الوصف'))
    payment_method = models.CharField(_('طريقة الدفع'), max_length=20, choices=PAYMENT_METHOD_CHOICES)
    account = models.ForeignKey(Account, on_delete=models.CASCADE, related_name='expenses', verbose_name=_('الحساب'))
    reference = models.CharField(_('المرجع'), max_length=100, blank=True, null=True)
    receipt = models.FileField(_('الإيصال'), upload_to='receipts/', blank=True, null=True)
    employee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='expenses', verbose_name=_('الموظف'))
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('مصروف')
        verbose_name_plural = _('المصروفات')
        ordering = ['-date']

    def __str__(self):
        return f"{self.date} - {self.category.name} - {self.amount}"

    def save(self, *args, **kwargs):
        # Check if this is a new expense
        is_new = self._state.adding

        # If it's a new expense, update the account balance
        if is_new:
            self.account.current_balance -= self.amount
            self.account.save(update_fields=['current_balance'])

        super().save(*args, **kwargs)

class Income(models.Model):
    PAYMENT_METHOD_CHOICES = (
        ('cash', _('نقدي')),
        ('bank_transfer', _('تحويل بنكي')),
        ('check', _('شيك')),
        ('credit_card', _('بطاقة ائتمان')),
    )

    date = models.DateField(_('تاريخ الإيراد'))
    category = models.ForeignKey(IncomeCategory, on_delete=models.CASCADE, related_name='incomes', verbose_name=_('الفئة'))
    amount = models.DecimalField(_('المبلغ'), max_digits=10, decimal_places=2)
    description = models.TextField(_('الوصف'))
    payment_method = models.CharField(_('طريقة الدفع'), max_length=20, choices=PAYMENT_METHOD_CHOICES)
    account = models.ForeignKey(Account, on_delete=models.CASCADE, related_name='incomes', verbose_name=_('الحساب'))
    reference = models.CharField(_('المرجع'), max_length=100, blank=True, null=True)
    receipt = models.FileField(_('الإيصال'), upload_to='receipts/', blank=True, null=True)
    employee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='incomes', verbose_name=_('الموظف'))
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('إيراد')
        verbose_name_plural = _('الإيرادات')
        ordering = ['-date']

    def __str__(self):
        return f"{self.date} - {self.category.name} - {self.amount}"

    def save(self, *args, **kwargs):
        # Check if this is a new income
        is_new = self._state.adding

        # If it's a new income, update the account balance
        if is_new:
            self.account.current_balance += self.amount
            self.account.save(update_fields=['current_balance'])

        super().save(*args, **kwargs)

class AccountTransfer(models.Model):
    date = models.DateField(_('تاريخ التحويل'))
    from_account = models.ForeignKey(Account, on_delete=models.CASCADE, related_name='transfers_from', verbose_name=_('من حساب'))
    to_account = models.ForeignKey(Account, on_delete=models.CASCADE, related_name='transfers_to', verbose_name=_('إلى حساب'))
    amount = models.DecimalField(_('المبلغ'), max_digits=10, decimal_places=2)
    description = models.TextField(_('الوصف'))
    reference = models.CharField(_('المرجع'), max_length=100, blank=True, null=True)
    employee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='transfers', verbose_name=_('الموظف'))
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('تحويل بين الحسابات')
        verbose_name_plural = _('تحويلات بين الحسابات')
        ordering = ['-date']

    def __str__(self):
        return f"{self.date} - {self.from_account.name} إلى {self.to_account.name} - {self.amount}"

    def save(self, *args, **kwargs):
        # Check if this is a new transfer
        is_new = self._state.adding

        # If it's a new transfer, update the account balances
        if is_new:
            self.from_account.current_balance -= self.amount
            self.to_account.current_balance += self.amount
            self.from_account.save(update_fields=['current_balance'])
            self.to_account.save(update_fields=['current_balance'])

        super().save(*args, **kwargs)

class Transaction(models.Model):
    TRANSACTION_TYPE_CHOICES = (
        ('sale', _('مبيعات')),
        ('purchase', _('مشتريات')),
        ('expense', _('مصروفات')),
        ('income', _('إيرادات')),
        ('transfer', _('تحويل')),
        ('other', _('أخرى')),
    )

    PAYMENT_STATUS_CHOICES = (
        ('paid', _('مدفوعة')),
        ('unpaid', _('غير مدفوعة')),
        ('partial', _('مدفوعة جزئياً')),
    )

    transaction_number = models.CharField(_('رقم المعاملة'), max_length=50, unique=True)
    transaction_type = models.CharField(_('نوع المعاملة'), max_length=20, choices=TRANSACTION_TYPE_CHOICES)
    date = models.DateField(_('تاريخ المعاملة'))
    amount = models.DecimalField(_('المبلغ'), max_digits=10, decimal_places=2)
    payment_status = models.CharField(_('حالة الدفع'), max_length=20, choices=PAYMENT_STATUS_CHOICES, default='unpaid')
    paid_amount = models.DecimalField(_('المبلغ المدفوع'), max_digits=10, decimal_places=2, default=0)

    # ربط المعاملة بالعميل أو المورد
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)
    related_object = GenericForeignKey('content_type', 'object_id')

    # ربط المعاملة بالكائن الأصلي (مثل فاتورة المبيعات أو المشتريات)
    source_content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, related_name='transactions', null=True, blank=True)
    source_object_id = models.PositiveIntegerField(null=True, blank=True)
    source_object = GenericForeignKey('source_content_type', 'source_object_id')

    account = models.ForeignKey(Account, on_delete=models.CASCADE, related_name='transactions', verbose_name=_('الحساب'))
    description = models.TextField(_('الوصف'), blank=True, null=True)
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='transactions', verbose_name=_('أنشئت بواسطة'))
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('معاملة مالية')
        verbose_name_plural = _('معاملات مالية')
        ordering = ['-date', '-created_at']

    def __str__(self):
        return f"{self.transaction_number} - {self.get_transaction_type_display()} - {self.amount}"

    @property
    def remaining_amount(self):
        return self.amount - self.paid_amount

    def update_payment_status(self):
        if self.paid_amount >= self.amount:
            self.payment_status = 'paid'
        elif self.paid_amount > 0:
            self.payment_status = 'partial'
        else:
            self.payment_status = 'unpaid'
        self.save(update_fields=['payment_status'])

class Payment(models.Model):
    PAYMENT_METHOD_CHOICES = (
        ('cash', _('نقدي')),
        ('bank_transfer', _('تحويل بنكي')),
        ('check', _('شيك')),
        ('credit_card', _('بطاقة ائتمان')),
    )

    transaction = models.ForeignKey(Transaction, on_delete=models.CASCADE, related_name='payments', verbose_name=_('المعاملة'))
    date = models.DateField(_('تاريخ الدفع'))
    amount = models.DecimalField(_('المبلغ'), max_digits=10, decimal_places=2)
    payment_method = models.CharField(_('طريقة الدفع'), max_length=20, choices=PAYMENT_METHOD_CHOICES)
    account = models.ForeignKey(Account, on_delete=models.CASCADE, related_name='payments', verbose_name=_('الحساب'))
    reference = models.CharField(_('المرجع'), max_length=100, blank=True, null=True)
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='payments', verbose_name=_('أنشئت بواسطة'))
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('دفعة')
        verbose_name_plural = _('دفعات')
        ordering = ['-date', '-created_at']

    def __str__(self):
        return f"{self.date} - {self.transaction.transaction_number} - {self.amount}"

    def save(self, *args, **kwargs):
        # Check if this is a new payment
        is_new = self._state.adding

        super().save(*args, **kwargs)

        # Update transaction payment status
        if is_new:
            # Update transaction paid amount
            self.transaction.paid_amount += self.amount
            self.transaction.save(update_fields=['paid_amount'])
            self.transaction.update_payment_status()
