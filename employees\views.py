from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.models import User
from django.http import HttpResponse, JsonResponse
from django.views.decorators.http import require_POST
from django.utils import timezone
import json
import csv
import io
import xlwt
from datetime import datetime

from .models import EmployeeProfile, Department, Position, Attendance, Leave, Role, CustomPermission, EmployeeAlert, AttendanceSettings, PerformanceReview

@login_required
def index(request):
    # الحصول على معلمة القسم من الاستعلام
    department_id = request.GET.get('department')

    # الحصول على جميع الموظفين
    employees = EmployeeProfile.objects.all().order_by('user__first_name', 'user__last_name')

    # تطبيق التصفية حسب القسم إذا تم تحديده
    if department_id:
        try:
            department = Department.objects.get(id=department_id)
            employees = employees.filter(position__department=department)
        except Department.DoesNotExist:
            pass

    # الحصول على جميع الأقسام
    departments = Department.objects.all()

    # إحصائيات سريعة
    active_employees_count = EmployeeProfile.objects.filter(is_active=True).count()

    # الموظفين الجدد في هذا الشهر
    from django.utils import timezone
    current_month = timezone.now().month
    current_year = timezone.now().year
    new_employees_count = EmployeeProfile.objects.filter(
        created_at__month=current_month,
        created_at__year=current_year
    ).count()

    # الإجازات الحالية
    today = timezone.now().date()
    current_leaves_count = Leave.objects.filter(
        start_date__lte=today,
        end_date__gte=today,
        status='approved'
    ).count()

    context = {
        'employees': employees,
        'departments': departments,
        'active_employees_count': active_employees_count,
        'new_employees_count': new_employees_count,
        'current_leaves_count': current_leaves_count,
    }
    return render(request, 'employees/index.html', context)

@login_required
def add_employee(request):
    departments = Department.objects.all()
    positions = Position.objects.all()
    roles = Role.objects.filter(is_active=True).order_by('name')

    if request.method == 'POST':
        try:
            # Get form data
            first_name = request.POST.get('first_name')
            last_name = request.POST.get('last_name')
            username = request.POST.get('username')
            email = request.POST.get('email')
            password = request.POST.get('password')
            password_confirm = request.POST.get('password_confirm')
            employee_id = request.POST.get('employee_id')
            phone = request.POST.get('phone')
            position_id = request.POST.get('position')
            role_id = request.POST.get('role')
            gender = request.POST.get('gender')
            date_of_birth = request.POST.get('date_of_birth') or None
            hire_date = request.POST.get('hire_date')
            salary = request.POST.get('salary')
            national_id = request.POST.get('national_id')
            address = request.POST.get('address')
            notes = request.POST.get('notes')

            # Validate required fields
            if not all([first_name, last_name, username, email, password, password_confirm,
                       employee_id, phone, position_id, gender, hire_date, salary]):
                messages.error(request, _('جميع الحقول المطلوبة يجب ملؤها'))
                return redirect('employees:add_employee')

            # Validate password confirmation
            if password != password_confirm:
                messages.error(request, _('كلمة المرور وتأكيدها غير متطابقين'))
                return redirect('employees:add_employee')

            # Check if username already exists
            if User.objects.filter(username=username).exists():
                messages.error(request, _('اسم المستخدم موجود بالفعل'))
                return redirect('employees:add_employee')

            # Check if employee_id already exists
            if EmployeeProfile.objects.filter(employee_id=employee_id).exists():
                messages.error(request, _('رقم الموظف موجود بالفعل'))
                return redirect('employees:add_employee')

            # Create user account
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password,
                first_name=first_name,
                last_name=last_name
            )

            # Get position and role
            position = Position.objects.get(id=position_id)
            role = Role.objects.get(id=role_id) if role_id else None

            # Create employee profile
            employee = EmployeeProfile.objects.create(
                user=user,
                employee_id=employee_id,
                position=position,
                role=role,
                phone=phone,
                address=address,
                date_of_birth=date_of_birth,
                gender=gender,
                national_id=national_id,
                hire_date=hire_date,
                salary=salary,
                notes=notes
            )

            # Handle image upload
            if 'image' in request.FILES:
                employee.image = request.FILES['image']
                employee.save()

            messages.success(request, _('تم إضافة الموظف بنجاح'))
            return redirect('employees:index')

        except Exception as e:
            messages.error(request, _('حدث خطأ أثناء إضافة الموظف: ') + str(e))

    context = {
        'departments': departments,
        'positions': positions,
        'roles': roles,
    }
    return render(request, 'employees/add_employee.html', context)

@login_required
def edit_employee(request, employee_id):
    employee = get_object_or_404(EmployeeProfile, id=employee_id)
    departments = Department.objects.all()
    positions = Position.objects.all()
    roles = Role.objects.filter(is_active=True).order_by('name')

    if request.method == 'POST':
        try:
            # Get form data
            first_name = request.POST.get('first_name')
            last_name = request.POST.get('last_name')
            email = request.POST.get('email')
            phone = request.POST.get('phone')
            position_id = request.POST.get('position')
            role_id = request.POST.get('role')
            gender = request.POST.get('gender')
            date_of_birth = request.POST.get('date_of_birth') or None
            hire_date = request.POST.get('hire_date')
            salary = request.POST.get('salary')
            national_id = request.POST.get('national_id')
            address = request.POST.get('address')
            notes = request.POST.get('notes')
            is_active = request.POST.get('is_active') == 'True'

            # Validate required fields
            if not all([first_name, last_name, email, phone, position_id, gender, hire_date, salary]):
                messages.error(request, _('جميع الحقول المطلوبة يجب ملؤها'))
                return redirect('employees:edit_employee', employee_id=employee_id)

            # Update user information
            user = employee.user
            user.first_name = first_name
            user.last_name = last_name
            user.email = email
            user.save()

            # Get position and role
            position = Position.objects.get(id=position_id)
            role = Role.objects.get(id=role_id) if role_id else None

            # Update employee profile
            employee.position = position
            employee.role = role
            employee.phone = phone
            employee.address = address
            employee.date_of_birth = date_of_birth
            employee.gender = gender
            employee.national_id = national_id
            employee.hire_date = hire_date
            employee.salary = salary
            employee.notes = notes
            employee.is_active = is_active

            # Handle image upload
            if 'image' in request.FILES:
                employee.image = request.FILES['image']

            employee.save()

            messages.success(request, _('تم تحديث بيانات الموظف بنجاح'))
            return redirect('employees:view_employee', employee_id=employee_id)

        except Exception as e:
            messages.error(request, _('حدث خطأ أثناء تحديث بيانات الموظف: ') + str(e))
            return redirect('employees:edit_employee', employee_id=employee_id)

    context = {
        'employee': employee,
        'departments': departments,
        'positions': positions,
        'roles': roles,
    }
    return render(request, 'employees/edit_employee.html', context)

@login_required
def view_employee(request, employee_id):
    employee = get_object_or_404(EmployeeProfile, id=employee_id)
    attendance = employee.attendance.all().order_by('-date')[:10]
    leaves = employee.leaves.all().order_by('-start_date')[:10]

    context = {
        'employee': employee,
        'attendance': attendance,
        'leaves': leaves,
    }
    return render(request, 'employees/view_employee.html', context)

@login_required
def delete_employee(request, employee_id):
    employee = get_object_or_404(EmployeeProfile, id=employee_id)

    if request.method == 'POST':
        # Delete user account as well
        user = employee.user
        employee.delete()
        user.delete()

        messages.success(request, _('تم حذف الموظف بنجاح'))
        return redirect('employees:index')

    context = {
        'employee': employee,
    }
    return render(request, 'employees/delete_employee.html', context)

@login_required
def attendance(request):
    employees = EmployeeProfile.objects.filter(is_active=True).order_by('user__first_name', 'user__last_name')

    from django.utils import timezone
    today = timezone.now().date()

    context = {
        'employees': employees,
        'today': today,
    }
    return render(request, 'employees/attendance.html', context)

@login_required
def roles(request):
    departments = Department.objects.all().order_by('name')
    positions = Position.objects.all().order_by('name')

    context = {
        'departments': departments,
        'positions': positions,
    }
    return render(request, 'employees/roles.html', context)


@login_required
def roles_permissions(request):
    """
    عرض صفحة إدارة الأدوار والصلاحيات
    """
    # الحصول على جميع الأدوار
    roles = Role.objects.all().order_by('name')

    # الحصول على جميع الصلاحيات مصنفة حسب الفئة
    permissions = CustomPermission.objects.all().order_by('category', 'name')

    # تنظيم الصلاحيات حسب الفئة
    permissions_by_category = {}
    for permission in permissions:
        category = permission.category
        if category not in permissions_by_category:
            permissions_by_category[category] = []
        permissions_by_category[category].append(permission)

    context = {
        'roles': roles,
        'permissions_by_category': permissions_by_category,
    }
    return render(request, 'employees/roles_permissions.html', context)


@login_required
def export_employees(request):
    """
    تصدير بيانات الموظفين إلى ملف Excel
    """
    response = HttpResponse(content_type='application/ms-excel')
    response['Content-Disposition'] = 'attachment; filename="employees.xls"'

    wb = xlwt.Workbook(encoding='utf-8')
    ws = wb.add_sheet('Employees')

    # أنماط الخلايا
    header_style = xlwt.easyxf('font: bold on, color black; align: horiz center; pattern: pattern solid, fore_colour gray25;')
    date_style = xlwt.easyxf('font: bold off; align: horiz center;', num_format_str='yyyy-mm-dd')
    cell_style = xlwt.easyxf('font: bold off; align: horiz center;')

    # عناوين الأعمدة
    columns = [
        _('رقم الموظف'), _('الاسم الأول'), _('الاسم الأخير'),
        _('البريد الإلكتروني'), _('رقم الهاتف'), _('القسم'),
        _('المنصب'), _('تاريخ التوظيف'), _('الراتب'),
        _('الحالة')
    ]

    # كتابة العناوين
    for col_num, column_title in enumerate(columns):
        ws.write(0, col_num, column_title, header_style)
        ws.col(col_num).width = 256 * 20  # عرض العمود

    # الحصول على بيانات الموظفين
    employees = EmployeeProfile.objects.all().order_by('user__first_name', 'user__last_name')

    # كتابة البيانات
    for row_num, employee in enumerate(employees, 1):
        ws.write(row_num, 0, employee.employee_id, cell_style)
        ws.write(row_num, 1, employee.user.first_name, cell_style)
        ws.write(row_num, 2, employee.user.last_name, cell_style)
        ws.write(row_num, 3, employee.user.email, cell_style)
        ws.write(row_num, 4, employee.phone, cell_style)
        ws.write(row_num, 5, employee.department.name if employee.department else "-", cell_style)
        ws.write(row_num, 6, employee.position.name if employee.position else "-", cell_style)
        ws.write(row_num, 7, employee.hire_date.strftime('%Y-%m-%d'), date_style)
        ws.write(row_num, 8, float(employee.salary), cell_style)
        ws.write(row_num, 9, _('نشط') if employee.is_active else _('غير نشط'), cell_style)

    wb.save(response)
    return response


@login_required
def download_template(request):
    """
    تنزيل قالب لاستيراد الموظفين
    """
    response = HttpResponse(content_type='application/ms-excel')
    response['Content-Disposition'] = 'attachment; filename="employees_template.xls"'

    wb = xlwt.Workbook(encoding='utf-8')
    ws = wb.add_sheet('Template')

    # أنماط الخلايا
    header_style = xlwt.easyxf('font: bold on, color black; align: horiz center; pattern: pattern solid, fore_colour gray25;')

    # عناوين الأعمدة
    columns = [
        _('الاسم الأول'), _('الاسم الأخير'), _('اسم المستخدم'),
        _('البريد الإلكتروني'), _('كلمة المرور'), _('رقم الموظف'),
        _('رقم الهاتف'), _('القسم'), _('المنصب'),
        _('الجنس'), _('تاريخ التوظيف'), _('الراتب')
    ]

    # كتابة العناوين
    for col_num, column_title in enumerate(columns):
        ws.write(0, col_num, column_title, header_style)
        ws.col(col_num).width = 256 * 20  # عرض العمود

    # كتابة ملاحظات للمستخدم
    ws.write(1, 7, _('اسم القسم'))
    ws.write(1, 8, _('اسم المنصب'))
    ws.write(1, 9, _('male/female'))
    ws.write(1, 10, _('YYYY-MM-DD'))

    wb.save(response)
    return response


@login_required
@require_POST
def import_employees(request):
    """
    استيراد بيانات الموظفين من ملف Excel أو CSV
    """
    if 'import_file' not in request.FILES:
        messages.error(request, _('يرجى تحديد ملف للاستيراد'))
        return redirect('employees:index')

    import_file = request.FILES['import_file']

    # هنا يجب إضافة المنطق الخاص بقراءة الملف ومعالجته
    # هذا مجرد مثال بسيط
    try:
        # للتبسيط، نفترض أن الملف هو CSV
        success_count = 0
        error_count = 0


        messages.success(request, _(f'تم استيراد {success_count} موظف بنجاح. فشل استيراد {error_count} موظف.'))
    except Exception as e:
        messages.error(request, _(f'حدث خطأ أثناء استيراد البيانات: {str(e)}'))

    return redirect('employees:index')


@login_required
@require_POST
def reset_password(request):
    """
    إعادة تعيين كلمة مرور الموظف
    """
    try:
        data = json.loads(request.body)
        employee_id = data.get('employee_id')
        new_password = data.get('new_password')

        if not employee_id or not new_password:
            return JsonResponse({'status': 'error', 'message': _('البيانات غير مكتملة')}, status=400)

        employee = get_object_or_404(EmployeeProfile, id=employee_id)
        user = employee.user
        user.set_password(new_password)
        user.save()

        return JsonResponse({'status': 'success', 'message': _('تم إعادة تعيين كلمة المرور بنجاح')})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)


@login_required
@require_POST
def toggle_status(request):
    """
    تبديل حالة الموظف (نشط/غير نشط)
    """
    try:
        data = json.loads(request.body)
        employee_id = data.get('employee_id')
        status = data.get('status')

        if employee_id is None or status is None:
            return JsonResponse({'status': 'error', 'message': _('البيانات غير مكتملة')}, status=400)

        employee = get_object_or_404(EmployeeProfile, id=employee_id)
        employee.is_active = status
        employee.save()

        status_text = _('نشط') if status else _('غير نشط')
        return JsonResponse({'status': 'success', 'message': _(f'تم تغيير حالة الموظف إلى {status_text} بنجاح')})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)


@login_required
@require_POST
def bulk_delete(request):
    """
    حذف مجموعة من الموظفين
    """
    try:
        data = json.loads(request.body)
        employee_ids = data.get('employee_ids', [])

        if not employee_ids:
            return JsonResponse({'status': 'error', 'message': _('لم يتم تحديد أي موظف')}, status=400)

        deleted_count = 0
        for employee_id in employee_ids:
            try:
                employee = EmployeeProfile.objects.get(id=employee_id)
                user = employee.user
                employee.delete()
                user.delete()
                deleted_count += 1
            except EmployeeProfile.DoesNotExist:
                continue

        return JsonResponse({'status': 'success', 'message': _(f'تم حذف {deleted_count} موظف بنجاح')})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)


@login_required
@require_POST
def bulk_activate(request):
    """
    تفعيل مجموعة من الموظفين
    """
    try:
        data = json.loads(request.body)
        employee_ids = data.get('employee_ids', [])

        if not employee_ids:
            return JsonResponse({'status': 'error', 'message': _('لم يتم تحديد أي موظف')}, status=400)

        activated_count = 0
        for employee_id in employee_ids:
            try:
                employee = EmployeeProfile.objects.get(id=employee_id)
                if not employee.is_active:
                    employee.is_active = True
                    employee.save()
                    activated_count += 1
            except EmployeeProfile.DoesNotExist:
                continue

        return JsonResponse({'status': 'success', 'message': _(f'تم تفعيل {activated_count} موظف بنجاح')})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)


@login_required
@require_POST
def bulk_deactivate(request):
    """
    تعطيل مجموعة من الموظفين
    """
    try:
        data = json.loads(request.body)
        employee_ids = data.get('employee_ids', [])

        if not employee_ids:
            return JsonResponse({'status': 'error', 'message': _('لم يتم تحديد أي موظف')}, status=400)

        deactivated_count = 0
        for employee_id in employee_ids:
            try:
                employee = EmployeeProfile.objects.get(id=employee_id)
                if employee.is_active:
                    employee.is_active = False
                    employee.save()
                    deactivated_count += 1
            except EmployeeProfile.DoesNotExist:
                continue

        return JsonResponse({'status': 'success', 'message': _(f'تم تعطيل {deactivated_count} موظف بنجاح')})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)
