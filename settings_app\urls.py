from django.urls import path
from . import views

app_name = 'settings_app'

urlpatterns = [
    path('', views.index, name='index'),
    path('profile/', views.profile, name='profile'),
    path('general/', views.general_settings, name='general_settings'),
    path('users/', views.users, name='users'),
    path('users/add/', views.add_user, name='add_user'),
    path('users/edit/<int:user_id>/', views.edit_user, name='edit_user'),
    path('users/delete/<int:user_id>/', views.delete_user, name='delete_user'),
    path('backup/', views.backup, name='backup'),
    path('restore/', views.restore, name='restore'),

    # Currency settings
    path('currency/', views.currency_settings, name='currency_settings'),
    path('currency/delete/<int:currency_id>/', views.delete_currency, name='delete_currency'),

    # Tax settings
    path('tax/', views.tax_settings, name='tax_settings'),
    path('tax/delete/<int:tax_id>/', views.delete_tax, name='delete_tax'),

    # Invoice settings
    path('invoice/', views.invoice_settings, name='invoice_settings'),

    # Email settings
    path('email/', views.email_settings, name='email_settings'),

    # Backup settings
    path('backup-settings/', views.backup_settings, name='backup_settings'),

    # Language settings
    path('language/', views.language_settings, name='language_settings'),
    path('language/delete/<int:language_id>/', views.delete_language, name='delete_language'),

    # Notification settings
    path('notifications/', views.notification_settings, name='notification_settings'),

    # Backup logs
    path('backup-logs/', views.backup_logs, name='backup_logs'),
    path('export-logs/', views.export_logs, name='export_logs'),
]
