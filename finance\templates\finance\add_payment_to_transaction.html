{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "إضافة دفعة" %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">{% trans "إضافة دفعة للمعاملة" %} #{{ transaction.transaction_number }}</h5>
                </div>
                <div class="card-body">
                    <!-- معلومات المعاملة -->
                    <div class="alert alert-info mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>{% trans "النوع:" %}</strong> {{ transaction.get_transaction_type_display }}</p>
                                <p><strong>{% trans "التاريخ:" %}</strong> {{ transaction.date }}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>{% trans "المبلغ الإجمالي:" %}</strong> {{ transaction.amount }} {% trans "د.م" %}</p>
                                <p><strong>{% trans "المبلغ المدفوع:" %}</strong> {{ transaction.paid_amount }} {% trans "د.م" %}</p>
                                <p><strong>{% trans "المبلغ المتبقي:" %}</strong> {{ transaction.remaining_amount }} {% trans "د.م" %}</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- نموذج إضافة دفعة -->
                    <form method="post" action="{% url 'finance:add_payment_to_transaction' transaction.id %}">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="date">{% trans "تاريخ الدفع" %} <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="date" name="date" value="{{ today|date:'Y-m-d' }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="amount">{% trans "المبلغ" %} <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" step="0.01" min="0.01" max="{{ transaction.remaining_amount }}" class="form-control" id="amount" name="amount" value="{{ transaction.remaining_amount }}" required>
                                        <div class="input-group-append">
                                            <span class="input-group-text">{% trans "د.م" %}</span>
                                        </div>
                                    </div>
                                    <small class="form-text text-muted">{% trans "الحد الأقصى هو المبلغ المتبقي" %}</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="payment_method">{% trans "طريقة الدفع" %} <span class="text-danger">*</span></label>
                                    <select class="form-control" id="payment_method" name="payment_method" required>
                                        <option value="">{% trans "اختر طريقة الدفع" %}</option>
                                        <option value="cash">{% trans "نقدي" %}</option>
                                        <option value="bank_transfer">{% trans "تحويل بنكي" %}</option>
                                        <option value="check">{% trans "شيك" %}</option>
                                        <option value="credit_card">{% trans "بطاقة ائتمان" %}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="account">{% trans "الحساب" %} <span class="text-danger">*</span></label>
                                    <select class="form-control" id="account" name="account" required>
                                        <option value="">{% trans "اختر الحساب" %}</option>
                                        {% for account in accounts %}
                                        <option value="{{ account.id }}" {% if account.id == transaction.account.id %}selected{% endif %}>{{ account.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="reference">{% trans "المرجع" %}</label>
                            <input type="text" class="form-control" id="reference" name="reference" placeholder="{% trans 'رقم الشيك أو التحويل' %}">
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="notes">{% trans "ملاحظات" %}</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>
                        
                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-success">{% trans "تسجيل الدفعة" %}</button>
                            <a href="{% url 'finance:transaction_detail' transaction.id %}" class="btn btn-secondary">{% trans "إلغاء" %}</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // تركيز تلقائي على حقل المبلغ
        $('#amount').focus();
    });
</script>
{% endblock %}
