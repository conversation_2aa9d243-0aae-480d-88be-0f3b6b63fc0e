{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "إدارة الموردين" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<style>
    .supplier-card {
        transition: all 0.3s;
        height: 100%;
    }

    .supplier-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .supplier-card .card-header {
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
    }

    .supplier-card .card-footer {
        background-color: #f8f9fc;
        border-top: 1px solid #e3e6f0;
    }

    .supplier-badge {
        position: absolute;
        top: 10px;
        left: 10px;
    }

    .search-box {
        position: relative;
    }

    .search-box .search-icon {
        position: absolute;
        top: 10px;
        right: 10px;
        color: #d1d3e2;
    }

    .search-box .form-control {
        padding-right: 2.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "إدارة الموردين" %}</h1>
    <div>
        <a href="{% url 'purchases:supplier_categories' %}" class="btn btn-info me-2">
            <i class="fas fa-tags me-1"></i> {% trans "فئات الموردين" %}
        </a>
        <a href="{% url 'purchases:add_supplier' %}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> {% trans "إضافة مورد جديد" %}
        </a>
    </div>
</div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<!-- Search Box -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "بحث عن مورد" %}</h6>
    </div>
    <div class="card-body">
        <form action="{% url 'purchases:search_suppliers' %}" method="get">
            <div class="row">
                <div class="col-md-8 mb-3">
                    <div class="search-box">
                        <input type="text" class="form-control" name="q" placeholder="{% trans 'بحث بالاسم، رقم الهاتف، البريد الإلكتروني...' %}" value="{{ query|default:'' }}">
                        <span class="search-icon"><i class="fas fa-search"></i></span>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-1"></i> {% trans "بحث" %}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Suppliers List -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "قائمة الموردين" %}</h6>
        <div class="dropdown no-arrow">
            <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
            </a>
            <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                <div class="dropdown-header">{% trans "خيارات العرض" %}:</div>
                <a class="dropdown-item view-mode" href="#" data-mode="table">
                    <i class="fas fa-table me-1"></i> {% trans "عرض كجدول" %}
                </a>
                <a class="dropdown-item view-mode" href="#" data-mode="cards">
                    <i class="fas fa-th-large me-1"></i> {% trans "عرض كبطاقات" %}
                </a>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="{% url 'purchases:reports' %}">
                    <i class="fas fa-file-export me-1"></i> {% trans "تصدير" %}
                </a>
            </div>
        </div>
    </div>
    <div class="card-body">
        <!-- Table View (Default) -->
        <div id="tableView">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="suppliersTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>{% trans "الاسم" %}</th>
                            <th>{% trans "الفئة" %}</th>
                            <th>{% trans "رقم الهاتف" %}</th>
                            <th>{% trans "البريد الإلكتروني" %}</th>
                            <th>{% trans "العنوان" %}</th>
                            <th>{% trans "الحالة" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for supplier in suppliers %}
                        <tr>
                            <td>{{ supplier.name }}</td>
                            <td>{{ supplier.category.name|default:"-" }}</td>
                            <td>{{ supplier.phone }}</td>
                            <td>{{ supplier.email|default:"-" }}</td>
                            <td>{{ supplier.address|default:"-" }}</td>
                            <td>
                                {% if supplier.is_active %}
                                <span class="badge bg-success">{% trans "نشط" %}</span>
                                {% else %}
                                <span class="badge bg-danger">{% trans "غير نشط" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{% url 'purchases:view_supplier' supplier_id=supplier.id %}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'purchases:edit_supplier' supplier_id=supplier.id %}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'purchases:delete_supplier' supplier_id=supplier.id %}" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Cards View (Hidden by default) -->
        <div id="cardsView" class="d-none">
            <div class="row">
                {% for supplier in suppliers %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card supplier-card">
                        {% if not supplier.is_active %}
                        <span class="supplier-badge badge bg-danger">{% trans "غير نشط" %}</span>
                        {% endif %}
                        <div class="card-header">
                            <h5 class="card-title mb-0">{{ supplier.name }}</h5>
                            {% if supplier.category %}
                            <span class="badge bg-info">{{ supplier.category.name }}</span>
                            {% endif %}
                        </div>
                        <div class="card-body">
                            <p class="card-text">
                                <i class="fas fa-phone me-2 text-primary"></i> {{ supplier.phone }}<br>
                                {% if supplier.email %}
                                <i class="fas fa-envelope me-2 text-primary"></i> {{ supplier.email }}<br>
                                {% endif %}
                                {% if supplier.address %}
                                <i class="fas fa-map-marker-alt me-2 text-primary"></i> {{ supplier.address }}<br>
                                {% endif %}
                                {% if supplier.contact_person %}
                                <i class="fas fa-user me-2 text-primary"></i> {{ supplier.contact_person }}
                                {% endif %}
                            </p>
                        </div>
                        <div class="card-footer">
                            <div class="btn-group w-100">
                                <a href="{% url 'purchases:view_supplier' supplier_id=supplier.id %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye me-1"></i> {% trans "عرض" %}
                                </a>
                                <a href="{% url 'purchases:edit_supplier' supplier_id=supplier.id %}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit me-1"></i> {% trans "تعديل" %}
                                </a>
                                <a href="{% url 'purchases:delete_supplier' supplier_id=supplier.id %}" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash me-1"></i> {% trans "حذف" %}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <div class="alert alert-info">
                        {% trans "لا يوجد موردين. قم بإضافة مورد جديد." %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $('#suppliersTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "order": [[0, "asc"]],
            "pageLength": 10,
            "lengthMenu": [[5, 10, 15, 20, 25, 50, -1], [5, 10, 15, 20, 25, 50, "الكل"]],
            "dom": 'lfrtip'
        });

        // Toggle view mode
        $('.view-mode').click(function(e) {
            e.preventDefault();
            var mode = $(this).data('mode');

            if (mode === 'table') {
                $('#tableView').removeClass('d-none');
                $('#cardsView').addClass('d-none');
            } else if (mode === 'cards') {
                $('#tableView').addClass('d-none');
                $('#cardsView').removeClass('d-none');
            }
        });
    });
</script>
{% endblock %}
