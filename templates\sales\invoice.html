{% extends 'invoice_base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "فاتورة" %} #{{ sale.invoice_number }} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/invoice-print.css' %}">
<link rel="stylesheet" href="{% static 'css/thermal-receipt.css' %}">
<link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
<style>
    /* أنماط للطباعة */
    @media print {
        body {
            background-color: white;
            font-size: 12pt;
        }
        .no-print {
            display: none !important;
        }
        .invoice-container {
            border: none !important;
            box-shadow: none !important;
            padding: 0 !important;
        }
    }
    
    /* أنماط للإشعارات والتأثيرات الحركية */
    .toast-notification {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background-color: var(--primary-color);
        color: white;
        padding: 12px 20px;
        border-radius: 50px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 9999;
        opacity: 0;
        transform: translateY(20px);
        transition: opacity 0.3s ease, transform 0.3s ease;
    }
    
    .toast-notification.show {
        opacity: 1;
        transform: translateY(0);
    }
    
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;
    }
    
    .loading-overlay.show {
        opacity: 1;
        visibility: visible;
    }
    
    .spinner {
        width: 50px;
        height: 50px;
        border: 5px solid var(--light-bg);
        border-top: 5px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    /* تحسينات إضافية للفاتورة */
    .invoice-container {
        transition: all 0.5s ease;
    }
    
    .table-invoice tbody tr {
        transition: background-color 0.3s ease;
    }
    
    .action-buttons .btn {
        transition: all 0.3s ease;
    }
    
    .action-buttons .btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
</style>
<style>
    :root {
        --primary-color: #3a5998;
        --secondary-color: #4CAF50;
        --accent-color: #FFC107;
        --light-bg: #f8f9fa;
        --border-radius: 12px;
        --box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
        --border-color: #e3e6f0;
    }

    body {
        font-family: 'Tajawal', sans-serif;
    }

    .invoice-container {
        background-color: #fff;
        padding: 40px;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        transition: all 0.3s ease;
        border: 1px solid var(--border-color);
    }

    .invoice-header {
        margin-bottom: 40px;
        position: relative;
    }

    .invoice-header::after {
        content: '';
        position: absolute;
        bottom: -20px;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(to right, var(--primary-color), transparent);
    }

    .invoice-logo {
        max-height: 100px;
        transition: transform 0.3s ease;
    }

    .invoice-logo:hover {
        transform: scale(1.05);
    }

    .invoice-title {
        font-size: 1.8rem;
        font-weight: 700;
        color: var(--primary-color);
        text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    }

    .invoice-number {
        font-size: 2.2rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 15px;
        display: inline-block;
        position: relative;
    }

    .invoice-number::after {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 0;
        width: 50px;
        height: 3px;
        background-color: var(--accent-color);
    }

    .invoice-details {
        margin-top: 25px;
        padding: 20px;
        background-color: var(--light-bg);
        border-radius: var(--border-radius);
        border-left: 4px solid var(--primary-color);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .invoice-details dt {
        font-weight: 700;
        color: var(--primary-color);
    }

    .invoice-details dd {
        margin-bottom: 0.8rem;
    }

    .invoice-items {
        margin-top: 40px;
    }

    .table-invoice {
        border-collapse: separate;
        border-spacing: 0;
        width: 100%;
    }

    .table-invoice thead th {
        background-color: var(--primary-color);
        color: white;
        padding: 12px;
        font-weight: 500;
        text-align: center;
        border: none;
    }

    .table-invoice thead th:first-child {
        border-top-right-radius: 10px;
    }

    .table-invoice thead th:last-child {
        border-top-left-radius: 10px;
    }

    .table-invoice tbody tr:nth-child(even) {
        background-color: rgba(0,0,0,0.02);
    }

    .table-invoice tbody tr:hover {
        background-color: rgba(0,0,0,0.05);
    }

    .table-invoice td {
        padding: 12px;
        vertical-align: middle;
        border-top: 1px solid var(--border-color);
    }

    .invoice-summary {
        margin-top: 40px;
        background-color: var(--light-bg);
        padding: 25px;
        border-radius: var(--border-radius);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border-right: 4px solid var(--secondary-color);
    }

    .total-amount {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--secondary-color);
    }

    .invoice-footer {
        margin-top: 40px;
        padding-top: 25px;
        border-top: 2px dashed var(--border-color);
    }

    .payment-info {
        margin-top: 30px;
        padding: 20px;
        background-color: var(--light-bg);
        border-radius: var(--border-radius);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border-right: 4px solid var(--accent-color);
    }

    .payment-status {
        display: inline-block;
        padding: 8px 15px;
        border-radius: 30px;
        font-weight: 700;
        text-transform: uppercase;
        font-size: 0.85rem;
    }

    .payment-status.paid {
        background-color: rgba(76, 175, 80, 0.2);
        color: #2E7D32;
    }

    .payment-status.unpaid {
        background-color: rgba(244, 67, 54, 0.2);
        color: #C62828;
    }

    .payment-status.partial {
        background-color: rgba(255, 193, 7, 0.2);
        color: #F57F17;
    }

    .qr-code {
        max-width: 150px;
        margin: 0 auto;
        padding: 10px;
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .signature-area {
        margin-top: 60px;
        border-top: 1px dashed var(--border-color);
        padding-top: 25px;
        display: flex;
        justify-content: space-between;
    }

    .signature-box {
        text-align: center;
        width: 200px;
    }

    .signature-line {
        border-top: 1px solid #000;
        margin-top: 40px;
        margin-bottom: 10px;
    }

    .terms-conditions {
        font-size: 0.9rem;
        margin-top: 40px;
        padding: 15px;
        background-color: rgba(0,0,0,0.02);
        border-radius: var(--border-radius);
        border: 1px dashed var(--border-color);
    }

    /* أزرار الإجراءات */
    .action-buttons .btn {
        border-radius: 50px;
        padding: 8px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .action-buttons .btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .action-buttons .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .action-buttons .btn-success {
        background-color: var(--secondary-color);
        border-color: var(--secondary-color);
    }

    .action-buttons .btn-info {
        background-color: #17a2b8;
        border-color: #17a2b8;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4 no-print">
    <div>
        <h1 class="h3 mb-0 text-gray-800">{% trans "فاتورة" %} #{{ sale.invoice_number }}</h1>
        <p class="text-muted"><i class="far fa-calendar-alt me-1"></i> {% trans "تاريخ الإصدار:" %} {{ sale.date|date:"Y-m-d" }}</p>
    </div>
    <div class="action-buttons">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-primary" onclick="window.print();">
                <i class="fas fa-print me-1"></i> {% trans "طباعة" %}
            </button>
            <button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                <span class="visually-hidden">{% trans "خيارات الطباعة" %}</span>
            </button>
            <ul class="dropdown-menu dropdown-menu-end shadow">
                <li><h6 class="dropdown-header">{% trans "حجم الورق" %}</h6></li>
                <li><a class="dropdown-item" href="#" data-paper-size="a4">{% trans "A4" %}</a></li>
                <li><a class="dropdown-item" href="#" data-paper-size="a5">{% trans "A5" %}</a></li>
                <li><a class="dropdown-item" href="#" data-paper-size="thermal">{% trans "إيصال حراري (80مم)" %}</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><h6 class="dropdown-header">{% trans "محتوى الفاتورة" %}</h6></li>
                <li>
                    <div class="dropdown-item">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="show_logo_print" {% if invoice_settings.show_logo %}checked{% endif %}>
                            <label class="form-check-label" for="show_logo_print">{% trans "إظهار الشعار" %}</label>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="dropdown-item">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="show_customer_info_print" {% if invoice_settings.show_customer_info %}checked{% endif %}>
                            <label class="form-check-label" for="show_customer_info_print">{% trans "معلومات العميل" %}</label>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="dropdown-item">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="show_signature_print" {% if invoice_settings.show_signature %}checked{% endif %}>
                            <label class="form-check-label" for="show_signature_print">{% trans "منطقة التوقيع" %}</label>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="dropdown-item">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="show_terms_print" {% if invoice_settings.show_terms %}checked{% endif %}>
                            <label class="form-check-label" for="show_terms_print">{% trans "الشروط والأحكام" %}</label>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="dropdown-item">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="show_qr_print" {% if invoice_settings.show_qr_code %}checked{% endif %}>
                            <label class="form-check-label" for="show_qr_print">{% trans "رمز QR" %}</label>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="dropdown-item">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="show_payment_info_print" {% if invoice_settings.show_payment_info %}checked{% endif %}>
                            <label class="form-check-label" for="show_payment_info_print">{% trans "معلومات الدفع" %}</label>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="dropdown-item">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="show_company_info_print" {% if invoice_settings.show_company_info %}checked{% endif %}>
                            <label class="form-check-label" for="show_company_info_print">{% trans "معلومات الشركة" %}</label>
                        </div>
                    </div>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                    <a class="dropdown-item text-primary" href="{% url 'settings_app:invoice_settings' %}">
                        <i class="fas fa-cog me-1"></i> {% trans "إعدادات الفاتورة" %}
                    </a>
                </li>
            </ul>
        </div>
        <button type="button" class="btn btn-success me-2" id="downloadPdfBtn">
            <i class="fas fa-file-pdf me-1"></i> {% trans "تحميل PDF" %}
        </button>
        <button type="button" class="btn btn-info me-2" id="emailInvoiceBtn">
            <i class="fas fa-envelope me-1"></i> {% trans "إرسال بالبريد الإلكتروني" %}
        </button>
        <a href="https://wa.me/?text={{ whatsapp_text|urlencode }}" class="btn btn-success me-2" target="_blank">
            <i class="fab fa-whatsapp me-1"></i> {% trans "إرسال عبر واتساب" %}
        </a>
        <a href="{% url 'sales:view_sale' sale.id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى تفاصيل البيع" %}
        </a>
    </div>
</div>

<div class="invoice-container">
    <!-- Invoice Header -->
    <div class="invoice-header">
        <div class="row">
            <div class="col-md-6">
                <div class="d-flex align-items-center">
                    <div class="logo-container" id="logo-container">
                        {% if invoice_settings.show_logo %}
                            {% if company_info.logo %}
                            <img src="{{ company_info.logo.url }}" alt="{{ company_info.name }}" class="invoice-logo me-3">
                            {% else %}
                            <img src="/static/img/logo.png" alt="Logo" class="invoice-logo me-3">
                            {% endif %}
                        {% endif %}
                    </div>
                    <div>
                        <h1 class="invoice-title">{{ company_info.name }}</h1>
                        <span class="badge bg-primary">{% trans "فاتورة مبيعات" %}</span>
                        <p class="mb-0">{% trans "فاتورة مبيعات" %}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6 text-md-end">
                <h2>{% trans "فاتورة" %} #{{ sale.invoice_number }}</h2>
                <p class="mb-0">{% trans "تاريخ الفاتورة:" %} {{ sale.date|date:"Y-m-d" }}</p>
                <p class="mb-0">{% trans "وقت الإصدار:" %} {{ sale.date|date:"H:i" }}</p>
            </div>
        </div>
    </div>

    <!-- Invoice Details -->
    <div class="row invoice-details">
        <div class="col-md-6 customer-info-container" id="customer-info-container">
            {% if invoice_settings.show_customer_info %}
            <h5>{% trans "معلومات العميل" %}</h5>
            <dl class="row mb-0">
                <dt class="col-sm-4">{% trans "اسم العميل:" %}</dt>
                <dd class="col-sm-8">{{ sale.customer.name }}</dd>

                {% if sale.customer.phone %}
                <dt class="col-sm-4">{% trans "رقم الهاتف:" %}</dt>
                <dd class="col-sm-8">{{ sale.customer.phone }}</dd>
                {% endif %}

                {% if sale.customer.email %}
                <dt class="col-sm-4">{% trans "البريد الإلكتروني:" %}</dt>
                <dd class="col-sm-8">{{ sale.customer.email }}</dd>
                {% endif %}

                {% if sale.customer.address %}
                <dt class="col-sm-4">{% trans "العنوان:" %}</dt>
                <dd class="col-sm-8">{{ sale.customer.address }}</dd>
                {% endif %}
            </dl>
            {% endif %}
        </div>
        <div class="col-md-6 company-info-container" id="company-info-container" {% if not invoice_settings.show_company_info %}style="display:none;"{% endif %}>
            <h5>{% trans "معلومات الشركة" %}</h5>
            <dl class="row mb-0">
                <dt class="col-sm-4">{% trans "اسم الشركة:" %}</dt>
                <dd class="col-sm-8">{{ company_info.name }}</dd>

                <dt class="col-sm-4">{% trans "رقم الهاتف:" %}</dt>
                <dd class="col-sm-8">{{ company_info.phone }}</dd>

                <dt class="col-sm-4">{% trans "البريد الإلكتروني:" %}</dt>
                <dd class="col-sm-8">{{ company_info.email }}</dd>

                <dt class="col-sm-4">{% trans "العنوان:" %}</dt>
                <dd class="col-sm-8">{{ company_info.address }}</dd>

                {% if company_info.tax_number %}
                <dt class="col-sm-4">{% trans "الرقم الضريبي:" %}</dt>
                <dd class="col-sm-8">{{ company_info.tax_number }}</dd>
                {% endif %}
            </dl>
        </div>
    </div>

    <!-- Invoice Items -->
    <div class="invoice-items">
        <h4 class="mb-3"><i class="fas fa-shopping-cart me-2"></i>{% trans "المنتجات" %}</h4>
        <div class="table-responsive">
            <table class="table table-invoice">
                <thead>
                    <tr>
                        <th width="5%">#</th>
                        <th width="15%">{% trans "كود المنتج" %}</th>
                        <th width="35%">{% trans "اسم المنتج" %}</th>
                        <th width="10%">{% trans "الكمية" %}</th>
                        <th width="15%">{% trans "سعر الوحدة" %}</th>
                        <th width="20%">{% trans "المجموع" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in items %}
                    <tr>
                        <td class="text-center">{{ forloop.counter }}</td>
                        <td>{{ item.product.code }}</td>
                        <td><strong>{{ item.product.name }}</strong></td>
                        <td class="text-center">{{ item.quantity }}</td>
                        <td class="text-start">{{ item.unit_price|floatformat:2 }} {% trans "د.م" %}</td>
                        <td class="text-start"><strong>{{ item.subtotal|floatformat:2 }} {% trans "د.م" %}</strong></td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Invoice Summary -->
    <div class="row">
        <div class="col-md-6">
            <div class="payment-info">
                <h5><i class="fas fa-money-check-alt me-2"></i>{% trans "معلومات الدفع" %}</h5>
                <dl class="row mb-0">
                    <dt class="col-sm-6 payment-method-info" {% if not invoice_settings.show_payment_info %}style="display:none;"{% endif %}>{% trans "طريقة الدفع:" %}</dt>
                    <dd class="col-sm-6 payment-method-info" {% if not invoice_settings.show_payment_info %}style="display:none;"{% endif %}>
                        {% if sale.payment_method == 'cash' %}
                        {% trans "نقدي" %}
                        {% elif sale.payment_method == 'card' %}
                        {% trans "بطاقة ائتمان" %}
                        {% elif sale.payment_method == 'transfer' %}
                        {% trans "تحويل بنكي" %}
                        {% elif sale.payment_method == 'check' %}
                        {% trans "شيك" %}
                        {% elif sale.payment_method == 'credit' %}
                        {% trans "آجل" %}
                        {% endif %}
                    </dd>

                    <dt class="col-sm-6">{% trans "حالة الدفع:" %}</dt>
                    <dd class="col-sm-6">
                        {% if sale.status == 'completed' %}
                        <span class="payment-status paid"><i class="fas fa-check-circle me-1"></i>{% trans "مدفوع بالكامل" %}</span>
                        {% elif sale.status == 'pending' %}
                        <span class="payment-status partial"><i class="fas fa-clock me-1"></i>{% trans "معلق" %}</span>
                        {% elif sale.status == 'cancelled' %}
                        <span class="payment-status unpaid"><i class="fas fa-exclamation-circle me-1"></i>{% trans "ملغي" %}</span>
                        {% endif %}
                    </dd>
                    
                    <!-- Display payment details -->
                    {% if payments %}
                    <dt class="col-sm-12 mt-3">{% trans "الدفعات:" %}</dt>
                    <dd class="col-sm-12">
                        <table class="table table-sm mt-2">
                            <thead>
                                <tr>
                                    <th>{% trans "التاريخ" %}</th>
                                    <th>{% trans "المبلغ" %}</th>
                                    <th>{% trans "المرجع" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in payments %}
                                <tr>
                                    <td>{{ payment.payment_date|date:"Y-m-d H:i" }}</td>
                                    <td>{{ payment.amount|floatformat:2 }} {% trans "د.م" %}</td>
                                    <td>{{ payment.reference|default:"" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th>{% trans "المجموع المدفوع:" %}</th>
                                    <th colspan="2">{{ total_paid|floatformat:2 }} {% trans "د.م" %}</th>
                                </tr>
                                <tr>
                                    <th>{% trans "المبلغ المتبقي:" %}</th>
                                    <th colspan="2">{{ remaining_amount|floatformat:2 }} {% trans "د.م" %}</th>
                                </tr>
                            </tfoot>
                        </table>
                    </dd>
                    {% endif %}
                </dl>
            </div>

            <div class="qr-code mt-4 text-center" id="qr-code">
                <img src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=INV-{{ sale.invoice_number }}" alt="QR Code">
                <p class="small mt-2">{% trans "امسح الرمز للتحقق من الفاتورة" %}</p>
            </div>
        </div>
        <div class="col-md-6">
            <div class="invoice-summary">
                <div class="d-flex justify-content-between mb-2">
                    <span>{% trans "المجموع الفرعي:" %}</span>
                    <span>{{ sale.subtotal|floatformat:2 }} د.م</span>
                </div>
                {% if invoice_settings.show_tax %}
                <div class="d-flex justify-content-between mb-2">
                    <span>{% trans "ضريبة القيمة المضافة" %} ({{ tax.rate }}%):</span>
                    <span>{{ sale.tax_amount|floatformat:2 }} د.م</span>
                </div>
                {% endif %}
                {% if sale.discount > 0 %}
                <div class="d-flex justify-content-between mb-2">
                    <span>{% trans "الخصم:" %}</span>
                    <span>{{ sale.discount|floatformat:2 }} د.م</span>
                </div>
                {% endif %}
                <hr>
                <div class="d-flex justify-content-between">
                    <strong>{% trans "المجموع الكلي:" %}</strong>
                    <strong class="total-amount">{{ sale.total_amount|floatformat:2 }} د.م</strong>
                </div>
            </div>
        </div>
    </div>

    <!-- Notes -->
    {% if sale.notes %}
    <div class="mt-4">
        <h5>{% trans "ملاحظات" %}</h5>
        <p>{{ sale.notes }}</p>
    </div>
    {% endif %}

    <!-- Signature Area -->
    <div class="row signature-area" id="signature-area">
        <div class="signature-box">
            <p>{% trans "توقيع المستلم" %}</p>
            <div style="height: 70px;"></div>
            <div class="signature-line"></div>
            <small class="text-muted">{% trans "الاسم والتاريخ" %}</small>
        </div>
        <div class="signature-box">
            <p>{% trans "ختم الشركة" %}</p>
            <div style="height: 70px;"></div>
            <div class="signature-line"></div>
            <small class="text-muted">{% trans "ختم الشركة" %}</small>
        </div>
        <div class="signature-box">
            <p>{% trans "توقيع البائع" %}</p>
            <div style="height: 70px;"></div>
            <div class="signature-line"></div>
            <small class="text-muted">{{ sale.employee.get_full_name|default:sale.employee.username }}</small>
        </div>
    </div>

    <!-- Terms and Conditions -->
    <div class="terms-conditions" id="terms-conditions">
        <h6><i class="fas fa-gavel me-2"></i>{% trans "الشروط والأحكام" %}</h6>
        <ol>
            <li>{% trans "جميع المبيعات نهائية ولا يمكن استردادها بعد مغادرة المتجر." %}</li>
            <li>{% trans "يمكن استبدال المنتجات في غضون 14 يومًا من تاريخ الشراء مع تقديم الفاتورة الأصلية." %}</li>
            <li>{% trans "لا يتم قبول المنتجات المستخدمة أو التالفة للاستبدال." %}</li>
            <li>{% trans "تطبق ضمانات الشركة المصنعة على المنتجات المؤهلة." %}</li>
            <li>{% trans "يحتفظ المتجر بالحق في تغيير هذه الشروط والأحكام في أي وقت." %}</li>
        </ol>
    </div>

    <!-- Invoice Footer -->
    <div class="invoice-footer text-center">
        <div class="row">
            <div class="col-md-4">
                <i class="fas fa-phone-alt me-2"></i> {{ company_info.phone|default:"" }}
            </div>
            <div class="col-md-4">
                <i class="fas fa-envelope me-2"></i> {{ company_info.email|default:"" }}
            </div>
            <div class="col-md-4">
                <i class="fas fa-globe me-2"></i> {{ company_info.website|default:"" }}
            </div>
        </div>
        <hr class="my-3">
        <p>{% trans "شكراً لتعاملكم معنا" %}</p>
        {% if company_info.footer_text %}
        <p class="small">{{ company_info.footer_text }}</p>
        {% endif %}
        <p class="small">{% trans "تم إنشاء هذه الفاتورة بواسطة" %} {{ company_info.name }} &copy; {% now "Y" %}</p>
        <p class="small">{% trans "تاريخ الطباعة:" %} {% now "Y-m-d H:i" %}</p>
    </div>
</div>

<!-- Email Invoice Modal -->
<div class="modal fade no-print" id="emailInvoiceModal" tabindex="-1" aria-labelledby="emailInvoiceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="emailInvoiceModalLabel">{% trans "إرسال الفاتورة بالبريد الإلكتروني" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'sales:email_invoice' sale.id %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="email_to" class="form-label required-field">{% trans "البريد الإلكتروني" %}</label>
                        <input type="email" class="form-control" id="email_to" name="email_to" value="{{ sale.customer.email }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="email_subject" class="form-label required-field">{% trans "الموضوع" %}</label>
                        <input type="text" class="form-control" id="email_subject" name="email_subject" value="{% trans 'فاتورة رقم' %} {{ sale.invoice_number }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="email_message" class="form-label">{% trans "الرسالة" %}</label>
                        <textarea class="form-control" id="email_message" name="email_message" rows="4">{% trans "مرفق فاتورة المبيعات الخاصة بكم. شكراً لتعاملكم معنا." %}</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "إرسال" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
<script>
    $(document).ready(function() {
        // إعادة تفعيل جميع خيارات الطباعة عند تحميل الصفحة
        setTimeout(function() {
            resetPrintOptionsUI();
        }, 500);
        // تهيئة خيارات الطباعة
        function initPrintOptions() {
            // استخدام إعدادات الفاتورة من قاعدة البيانات كقيم افتراضية
            // ثم التحقق من حالة الخيارات المحفوظة محلياً إذا كانت موجودة
            let showLogo = {% if invoice_settings.show_logo %}true{% else %}false{% endif %};
            let showCustomerInfo = {% if invoice_settings.show_customer_info %}true{% else %}false{% endif %};
            let showSignature = {% if invoice_settings.show_signature %}true{% else %}false{% endif %};
            let showTerms = {% if invoice_settings.show_terms %}true{% else %}false{% endif %};
            let showQR = {% if invoice_settings.show_qr_code %}true{% else %}false{% endif %};
            let showPaymentInfo = {% if invoice_settings.show_payment_info %}true{% else %}false{% endif %};
            let showCompanyInfo = {% if invoice_settings.show_company_info %}true{% else %}false{% endif %};
            let paperSize = '{{ invoice_settings.paper_size|default:"a4" }}';
            
            // استخدام الإعدادات المحلية إذا كانت موجودة (للسماح بتخصيص مؤقت)
            if (localStorage.getItem('invoice_show_logo') !== null) {
                showLogo = localStorage.getItem('invoice_show_logo') === 'true';
            }
            if (localStorage.getItem('invoice_show_customer_info') !== null) {
                showCustomerInfo = localStorage.getItem('invoice_show_customer_info') === 'true';
            }
            if (localStorage.getItem('invoice_show_signature') !== null) {
                showSignature = localStorage.getItem('invoice_show_signature') === 'true';
            }
            if (localStorage.getItem('invoice_show_terms') !== null) {
                showTerms = localStorage.getItem('invoice_show_terms') === 'true';
            }
            if (localStorage.getItem('invoice_show_qr') !== null) {
                showQR = localStorage.getItem('invoice_show_qr') === 'true';
            }
            if (localStorage.getItem('invoice_show_company_info') !== null) {
                showCompanyInfo = localStorage.getItem('invoice_show_company_info') === 'true';
            }
            if (localStorage.getItem('invoice_show_payment_info') !== null) {
                showPaymentInfo = localStorage.getItem('invoice_show_payment_info') === 'true';
            }
            if (localStorage.getItem('invoice_paper_size')) {
                paperSize = localStorage.getItem('invoice_paper_size');
            }

            // تعيين حالة مربعات الاختيار
            $('#show_logo_print').prop('checked', showLogo);
            $('#show_customer_info_print').prop('checked', showCustomerInfo);
            $('#show_signature_print').prop('checked', showSignature);
            $('#show_terms_print').prop('checked', showTerms);
            $('#show_qr_print').prop('checked', showQR);
            $('#show_payment_info_print').prop('checked', showPaymentInfo);
            $('#show_company_info_print').prop('checked', showCompanyInfo);

            // التأكد من أن جميع مربعات الاختيار تعمل بشكل صحيح
            $('.dropdown-item .form-check-input').each(function() {
                $(this).prop('disabled', false);
            });
            
            // تفعيل جميع خيارات القائمة المنسدلة
            $('.dropdown-item').removeClass('disabled');

            // تطبيق الخيارات
            applyPrintOptions();
            
            // تطبيق حجم الورق
            setPaperSize(paperSize);
        }

        // تطبيق خيارات الطباعة
        function applyPrintOptions() {
            // الحصول على حالة مربعات الاختيار الحالية
            const showLogo = $('#show_logo_print').is(':checked');
            const showCustomerInfo = $('#show_customer_info_print').is(':checked');
            const showCompanyInfo = $('#show_company_info_print').is(':checked');
            const showSignature = $('#show_signature_print').is(':checked');
            const showTerms = $('#show_terms_print').is(':checked');
            const showQR = $('#show_qr_print').is(':checked');
            const showPaymentInfo = $('#show_payment_info_print').is(':checked');

            // حفظ الخيارات في التخزين المحلي للاستخدام المستقبلي
            localStorage.setItem('invoice_show_logo', showLogo);
            localStorage.setItem('invoice_show_customer_info', showCustomerInfo);
            localStorage.setItem('invoice_show_company_info', showCompanyInfo);
            localStorage.setItem('invoice_show_signature', showSignature);
            localStorage.setItem('invoice_show_terms', showTerms);
            localStorage.setItem('invoice_show_qr', showQR);
            localStorage.setItem('invoice_show_payment_info', showPaymentInfo);

            // تطبيق الخيارات على عناصر الفاتورة بتأثير مرئي
            if (showLogo) {
                $('#logo-container').fadeIn(200);
            } else {
                $('#logo-container').hide();
            }

            if (showCustomerInfo) {
                $('#customer-info-container').fadeIn(200);
            } else {
                $('#customer-info-container').hide();
            }
            
            if (showCompanyInfo) {
                $('#company-info-container').fadeIn(200);
            } else {
                $('#company-info-container').hide();
            }

            if (showSignature) {
                $('#signature-area').fadeIn(200);
            } else {
                $('#signature-area').hide();
            }

            if (showTerms) {
                $('#terms-conditions').fadeIn(200);
            } else {
                $('#terms-conditions').hide();
            }

            if (showQR) {
                $('#qr-code').fadeIn(200);
            } else {
                $('#qr-code').hide();
            }
            
            if (showPaymentInfo) {
                $('.payment-method-info').fadeIn(200);
            } else {
                $('.payment-method-info').hide();
            }
            
            // تحديث حالة مربعات الاختيار في القائمة المنسدلة
            $('#show_logo_print').prop('checked', showLogo);
            $('#show_customer_info_print').prop('checked', showCustomerInfo);
            $('#show_company_info_print').prop('checked', showCompanyInfo);
            $('#show_signature_print').prop('checked', showSignature);
            $('#show_terms_print').prop('checked', showTerms);
            $('#show_qr_print').prop('checked', showQR);
            $('#show_payment_info_print').prop('checked', showPaymentInfo);
            
            // إظهار رسالة تأكيد صغيرة للمستخدم
            const alertMsg = $('<div class="alert alert-success alert-dismissible fade show no-print" role="alert" style="position: fixed; top: 10px; right: 10px; z-index: 9999;">' +
                               '<i class="fas fa-check-circle me-1"></i> تم تطبيق إعدادات الفاتورة بنجاح' +
                               '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                               '</div>');
            $('body').append(alertMsg);
            setTimeout(function() {
                alertMsg.alert('close');
            }, 2000);
        }

        // تغيير حجم الورق مع تأثيرات حركية
        function setPaperSize(size) {
            // التأكد من أن الحجم صالح، وإلا استخدم A4 كافتراضي
            if (!size || (size !== 'a4' && size !== 'a5' && size !== 'thermal')) {
                size = 'a4';
            }
            
            // حفظ الخيار في التخزين المحلي
            localStorage.setItem('invoice_paper_size', size);

            // إضافة تأثير انتقالي قبل تغيير الحجم
            $('.invoice-container').css('transition', 'all 0.5s ease');
            
            // إزالة الأصناف السابقة وأي أنماط سابقة
            $('.invoice-container').removeClass('a4-invoice a5-invoice thermal-receipt');
            $('body').removeClass('a5-mode thermal-mode');
            $('#paper-size-style').remove();

            // إضافة الصنف المناسب مع تأثير حركي
            if (size === 'a5') {
                // إظهار تأثير التحميل
                showLoading();
                
                setTimeout(function() {
                    $('.invoice-container').addClass('a5-invoice');
                    $('body').addClass('a5-mode');
                    // تعديل حجم الصفحة في CSS
                    $('head').append('<style id="paper-size-style">@page { size: A5; }</style>');
                    
                    // تحديث حالة القائمة المنسدلة
                    $('[data-paper-size]').removeClass('active');
                    $('[data-paper-size="a5"]').addClass('active');
                    
                    // إخفاء تأثير التحميل
                    hideLoading();
                    
                    // إظهار رسالة تأكيد
                    showToast('تم تغيير حجم الورق إلى A5');
                }, 300);
            } else if (size === 'thermal') {
                // إظهار تأثير التحميل
                showLoading();
                
                setTimeout(function() {
                    $('.invoice-container').addClass('thermal-receipt');
                    $('body').addClass('thermal-mode');
                    // تعديل حجم الصفحة في CSS
                    $('head').append('<style id="paper-size-style">@page { size: 80mm auto !important; margin: 0mm !important; }</style>');
                    // تطبيق تنسيقات إضافية للإيصال الحراري
                    $('.invoice-container').css({
                        'width': '80mm',
                        'margin': '0 auto',
                        'padding': '5mm'
                    });
                    
                    // تحديث حالة القائمة المنسدلة
                    $('[data-paper-size]').removeClass('active');
                    $('[data-paper-size="thermal"]').addClass('active');
                    
                    // إخفاء تأثير التحميل
                    hideLoading();
                    
                    // إظهار رسالة تأكيد
                    showToast('تم تغيير حجم الورق إلى إيصال حراري');
                }, 300);
            } else {
                // A4 هو الافتراضي
                $('.invoice-container').addClass('a4-invoice');
                // تعديل حجم الصفحة في CSS
                $('head').append('<style id="paper-size-style">@page { size: A4; }</style>');
                
                // تحديث حالة القائمة المنسدلة
                $('[data-paper-size]').removeClass('active');
                $('[data-paper-size="a4"]').addClass('active');
            }
            
            // إظهار رسالة تأكيد صغيرة للمستخدم
            const paperSizeNames = {
                'a4': 'A4',
                'a5': 'A5',
                'thermal': 'إيصال حراري (80مم)'
            };
            
            const alertMsg = $('<div class="alert alert-info alert-dismissible fade show no-print" role="alert" style="position: fixed; bottom: 10px; left: 10px; z-index: 9999;">' +
                               '<i class="fas fa-info-circle me-1"></i> تم تغيير حجم الورق إلى ' + paperSizeNames[size] +
                               '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                               '</div>');
            $('body').append(alertMsg);
            setTimeout(function() {
                alertMsg.alert('close');
            }, 2000);
        }

        // تهيئة الأحداث
        function initEvents() {
            // أحداث مربعات الاختيار - تطبيق التغييرات فوراً عند تغيير أي خيار
            $('#show_logo_print, #show_customer_info_print, #show_company_info_print, #show_signature_print, #show_terms_print, #show_qr_print, #show_payment_info_print').change(function() {
                applyPrintOptions();
            });

            // أحداث حجم الورق - تطبيق حجم الورق المختار فوراً
            $('[data-paper-size]').click(function(e) {
                e.preventDefault();
                const size = $(this).data('paper-size');
                setPaperSize(size);
            });
            
            // التأكد من أن جميع خيارات الطباعة تعمل بشكل صحيح
            $('.dropdown-item .form-check-input').each(function() {
                $(this).prop('disabled', false);
            });
            
            // تفعيل جميع خيارات القائمة المنسدلة
            $('.dropdown-item').removeClass('disabled');
            
            // إضافة زر لإعادة تعيين الإعدادات الافتراضية
            $('.dropdown-menu').append(
                '<li><hr class="dropdown-divider"></li>' +
                '<li><a class="dropdown-item text-warning" href="#" id="resetInvoiceSettings">' +
                '<i class="fas fa-undo me-1"></i> {% trans "إعادة تعيين الإعدادات الافتراضية" %}</a></li>'
            );
            
            // حدث إعادة تعيين الإعدادات الافتراضية
            $('#resetInvoiceSettings').click(function(e) {
                e.preventDefault();
                // حذف الإعدادات المحلية
                localStorage.removeItem('invoice_show_logo');
                localStorage.removeItem('invoice_show_customer_info');
                localStorage.removeItem('invoice_show_company_info');
                localStorage.removeItem('invoice_show_signature');
                localStorage.removeItem('invoice_show_terms');
                localStorage.removeItem('invoice_show_qr');
                localStorage.removeItem('invoice_show_payment_info');
                localStorage.removeItem('invoice_paper_size');
                
                // إعادة تهيئة الإعدادات من قاعدة البيانات
                initPrintOptions();
                
                // إظهار رسالة تأكيد
                const alertMsg = $('<div class="alert alert-warning alert-dismissible fade show no-print" role="alert" style="position: fixed; top: 10px; right: 10px; z-index: 9999;">' +
                                   '<i class="fas fa-sync-alt me-1"></i> تم إعادة تعيين إعدادات الفاتورة الافتراضية' +
                                   '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                                   '</div>');
                $('body').append(alertMsg);
                setTimeout(function() {
                    alertMsg.alert('close');
                }, 2000);
            });
        }

        // Email Invoice Button
        $('#emailInvoiceBtn').click(function(e) {
            e.preventDefault();
            $('#emailInvoiceModal').modal('show');
        });

        // Download PDF Button
        $('#downloadPdfBtn').click(function(e) {
            e.preventDefault();
            
            // إظهار رسالة تحميل
            showToast('جاري تحضير ملف PDF، يرجى الانتظار...');
            
            // Hide buttons
            $('.no-print').hide();

            // Get paper size
            const paperSize = localStorage.getItem('invoice_paper_size') || 'a4';
            let pdfFormat = 'a4';
            let pdfOrientation = 'portrait';
            let pdfMargin = 10;

            if (paperSize === 'a5') {
                pdfFormat = 'a5';
            } else if (paperSize === 'thermal') {
                pdfFormat = [80, 'auto']; // 80mm width, auto height
                pdfMargin = 2; // هوامش أقل للإيصال الحراري
            }

            // Options for PDF
            var options = {
                margin: pdfMargin,
                filename: 'فاتورة_{{ sale.invoice_number }}.pdf',
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: { scale: 2, useCORS: true },
                jsPDF: { unit: 'mm', format: pdfFormat, orientation: pdfOrientation }
            };

            // Generate PDF
            html2pdf().from($('.invoice-container')[0]).set(options).save().then(function() {
                // Show buttons again
                $('.no-print').show();
                showToast('تم تحميل الفاتورة بنجاح!');
            });
        });
        
        // Email Invoice Button
        $('#emailInvoiceBtn').click(function(e) {
            e.preventDefault();
            showToast('جاري تحضير البريد الإلكتروني...');
            $('#emailInvoiceModal').modal('show');
        });
        
        // وظيفة لعرض رسائل تأكيد
        function showToast(message) {
            // إزالة أي تنبيهات سابقة
            $('.toast-notification').remove();
            
            // إنشاء عنصر التنبيه
            const toast = $('<div class="toast-notification no-print">' + message + '</div>');
            $('body').append(toast);
            
            // إظهار التنبيه
            setTimeout(function() {
                toast.addClass('show');
            }, 100);
            
            // إخفاء التنبيه بعد 3 ثوان
            setTimeout(function() {
                toast.removeClass('show');
                setTimeout(function() {
                    toast.remove();
                }, 500);
            }, 3000);
        }
        
        // وظيفة لإظهار تأثير التحميل
        function showLoading() {
            // إزالة أي تأثيرات تحميل سابقة
            $('.loading-overlay').remove();
            
            // إنشاء عنصر التحميل
            const loading = $('<div class="loading-overlay no-print"><div class="spinner"></div></div>');
            $('body').append(loading);
            
            // إظهار التحميل
            setTimeout(function() {
                loading.addClass('show');
            }, 100);
        }
        
        // وظيفة لإخفاء تأثير التحميل
        function hideLoading() {
            const loading = $('.loading-overlay');
            loading.removeClass('show');
            setTimeout(function() {
                loading.remove();
            }, 500);
        }
        
        // دالة لإعادة تفعيل جميع خيارات الطباعة
        function resetPrintOptionsUI() {
            // التأكد من أن جميع مربعات الاختيار تعمل بشكل صحيح
            $('.dropdown-item .form-check-input').each(function() {
                $(this).prop('disabled', false);
            });
            
            // تفعيل جميع خيارات القائمة المنسدلة
            $('.dropdown-item').removeClass('disabled');
            
            // التأكد من أن جميع خيارات حجم الورق متاحة
            $('[data-paper-size]').parent().removeClass('disabled');
            
            // إظهار رسالة تأكيد
            showToast('تم إعادة تفعيل جميع خيارات الطباعة');
        }

        // تهيئة الصفحة
        initPrintOptions(); // سيقوم بتطبيق الإعدادات المحفوظة وحجم الورق
        initEvents();
        
        // تحسين طباعة الإيصال الحراري
        window.addEventListener('beforeprint', function() {
            const currentPaperSize = localStorage.getItem('invoice_paper_size') || 'a4';
            if (currentPaperSize === 'thermal') {
                // تأكيد تطبيق تنسيقات الإيصال الحراري قبل الطباعة
                $('body').addClass('thermal-mode');
                $('.invoice-container').addClass('thermal-receipt');
                $('.invoice-container').css({
                    'width': '80mm',
                    'margin': '0 auto',
                    'padding': '5mm'
                });
            }
        });
    });
</script>
{% endblock %}
