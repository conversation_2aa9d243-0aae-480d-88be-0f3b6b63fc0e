# Generated by Django 5.2 on 2025-04-21 14:56

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AttendanceSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('work_start_time', models.TimeField(verbose_name='وقت بدء العمل')),
                ('work_end_time', models.TimeField(verbose_name='وقت انتهاء العمل')),
                ('late_threshold_minutes', models.PositiveIntegerField(default=15, verbose_name='حد التأخير بالدقائق')),
                ('absence_threshold_days', models.PositiveIntegerField(default=3, verbose_name='حد الغياب بالأيام للتنبيه')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إعدادات الحضور',
                'verbose_name_plural': 'إعدادات الحضور',
            },
        ),
        migrations.CreateModel(
            name='CustomPermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='الرمز')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('category', models.CharField(default='general', max_length=50, verbose_name='الفئة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'صلاحية',
                'verbose_name_plural': 'الصلاحيات',
                'ordering': ['category', 'name'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alert_type', models.CharField(choices=[('absence', 'غياب'), ('late', 'تأخير'), ('contract', 'انتهاء العقد'), ('performance', 'أداء'), ('other', 'أخرى')], max_length=20, verbose_name='نوع التنبيه')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروء')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='alerts', to='employees.employeeprofile', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'تنبيه موظف',
                'verbose_name_plural': 'تنبيهات الموظفين',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PerformanceReview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('review_date', models.DateField(verbose_name='تاريخ التقييم')),
                ('period_start', models.DateField(verbose_name='بداية فترة التقييم')),
                ('period_end', models.DateField(verbose_name='نهاية فترة التقييم')),
                ('attendance_rating', models.PositiveSmallIntegerField(choices=[(1, 'ضعيف'), (2, 'مقبول'), (3, 'جيد'), (4, 'جيد جداً'), (5, 'ممتاز')], verbose_name='تقييم الحضور')),
                ('productivity_rating', models.PositiveSmallIntegerField(choices=[(1, 'ضعيف'), (2, 'مقبول'), (3, 'جيد'), (4, 'جيد جداً'), (5, 'ممتاز')], verbose_name='تقييم الإنتاجية')),
                ('quality_rating', models.PositiveSmallIntegerField(choices=[(1, 'ضعيف'), (2, 'مقبول'), (3, 'جيد'), (4, 'جيد جداً'), (5, 'ممتاز')], verbose_name='تقييم الجودة')),
                ('teamwork_rating', models.PositiveSmallIntegerField(choices=[(1, 'ضعيف'), (2, 'مقبول'), (3, 'جيد'), (4, 'جيد جداً'), (5, 'ممتاز')], verbose_name='تقييم العمل الجماعي')),
                ('comments', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='performance_reviews', to='employees.employeeprofile', verbose_name='الموظف')),
                ('reviewer', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='المقيم')),
            ],
            options={
                'verbose_name': 'تقييم أداء',
                'verbose_name_plural': 'تقييمات الأداء',
                'ordering': ['-review_date'],
            },
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('permissions', models.ManyToManyField(blank=True, to='employees.custompermission', verbose_name='الصلاحيات')),
            ],
            options={
                'verbose_name': 'دور وظيفي',
                'verbose_name_plural': 'الأدوار الوظيفية',
            },
        ),
    ]
