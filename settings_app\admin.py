from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import (
    SystemSetting, CompanyInfo, BackupLog, NotificationSetting,
    CurrencySetting, TaxSetting, InvoiceSetting, EmailSetting,
    BackupSetting, LanguageSetting
)

@admin.register(SystemSetting)
class SystemSettingAdmin(admin.ModelAdmin):
    list_display = ('key', 'value', 'is_public', 'updated_at')
    list_filter = ('is_public',)
    search_fields = ('key', 'value')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (_('معلومات الإعداد'), {
            'fields': ('key', 'value', 'description', 'is_public')
        }),
        (_('معلومات التوقيت'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(CompanyInfo)
class CompanyInfoAdmin(admin.ModelAdmin):
    list_display = ('name', 'phone', 'email', 'updated_at')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (_('معلومات الشركة الأساسية'), {
            'fields': ('name', 'logo', 'address', 'phone', 'email')
        }),
        (_('معلومات إضافية'), {
            'fields': ('website', 'tax_number', 'commercial_register', 'footer_text')
        }),
        (_('معلومات التوقيت'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(BackupLog)
class BackupLogAdmin(admin.ModelAdmin):
    list_display = ('file_name', 'backup_type', 'status', 'file_size', 'created_at')
    list_filter = ('backup_type', 'status', 'created_at')
    search_fields = ('file_name', 'notes')
    readonly_fields = ('created_at',)
    fieldsets = (
        (_('معلومات النسخة الاحتياطية'), {
            'fields': ('file_name', 'file_path', 'file_size', 'backup_type', 'status')
        }),
        (_('ملاحظات'), {
            'fields': ('notes',)
        }),
        (_('معلومات التوقيت'), {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )

@admin.register(NotificationSetting)
class NotificationSettingAdmin(admin.ModelAdmin):
    list_display = ('event', 'email_notification', 'system_notification', 'updated_at')
    list_filter = ('email_notification', 'system_notification')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (_('إعدادات الإشعار'), {
            'fields': ('event', 'email_notification', 'system_notification')
        }),
        (_('قالب البريد الإلكتروني'), {
            'fields': ('email_template', 'recipients')
        }),
        (_('معلومات التوقيت'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(CurrencySetting)
class CurrencySettingAdmin(admin.ModelAdmin):
    list_display = ('currency', 'symbol', 'position', 'is_default', 'updated_at')
    list_filter = ('is_default',)
    search_fields = ('currency', 'symbol')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (_('إعدادات العملة'), {
            'fields': ('currency', 'symbol', 'position', 'is_default')
        }),
        (_('إعدادات التنسيق'), {
            'fields': ('decimal_places', 'thousands_separator', 'decimal_separator')
        }),
        (_('معلومات التوقيت'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(TaxSetting)
class TaxSettingAdmin(admin.ModelAdmin):
    list_display = ('name', 'rate', 'tax_type', 'is_enabled', 'updated_at')
    list_filter = ('is_enabled', 'tax_type')
    search_fields = ('name', 'tax_number')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (_('إعدادات الضريبة'), {
            'fields': ('name', 'rate', 'tax_type', 'is_enabled')
        }),
        (_('معلومات إضافية'), {
            'fields': ('tax_number',)
        }),
        (_('معلومات التوقيت'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(InvoiceSetting)
class InvoiceSettingAdmin(admin.ModelAdmin):
    list_display = ('prefix', 'next_number', 'show_logo', 'show_tax', 'updated_at')
    list_filter = ('show_logo', 'show_tax', 'show_customer_info')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (_('إعدادات ترقيم الفاتورة'), {
            'fields': ('prefix', 'next_number')
        }),
        (_('إعدادات عرض الفاتورة'), {
            'fields': ('show_logo', 'show_tax', 'show_customer_info')
        }),
        (_('نصوص الفاتورة'), {
            'fields': ('footer_text', 'terms_and_conditions')
        }),
        (_('معلومات التوقيت'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(EmailSetting)
class EmailSettingAdmin(admin.ModelAdmin):
    list_display = ('smtp_host', 'smtp_port', 'from_email', 'is_enabled', 'updated_at')
    list_filter = ('is_enabled', 'smtp_use_tls')
    search_fields = ('smtp_host', 'from_email', 'from_name')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (_('إعدادات SMTP'), {
            'fields': ('smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_use_tls')
        }),
        (_('إعدادات المرسل'), {
            'fields': ('from_email', 'from_name', 'is_enabled')
        }),
        (_('معلومات التوقيت'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(BackupSetting)
class BackupSettingAdmin(admin.ModelAdmin):
    list_display = ('is_auto_backup', 'frequency', 'storage_type', 'retention_days', 'updated_at')
    list_filter = ('is_auto_backup', 'frequency', 'storage_type')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (_('إعدادات النسخ الاحتياطي التلقائي'), {
            'fields': ('is_auto_backup', 'frequency', 'backup_time')
        }),
        (_('إعدادات التخزين'), {
            'fields': ('storage_type', 'storage_path', 'retention_days', 'include_media')
        }),
        (_('معلومات التوقيت'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(LanguageSetting)
class LanguageSettingAdmin(admin.ModelAdmin):
    list_display = ('language', 'direction', 'is_default', 'is_enabled', 'updated_at')
    list_filter = ('is_default', 'is_enabled', 'direction')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (_('إعدادات اللغة'), {
            'fields': ('language', 'direction', 'is_default', 'is_enabled')
        }),
        (_('معلومات التوقيت'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
