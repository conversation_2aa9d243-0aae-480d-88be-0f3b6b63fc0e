from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import (
    Job, JobItem, JobService, JobAttachment, JobComment,
    Department, Team, TeamMember, Skill, EmployeeSkill, TeamAssignment
)

class JobItemInline(admin.TabularInline):
    model = JobItem
    extra = 1
    fields = ('product', 'quantity', 'unit_price', 'subtotal', 'notes')
    readonly_fields = ('subtotal',)

class JobServiceInline(admin.TabularInline):
    model = JobService
    extra = 1
    fields = ('name', 'description', 'cost', 'technician')

class JobAttachmentInline(admin.TabularInline):
    model = JobAttachment
    extra = 1
    fields = ('file', 'name', 'description', 'uploaded_by')

class JobCommentInline(admin.TabularInline):
    model = JobComment
    extra = 1
    fields = ('user', 'comment', 'created_at')
    readonly_fields = ('created_at',)

@admin.register(Job)
class JobAdmin(admin.ModelAdmin):
    list_display = ('job_number', 'title', 'customer', 'car_make', 'car_model', 'status', 'priority', 'created_at')
    list_filter = ('status', 'priority', 'created_at')
    search_fields = ('job_number', 'title', 'customer__name', 'car_make', 'car_model', 'car_plate')
    readonly_fields = ('job_number', 'created_at', 'updated_at')
    date_hierarchy = 'created_at'
    inlines = [JobItemInline, JobServiceInline, JobAttachmentInline, JobCommentInline]

    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('job_number', 'title', 'description', 'customer')
        }),
        (_('معلومات السيارة'), {
            'fields': ('car_make', 'car_model', 'car_year', 'car_vin', 'car_plate', 'car_color', 'car_mileage')
        }),
        (_('حالة العمل'), {
            'fields': ('status', 'priority', 'assigned_to', 'created_by')
        }),
        (_('التواريخ'), {
            'fields': ('created_at', 'updated_at', 'start_date', 'due_date', 'completed_date')
        }),
        (_('المالية'), {
            'fields': ('estimated_cost', 'actual_cost')
        }),
        (_('ملاحظات'), {
            'fields': ('notes',)
        }),
    )

@admin.register(JobItem)
class JobItemAdmin(admin.ModelAdmin):
    list_display = ('job', 'product', 'quantity', 'unit_price', 'subtotal')
    list_filter = ('job',)
    search_fields = ('job__job_number', 'product__name')
    readonly_fields = ('subtotal',)

@admin.register(JobService)
class JobServiceAdmin(admin.ModelAdmin):
    list_display = ('job', 'name', 'cost', 'technician')
    list_filter = ('job',)
    search_fields = ('job__job_number', 'name', 'technician__username')

@admin.register(JobAttachment)
class JobAttachmentAdmin(admin.ModelAdmin):
    list_display = ('job', 'name', 'uploaded_by', 'uploaded_at')
    list_filter = ('job', 'uploaded_at')
    search_fields = ('job__job_number', 'name', 'uploaded_by__username')
    readonly_fields = ('uploaded_at',)

@admin.register(JobComment)
class JobCommentAdmin(admin.ModelAdmin):
    list_display = ('job', 'user', 'comment', 'created_at')
    list_filter = ('job', 'user', 'created_at')
    search_fields = ('job__job_number', 'user__username', 'comment')
    readonly_fields = ('created_at',)

# إدارة فرق العمل

class TeamInline(admin.TabularInline):
    model = Team
    extra = 1
    fields = ('name', 'description', 'leader')

@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ('name', 'manager', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('name', 'description', 'manager__username')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [TeamInline]

    fieldsets = (
        (_('معلومات القسم'), {
            'fields': ('name', 'description', 'manager')
        }),
        (_('معلومات التوقيت'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

class TeamMemberInline(admin.TabularInline):
    model = TeamMember
    extra = 1
    fields = ('user', 'role', 'is_active', 'notes')

@admin.register(Team)
class TeamAdmin(admin.ModelAdmin):
    list_display = ('name', 'department', 'leader', 'created_at')
    list_filter = ('department', 'created_at')
    search_fields = ('name', 'description', 'leader__username')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [TeamMemberInline]

    fieldsets = (
        (_('معلومات الفريق'), {
            'fields': ('name', 'description', 'department', 'leader')
        }),
        (_('معلومات التوقيت'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(TeamMember)
class TeamMemberAdmin(admin.ModelAdmin):
    list_display = ('user', 'team', 'role', 'is_active', 'joined_at')
    list_filter = ('team', 'role', 'is_active', 'joined_at')
    search_fields = ('user__username', 'user__first_name', 'user__last_name', 'team__name')
    readonly_fields = ('joined_at',)

    fieldsets = (
        (_('معلومات العضو'), {
            'fields': ('user', 'team', 'role', 'is_active')
        }),
        (_('معلومات إضافية'), {
            'fields': ('notes', 'joined_at')
        }),
    )

@admin.register(Skill)
class SkillAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name', 'description')

class EmployeeSkillInline(admin.TabularInline):
    model = EmployeeSkill
    extra = 1
    fields = ('skill', 'proficiency', 'years_experience', 'notes')

@admin.register(EmployeeSkill)
class EmployeeSkillAdmin(admin.ModelAdmin):
    list_display = ('user', 'skill', 'proficiency', 'years_experience')
    list_filter = ('skill', 'proficiency')
    search_fields = ('user__username', 'user__first_name', 'user__last_name', 'skill__name')

    fieldsets = (
        (_('معلومات المهارة'), {
            'fields': ('user', 'skill', 'proficiency', 'years_experience')
        }),
        (_('معلومات إضافية'), {
            'fields': ('notes',)
        }),
    )

@admin.register(TeamAssignment)
class TeamAssignmentAdmin(admin.ModelAdmin):
    list_display = ('job', 'team', 'assigned_by', 'assigned_at')
    list_filter = ('team', 'assigned_at')
    search_fields = ('job__job_number', 'team__name', 'assigned_by__username')
    readonly_fields = ('assigned_at',)

    fieldsets = (
        (_('معلومات التخصيص'), {
            'fields': ('job', 'team', 'assigned_by')
        }),
        (_('معلومات إضافية'), {
            'fields': ('notes', 'assigned_at')
        }),
    )