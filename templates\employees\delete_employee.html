{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "حذف الموظف" %} - {{ employee.full_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "حذف الموظف" %}</h1>
        <a href="{% url 'employees:index' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> {% trans "العودة إلى قائمة الموظفين" %}
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "تأكيد الحذف" %}</h6>
        </div>
        <div class="card-body">
            <div class="alert alert-danger">
                <h5>{% trans "هل أنت متأكد من حذف الموظف" %}: <strong>{{ employee.full_name }}</strong>؟</h5>
                <p>{% trans "سيتم حذف جميع بيانات الموظف بشكل نهائي، بما في ذلك حساب المستخدم المرتبط به. هذا الإجراء لا يمكن التراجع عنه." %}</p>
            </div>

            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">{% trans "معلومات الموظف" %}</h5>
                            <p><strong>{% trans "رقم الموظف" %}:</strong> {{ employee.employee_id }}</p>
                            <p><strong>{% trans "الاسم" %}:</strong> {{ employee.full_name }}</p>
                            <p><strong>{% trans "البريد الإلكتروني" %}:</strong> {{ employee.user.email }}</p>
                            <p><strong>{% trans "القسم" %}:</strong> {{ employee.department.name|default:"-" }}</p>
                            <p><strong>{% trans "المنصب" %}:</strong> {{ employee.position.name|default:"-" }}</p>
                            <p><strong>{% trans "تاريخ التوظيف" %}:</strong> {{ employee.hire_date|date:"Y-m-d" }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <form method="post">
                {% csrf_token %}
                <div class="form-group">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> {% trans "تأكيد الحذف" %}
                    </button>
                    <a href="{% url 'employees:view_employee' employee.id %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> {% trans "إلغاء" %}
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
