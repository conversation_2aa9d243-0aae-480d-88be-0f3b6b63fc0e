{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "إدارة المخزون" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<style>
    .status-available {
        color: #198754;
        font-weight: bold;
    }
    .status-low {
        color: #ffc107;
        font-weight: bold;
    }
    .status-out {
        color: #dc3545;
        font-weight: bold;
    }
    .card-dashboard {
        transition: all 0.3s;
    }
    .card-dashboard:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .product-image-small {
        width: 50px;
        height: 50px;
        object-fit: contain;
        border-radius: 4px;
    }
    .action-buttons .btn-group {
        display: flex;
        gap: 3px;
    }

    .action-buttons .btn {
        border-radius: 4px;
        padding: 0.25rem 0.5rem;
    }

    /* تنسيق نافذة عرض التفاصيل */
    #productImageContainer img {
        transition: transform 0.3s ease;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    #productImageContainer img:hover {
        transform: scale(1.05);
    }

    #productDescription {
        min-height: 100px;
        max-height: 200px;
        overflow-y: auto;
    }
    .filter-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .dataTables_length {
        margin-bottom: 15px;
        margin-right: 15px;
    }

    .dataTables_length select {
        padding: 0.375rem 2.25rem 0.375rem 0.75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #212529;
        background-color: #fff;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .dataTables_info {
        padding-top: 0.85em;
        white-space: nowrap;
        margin-right: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "إدارة المخزون" %}</h1>
    <div>
        <a href="{% url 'inventory:stock_alerts' %}" class="btn btn-warning me-2">
            <i class="fas fa-exclamation-triangle me-1"></i> {% trans "تنبيهات المخزون" %}
            <span class="badge bg-danger ms-1">{{ low_stock_count|add:out_of_stock_count }}</span>
        </a>
        <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#importModal">
            <i class="fas fa-file-import me-1"></i> {% trans "استيراد" %}
        </button>
        <div class="dropdown d-inline-block me-2">
            <button class="btn btn-info dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-file-export me-1"></i> {% trans "تصدير" %}
            </button>
            <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                <li><a class="dropdown-item" href="{% url 'inventory:export_products' %}?type=excel"><i class="fas fa-file-excel me-1"></i> {% trans "تصدير Excel" %}</a></li>
                <li><a class="dropdown-item" href="{% url 'inventory:export_products' %}?type=pdf"><i class="fas fa-file-pdf me-1"></i> {% trans "تصدير PDF" %}</a></li>
                <li><a class="dropdown-item" href="{% url 'inventory:export_products' %}?type=csv"><i class="fas fa-file-csv me-1"></i> {% trans "تصدير CSV" %}</a></li>
            </ul>
        </div>
        <a href="{% url 'inventory:add_product' %}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> {% trans "إضافة منتج جديد" %}
        </a>
    </div>
</div>

<!-- Dashboard Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2 card-dashboard">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {% trans "إجمالي المنتجات" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ products.count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-boxes fa-2x text-primary opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <a href="{% url 'inventory:stock_alerts' %}" class="text-decoration-none">
            <div class="card border-left-warning shadow h-100 py-2 card-dashboard">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                {% trans "منتجات منخفضة المخزون" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ low_stock_count|default:"0" }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-warning opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </a>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <a href="{% url 'inventory:stock_alerts' %}?status=out" class="text-decoration-none">
            <div class="card border-left-danger shadow h-100 py-2 card-dashboard">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                {% trans "منتجات نفدت من المخزون" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ out_of_stock_count|default:"0" }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-danger opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </a>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2 card-dashboard">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {% trans "قيمة المخزون" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ inventory_value|default:"0"|floatformat:2 }} د.م
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-success opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Alerts Section -->
{% if low_stock_count > 0 or out_of_stock_count > 0 %}
<div class="alert alert-warning alert-dismissible fade show" role="alert">
    <div class="d-flex align-items-center">
        <div class="me-3">
            <i class="fas fa-exclamation-triangle fa-2x"></i>
        </div>
        <div>
            <h5 class="alert-heading mb-1">{% trans "تنبيهات المخزون" %}</h5>
            <p class="mb-0">
                {% if out_of_stock_count > 0 %}
                    {% trans "لديك" %} <strong>{{ out_of_stock_count }}</strong> {% trans "منتج نفد من المخزون" %}{% if low_stock_count > 0 %} {% trans "و" %} {% endif %}
                {% endif %}
                {% if low_stock_count > 0 %}
                    <strong>{{ low_stock_count }}</strong> {% trans "منتج منخفض المخزون" %}
                {% endif %}
                {% trans "يحتاج إلى إعادة تعبئة." %}
            </p>
        </div>
        <div class="ms-auto">
            <a href="{% url 'inventory:stock_alerts' %}" class="btn btn-warning btn-sm">
                {% trans "عرض التنبيهات" %}
            </a>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
{% endif %}

<!-- Filter Section -->
<div class="filter-section mb-4">
    <div class="row">
        <div class="col-md-3 mb-2">
            <label for="categoryFilter" class="form-label">{% trans "الفئة" %}</label>
            <select class="form-select" id="categoryFilter">
                <option value="">{% trans "جميع الفئات" %}</option>
                {% for category in categories %}
                <option value="{{ category.id }}">{{ category.name }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-3 mb-2">
            <label for="supplierFilter" class="form-label">{% trans "المورد" %}</label>
            <select class="form-select" id="supplierFilter">
                <option value="">{% trans "جميع الموردين" %}</option>
                {% for supplier in suppliers %}
                <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-3 mb-2">
            <label for="stockStatusFilter" class="form-label">{% trans "حالة المخزون" %}</label>
            <select class="form-select" id="stockStatusFilter">
                <option value="">{% trans "جميع الحالات" %}</option>
                <option value="available">{% trans "متوفر" %}</option>
                <option value="low">{% trans "منخفض" %}</option>
                <option value="out">{% trans "نفد" %}</option>
            </select>
        </div>
        <div class="col-md-3 mb-2">
            <label for="quickSearch" class="form-label">{% trans "بحث سريع" %}</label>
            <form method="get" action="{% url 'inventory:index' %}">
                <div class="input-group">
                    <input type="text" class="form-control" name="search" placeholder="{% trans 'اسم المنتج أو الكود أو الوصف أو الباركود' %}" value="{{ search_query|default_if_none:'' }}">
                    <button class="btn btn-primary" type="submit"><i class="fas fa-search"></i></button>
                </div>
            </form>
        </div>
        <div class="col-12 mt-3">
            <button class="btn btn-outline-primary" type="button" id="sortByPurchasePrice">
                <i class="fas fa-sort-amount-down me-1"></i> {% trans "ترتيب حسب سعر الشراء (من الأقل إلى الأعلى)" %}
            </button>
        </div>
    </div>
</div>

<!-- Products Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "قائمة المنتجات" %}</h6>
        <div class="btn-group">
            <button type="button" class="btn btn-sm btn-outline-danger" id="bulkDeleteBtn" disabled>
                <i class="fas fa-trash me-1"></i> {% trans "حذف متعدد" %}
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover" id="productsTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th width="20px">
                            <input type="checkbox" id="selectAll" class="form-check-input">
                        </th>
                        <th width="60px">{% trans "صورة" %}</th>
                        <th>{% trans "الكود" %}</th>
                        <th>{% trans "اسم المنتج" %}</th>
                        <th>{% trans "الفئة" %}</th>
                        <th>{% trans "المورد" %}</th>
                        <th>{% trans "الكمية" %}</th>
                        <th>{% trans "سعر البيع" %}</th>
                        <th>{% trans "سعر الشراء" %}</th>
                        <th>{% trans "الحالة" %}</th>
                        <th class="d-none">{% trans "الوصف" %}</th>
                        <th>{% trans "الإجراءات" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for product in products %}
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input product-select" value="{{ product.id }}">
                        </td>
                        <td>
                            {% if product.image %}
                            <img src="{{ product.image.url }}" alt="{{ product.name }}" class="product-image-small">
                            {% else %}
                            <div class="text-center">
                                <i class="fas fa-image text-muted"></i>
                            </div>
                            {% endif %}
                        </td>
                        <td>{{ product.code }}</td>
                        <td>{{ product.name }}</td>
                        <td>{{ product.category.name }}</td>
                        <td>{{ product.supplier.name|default:"-" }}</td>
                        <td>{{ product.quantity }}</td>
                        <td>{{ product.selling_price }} د.م</td>
                        <td>{{ product.purchase_price }} د.م</td>
                        <td>
                            {% if product.quantity == 0 %}
                            <span class="status-out">{% trans "نفد" %}</span>
                            {% elif product.is_low_stock %}
                            <span class="status-low">{% trans "منخفض" %}</span>
                            {% else %}
                            <span class="status-available">{% trans "متوفر" %}</span>
                            {% endif %}
                        </td>
                        <td class="d-none">{{ product.description }}</td>
                        <td>
                            <div class="action-buttons">
                                <div class="btn-group">
                                    <a href="#" class="btn btn-sm btn-info view-product-btn" data-id="{{ product.id }}" data-bs-toggle="modal" data-bs-target="#viewProductModal" title="{% trans 'عرض التفاصيل' %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="#" class="btn btn-sm btn-info stock-movement-btn" data-id="{{ product.id }}" data-name="{{ product.name }}" data-bs-toggle="modal" data-bs-target="#stockMovementModal" title="{% trans 'حركة المخزون' %}">
                                        <i class="fas fa-exchange-alt"></i>
                                    </a>
                                    <a href="{% url 'inventory:edit_product' product.id %}" class="btn btn-sm btn-primary" title="{% trans 'تعديل' %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'inventory:delete_product' product.id %}" class="btn btn-sm btn-danger" title="{% trans 'حذف' %}">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="10" class="text-center">{% trans "لا توجد منتجات" %}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Stock Movement Modal -->
<div class="modal fade" id="stockMovementModal" tabindex="-1" aria-labelledby="stockMovementModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="stockMovementModalLabel">{% trans "حركة المخزون" %}: <span id="productName"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="stockMovementForm">
                    <input type="hidden" id="productId" name="product_id">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="movementType" class="form-label">{% trans "نوع الحركة" %}</label>
                            <select class="form-select" id="movementType" name="movement_type" required>
                                <option value="in">{% trans "وارد (إضافة)" %}</option>
                                <option value="out">{% trans "صادر (سحب)" %}</option>
                                <option value="adjustment">{% trans "تعديل" %}</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="quantity" class="form-label">{% trans "الكمية" %}</label>
                            <input type="number" class="form-control" id="quantity" name="quantity" min="1" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="reference" class="form-label">{% trans "المرجع" %}</label>
                        <input type="text" class="form-control" id="reference" name="reference" placeholder="{% trans 'مثال: فاتورة شراء رقم 123' %}">
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">{% trans "ملاحظات" %}</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </form>

                <hr>

                <h6 class="mb-3">{% trans "سجل الحركات السابقة" %}</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-bordered" id="movementsTable">
                        <thead>
                            <tr>
                                <th>{% trans "التاريخ" %}</th>
                                <th>{% trans "نوع الحركة" %}</th>
                                <th>{% trans "الكمية" %}</th>
                                <th>{% trans "المرجع" %}</th>
                            </tr>
                        </thead>
                        <tbody id="movementsTableBody">
                            <!-- سيتم ملء هذا الجزء ديناميكيًا -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إغلاق" %}</button>
                <button type="button" class="btn btn-primary" id="saveMovementBtn">{% trans "حفظ" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- View Product Modal -->
<div class="modal fade" id="viewProductModal" tabindex="-1" aria-labelledby="viewProductModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewProductModalLabel">{% trans "تفاصيل المنتج" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-5 text-center mb-3">
                        <div id="productImageContainer" class="mb-3">
                            <img id="productImage" src="" alt="صورة المنتج" class="img-fluid rounded" style="max-height: 250px; cursor: pointer;" onclick="openImageInFullScreen(this.src)">
                        </div>
                        <div id="noImageContainer" class="d-none">
                            <div class="border rounded p-5 text-muted">
                                <i class="fas fa-image fa-4x mb-3"></i>
                                <p>{% trans "لا توجد صورة" %}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-7">
                        <h4 id="productName" class="mb-3"></h4>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <tbody>
                                    <tr>
                                        <th width="40%">{% trans "الكود" %}</th>
                                        <td id="productCode"></td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "الفئة" %}</th>
                                        <td id="productCategory"></td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "المورد" %}</th>
                                        <td id="productSupplier"></td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "موقع التخزين" %}</th>
                                        <td id="productStorageLocation"></td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "الكمية المتوفرة" %}</th>
                                        <td id="productQuantity"></td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "سعر البيع" %}</th>
                                        <td id="productSellingPrice"></td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "سعر الشراء" %}</th>
                                        <td id="productPurchasePrice"></td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "الحد الأدنى للكمية" %}</th>
                                        <td id="productMinQuantity"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <h5>{% trans "الوصف" %}</h5>
                        <div id="productDescription" class="border rounded p-3 bg-light"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a href="#" id="editProductLink" class="btn btn-primary">
                    <i class="fas fa-edit me-1"></i> {% trans "تعديل" %}
                </a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إغلاق" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importModalLabel">{% trans "استيراد المنتجات" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="importForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="importFile" class="form-label">{% trans "ملف Excel/CSV" %}</label>
                        <input type="file" class="form-control" id="importFile" name="import_file" accept=".xlsx,.xls,.csv" required>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="updateExisting" name="update_existing">
                        <label class="form-check-label" for="updateExisting">
                            {% trans "تحديث المنتجات الموجودة" %}
                        </label>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-1"></i>
                        {% trans "يجب أن يحتوي الملف على الأعمدة التالية: الكود، الاسم، الفئة، نوع السيارة، سعر الشراء، سعر البيع، الكمية، الحد الأدنى للكمية" %}
                    </div>
                    <div class="mb-3">
                        <a href="#" id="downloadTemplateBtn" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-download me-1"></i> {% trans "تنزيل قالب" %}
                        </a>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إغلاق" %}</button>
                <button type="button" class="btn btn-primary" id="importBtn">{% trans "استيراد" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize DataTable
        var table = $('#productsTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "order": [[3, "asc"]],
            "columnDefs": [
                { "orderable": false, "targets": [0, 1, 11] }
            ],
            "pageLength": 10,
            "lengthMenu": [[5, 10, 15, 20, 25, 50, -1], [5, 10, 15, 20, 25, 50, "الكل"]],
            "dom": 'lfrtip'
        });
        
        // إضافة زر ترتيب حسب سعر الشراء
        $('#sortByPurchasePrice').click(function() {
            table.order([6, 'asc']).draw(); // ترتيب حسب العمود السادس (سعر الشراء) تصاعدياً
        });

        // Select All Checkbox
        $('#selectAll').change(function() {
            $('.product-select').prop('checked', $(this).prop('checked'));
            updateBulkButtons();
        });

        // Individual Checkboxes
        $(document).on('change', '.product-select', function() {
            updateBulkButtons();
        });

        // Update Bulk Buttons State
        function updateBulkButtons() {
            var selectedCount = $('.product-select:checked').length;
            $('#bulkDeleteBtn').prop('disabled', selectedCount === 0);
        }

        // Category Filter
        $('#categoryFilter').change(function() {
            var categoryId = $(this).val();
            table.column(4).search(categoryId ? $(this).find('option:selected').text() : '').draw();
        });

        // Supplier Filter
        $('#supplierFilter').change(function() {
            var supplierId = $(this).val();
            table.column(5).search(supplierId ? $(this).find('option:selected').text() : '').draw();
        });

        // Stock Status Filter
        $('#stockStatusFilter').change(function() {
            var status = $(this).val();
            if (status) {
                var statusText = '';
                if (status === 'available') statusText = 'متوفر';
                else if (status === 'low') statusText = 'منخفض';
                else if (status === 'out') statusText = 'نفد';

                table.column(9).search(statusText).draw(); // تحديث رقم العمود بعد إضافة عمود سعر الشراء
            } else {
                table.column(9).search('').draw(); // تحديث رقم العمود بعد إضافة عمود سعر الشراء
            }
        });

        // Quick Search
        $('#searchBtn').click(function() {
            var searchTerm = $('#quickSearch').val();
            table.search(searchTerm).draw();
        });

        $('#quickSearch').keypress(function(e) {
            if (e.which === 13) {
                var searchTerm = $(this).val();
                table.search(searchTerm).draw();
            }
        });

        // Stock Movement Modal
        $('.stock-movement-btn').click(function() {
            var productId = $(this).data('id');
            var productName = $(this).data('name');

            $('#productId').val(productId);
            $('#productName').text(productName);

            // Load product movements
            $.ajax({
                url: '/inventory/product/' + productId + '/movements/',
                type: 'GET',
                success: function(data) {
                    var tbody = $('#movementsTableBody');
                    tbody.empty();

                    if (data.movements.length > 0) {
                        $.each(data.movements, function(i, movement) {
                            var row = '<tr>' +
                                '<td>' + movement.created_at + '</td>' +
                                '<td>' + movement.movement_type_display + '</td>' +
                                '<td>' + movement.quantity + '</td>' +
                                '<td>' + (movement.reference || '-') + '</td>' +
                                '</tr>';
                            tbody.append(row);
                        });
                    } else {
                        tbody.append('<tr><td colspan="4" class="text-center">لا توجد حركات سابقة</td></tr>');
                    }
                },
                error: function() {
                    $('#movementsTableBody').html('<tr><td colspan="4" class="text-center text-danger">حدث خطأ أثناء تحميل البيانات</td></tr>');
                }
            });
        });

        // Save Stock Movement
        $('#saveMovementBtn').click(function() {
            var form = $('#stockMovementForm');

            if (!form[0].checkValidity()) {
                form[0].reportValidity();
                return;
            }

            var formData = {
                product_id: $('#productId').val(),
                movement_type: $('#movementType').val(),
                quantity: $('#quantity').val(),
                reference: $('#reference').val(),
                notes: $('#notes').val()
            };

            $.ajax({
                url: '/inventory/stock-movement/add/',
                type: 'POST',
                data: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                },
                success: function(response) {
                    if (response.success) {
                        // Show success message
                        alert('تم حفظ حركة المخزون بنجاح');

                        // Close modal and reload page
                        $('#stockMovementModal').modal('hide');
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + response.error);
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error details:", xhr.responseText);
                    alert('حدث خطأ أثناء معالجة الطلب: ' + error);
                }
            });
        });

        // Export Button
        $('#exportBtn').click(function(e) {
            e.preventDefault();
            window.location.href = '/inventory/export/';
        });

        // Import Button
        $('#importBtn').click(function() {
            var form = $('#importForm')[0];

            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            var formData = new FormData(form);

            $.ajax({
                url: '/inventory/import/',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                },
                success: function(response) {
                    if (response.success) {
                        alert('تم استيراد المنتجات بنجاح: ' + response.imported_count + ' منتج');
                        $('#importModal').modal('hide');
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + response.error);
                    }
                },
                error: function() {
                    alert('حدث خطأ أثناء معالجة الطلب');
                }
            });
        });

        // Download Template Button
        $('#downloadTemplateBtn').click(function(e) {
            e.preventDefault();
            window.location.href = '/inventory/export-template/';
        });

        // Bulk Delete Button
        $('#bulkDeleteBtn').click(function() {
            if (confirm('هل أنت متأكد من رغبتك في حذف المنتجات المحددة؟')) {
                var selectedIds = [];
                $('.product-select:checked').each(function() {
                    selectedIds.push($(this).val());
                });

                $.ajax({
                    url: '/inventory/bulk-delete/',
                    type: 'POST',
                    data: {
                        product_ids: selectedIds.join(',')
                    },
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('تم حذف المنتجات بنجاح');
                            location.reload();
                        } else {
                            alert('حدث خطأ: ' + response.error);
                        }
                    },
                    error: function() {
                        alert('حدث خطأ أثناء معالجة الطلب');
                    }
                });
            }
        });

        // View Product Details
        $('.view-product-btn').click(function() {
            var productId = $(this).data('id');

            // Load product details
            $.ajax({
                url: '/inventory/product/' + productId + '/details/',
                type: 'GET',
                success: function(data) {
                    // Fill product details in modal
                    $('#productName').text(data.name);
                    $('#productCode').text(data.code);
                    $('#productCategory').text(data.category_name);
                    $('#productSupplier').text(data.supplier_name);
                    $('#productStorageLocation').text(data.storage_location || '-');
                    $('#productQuantity').text(data.quantity);
                    $('#productSellingPrice').text(data.selling_price + ' د.م');
                    $('#productPurchasePrice').text(data.purchase_price + ' د.م');
                    $('#productMinQuantity').text(data.min_quantity);
                    $('#productDescription').html(data.description || '<span class="text-muted">لا يوجد وصف</span>');

                    // Set edit link
                    $('#editProductLink').attr('href', '/inventory/edit/' + productId + '/');

                    // Handle product image
                    if (data.image_url) {
                        $('#productImage').attr('src', data.image_url);
                        $('#productImageContainer').removeClass('d-none');
                        $('#noImageContainer').addClass('d-none');
                    } else {
                        $('#productImageContainer').addClass('d-none');
                        $('#noImageContainer').removeClass('d-none');
                    }
                },
                error: function() {
                    alert('حدث خطأ أثناء تحميل بيانات المنتج');
                }
            });
        });

        // Function to open image in full screen
        function openImageInFullScreen(src) {
            window.open(src, '_blank');
        }

        // Helper function to get CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    });

    // Define openImageInFullScreen function in global scope
    function openImageInFullScreen(src) {
        window.open(src, '_blank');
    }
</script>
{% endblock %}
