from django.urls import path
from . import views

app_name = 'finance'

urlpatterns = [
    path('', views.dashboard, name='dashboard'),  # تغيير الصفحة الرئيسية إلى لوحة المعلومات
    path('index/', views.index, name='index'),  # الصفحة القديمة

    # المصروفات
    path('expenses/', views.expenses, name='expenses'),
    path('expenses/add/', views.add_expense, name='add_expense'),
    path('expenses/edit/<int:expense_id>/', views.edit_expense, name='edit_expense'),
    path('expenses/delete/<int:expense_id>/', views.delete_expense, name='delete_expense'),

    # الإيرادات
    path('income/', views.income, name='income'),
    path('income/add/', views.add_income, name='add_income'),
    path('income/edit/<int:income_id>/', views.edit_income, name='edit_income'),
    path('income/delete/<int:income_id>/', views.delete_income, name='delete_income'),

    # فئات الإيرادات
    path('income-categories/', views.income_categories, name='income_categories'),
    path('income-categories/add/', views.add_income_category, name='add_income_category'),
    path('income-categories/edit/<int:category_id>/', views.edit_income_category, name='edit_income_category'),
    path('income-categories/delete/<int:category_id>/', views.delete_income_category, name='delete_income_category'),

    # الحسابات
    path('accounts/', views.accounts, name='accounts'),
    path('accounts/add/', views.add_account, name='add_account'),
    path('accounts/edit/<int:account_id>/', views.edit_account, name='edit_account'),
    path('accounts/activate/<int:account_id>/', views.activate_account, name='activate_account'),
    path('accounts/deactivate/<int:account_id>/', views.deactivate_account, name='deactivate_account'),

    # المعاملات المالية
    path('transactions/', views.transactions, name='transactions'),
    path('transactions/<int:transaction_id>/', views.transaction_detail, name='transaction_detail'),
    path('transactions/<int:transaction_id>/add-payment/', views.add_payment_to_transaction, name='add_payment_to_transaction'),

    # التحويلات بين الحسابات
    path('transfers/', views.transfers, name='transfers'),
    path('transfers/add/', views.add_transfer, name='add_transfer'),
]
