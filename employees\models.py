from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.models import User, Permission as DjangoPermission

class Department(models.Model):
    name = models.CharField(_('الاسم'), max_length=100)
    description = models.TextField(_('الوصف'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('قسم')
        verbose_name_plural = _('الأقسام')
        ordering = ['name']

    def __str__(self):
        return self.name

class Position(models.Model):
    name = models.CharField(_('الاسم'), max_length=100)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='positions', verbose_name=_('القسم'))
    description = models.TextField(_('الوصف'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('منصب')
        verbose_name_plural = _('المناصب')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.department.name})"

class EmployeeProfile(models.Model):
    GENDER_CHOICES = (
        ('male', _('ذكر')),
        ('female', _('أنثى')),
    )

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='employee_profile', verbose_name=_('المستخدم'))
    employee_id = models.CharField(_('رقم الموظف'), max_length=20, unique=True)
    position = models.ForeignKey(Position, on_delete=models.SET_NULL, null=True, blank=True, related_name='employees', verbose_name=_('المنصب'))
    role = models.ForeignKey('Role', on_delete=models.SET_NULL, null=True, blank=True, related_name='role_employees', verbose_name=_('الدور الوظيفي'))
    phone = models.CharField(_('رقم الهاتف'), max_length=20)
    address = models.TextField(_('العنوان'), blank=True, null=True)
    date_of_birth = models.DateField(_('تاريخ الميلاد'), blank=True, null=True)
    gender = models.CharField(_('الجنس'), max_length=10, choices=GENDER_CHOICES)
    national_id = models.CharField(_('رقم الهوية'), max_length=20, blank=True, null=True)
    hire_date = models.DateField(_('تاريخ التوظيف'))
    salary = models.DecimalField(_('الراتب'), max_digits=10, decimal_places=2)
    image = models.ImageField(_('الصورة'), upload_to='employees/', blank=True, null=True)
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    is_active = models.BooleanField(_('نشط'), default=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('ملف الموظف')
        verbose_name_plural = _('ملفات الموظفين')
        ordering = ['user__first_name', 'user__last_name']

    def __str__(self):
        return f"{self.user.get_full_name()} ({self.employee_id})"

    @property
    def full_name(self):
        return self.user.get_full_name()

    @property
    def department(self):
        if self.position:
            return self.position.department
        return None

class Attendance(models.Model):
    STATUS_CHOICES = (
        ('present', _('حاضر')),
        ('absent', _('غائب')),
        ('late', _('متأخر')),
        ('half_day', _('نصف يوم')),
        ('leave', _('إجازة')),
    )

    employee = models.ForeignKey(EmployeeProfile, on_delete=models.CASCADE, related_name='attendance', verbose_name=_('الموظف'))
    date = models.DateField(_('التاريخ'))
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES)
    check_in = models.TimeField(_('وقت الحضور'), blank=True, null=True)
    check_out = models.TimeField(_('وقت الانصراف'), blank=True, null=True)
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('حضور')
        verbose_name_plural = _('الحضور')
        ordering = ['-date']
        unique_together = ['employee', 'date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.date} - {self.get_status_display()}"

class Leave(models.Model):
    LEAVE_TYPE_CHOICES = (
        ('annual', _('سنوية')),
        ('sick', _('مرضية')),
        ('emergency', _('طارئة')),
        ('unpaid', _('غير مدفوعة')),
        ('other', _('أخرى')),
    )

    STATUS_CHOICES = (
        ('pending', _('معلق')),
        ('approved', _('مقبولة')),
        ('rejected', _('مرفوضة')),
        ('cancelled', _('ملغاة')),
    )

    employee = models.ForeignKey(EmployeeProfile, on_delete=models.CASCADE, related_name='leaves', verbose_name=_('الموظف'))
    leave_type = models.CharField(_('نوع الإجازة'), max_length=20, choices=LEAVE_TYPE_CHOICES)
    start_date = models.DateField(_('تاريخ البداية'))
    end_date = models.DateField(_('تاريخ النهاية'))
    reason = models.TextField(_('السبب'))
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='pending')
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_leaves', verbose_name=_('تمت الموافقة من قبل'))
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('إجازة')
        verbose_name_plural = _('الإجازات')
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.get_leave_type_display()} ({self.start_date} to {self.end_date})"

    @property
    def days_count(self):
        delta = self.end_date - self.start_date
        return delta.days + 1


class CustomPermission(models.Model):
    """
    نموذج للصلاحيات المخصصة للنظام
    """
    name = models.CharField(_('الاسم'), max_length=100)
    code = models.CharField(_('الرمز'), max_length=50, unique=True)
    description = models.TextField(_('الوصف'), blank=True, null=True)
    category = models.CharField(_('الفئة'), max_length=50, default='general')
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('صلاحية')
        verbose_name_plural = _('الصلاحيات')
        ordering = ['category', 'name']

    def __str__(self):
        return self.name


class Role(models.Model):
    """
    نموذج للأدوار الوظيفية
    """
    name = models.CharField(_('الاسم'), max_length=100)
    description = models.TextField(_('الوصف'), blank=True, null=True)
    permissions = models.ManyToManyField(CustomPermission, verbose_name=_('الصلاحيات'), blank=True)
    is_active = models.BooleanField(_('نشط'), default=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('دور وظيفي')
        verbose_name_plural = _('الأدوار الوظيفية')

    def __str__(self):
        return self.name


# تم حذف السطر الذي يحاول إضافة حقل role إلى نموذج EmployeeProfile بعد تعريفه


class EmployeeAlert(models.Model):
    """
    نموذج لتنبيهات الموظفين
    """
    ALERT_TYPES = (
        ('absence', _('غياب')),
        ('late', _('تأخير')),
        ('contract', _('انتهاء العقد')),
        ('performance', _('أداء')),
        ('other', _('أخرى')),
    )

    employee = models.ForeignKey(EmployeeProfile, on_delete=models.CASCADE, related_name='alerts', verbose_name=_('الموظف'))
    alert_type = models.CharField(_('نوع التنبيه'), max_length=20, choices=ALERT_TYPES)
    message = models.TextField(_('الرسالة'))
    is_read = models.BooleanField(_('مقروء'), default=False)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)

    class Meta:
        verbose_name = _('تنبيه موظف')
        verbose_name_plural = _('تنبيهات الموظفين')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.employee.full_name} - {self.get_alert_type_display()}"


class AttendanceSettings(models.Model):
    """
    نموذج لإعدادات الحضور والانصراف
    """
    work_start_time = models.TimeField(_('وقت بدء العمل'))
    work_end_time = models.TimeField(_('وقت انتهاء العمل'))
    late_threshold_minutes = models.PositiveIntegerField(_('حد التأخير بالدقائق'), default=15)
    absence_threshold_days = models.PositiveIntegerField(_('حد الغياب بالأيام للتنبيه'), default=3)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('إعدادات الحضور')
        verbose_name_plural = _('إعدادات الحضور')

    def __str__(self):
        return f"{self.work_start_time} - {self.work_end_time}"


class PerformanceReview(models.Model):
    """
    نموذج لتقييم أداء الموظفين
    """
    RATING_CHOICES = (
        (1, _('ضعيف')),
        (2, _('مقبول')),
        (3, _('جيد')),
        (4, _('جيد جداً')),
        (5, _('ممتاز')),
    )

    employee = models.ForeignKey(EmployeeProfile, on_delete=models.CASCADE, related_name='performance_reviews', verbose_name=_('الموظف'))
    reviewer = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name=_('المقيم'))
    review_date = models.DateField(_('تاريخ التقييم'))
    period_start = models.DateField(_('بداية فترة التقييم'))
    period_end = models.DateField(_('نهاية فترة التقييم'))

    attendance_rating = models.PositiveSmallIntegerField(_('تقييم الحضور'), choices=RATING_CHOICES)
    productivity_rating = models.PositiveSmallIntegerField(_('تقييم الإنتاجية'), choices=RATING_CHOICES)
    quality_rating = models.PositiveSmallIntegerField(_('تقييم الجودة'), choices=RATING_CHOICES)
    teamwork_rating = models.PositiveSmallIntegerField(_('تقييم العمل الجماعي'), choices=RATING_CHOICES)

    comments = models.TextField(_('ملاحظات'), blank=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('تقييم أداء')
        verbose_name_plural = _('تقييمات الأداء')
        ordering = ['-review_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.review_date}"

    @property
    def average_rating(self):
        ratings = [self.attendance_rating, self.productivity_rating, self.quality_rating, self.teamwork_rating]
        return sum(ratings) / len(ratings)
