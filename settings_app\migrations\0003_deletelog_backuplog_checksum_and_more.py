# Generated by Django 5.2 on 2025-04-22 12:17

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('settings_app', '0002_backupsetting_currencysetting_emailsetting_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='DeleteLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('backup_filename', models.CharField(max_length=255, verbose_name='اسم ملف النسخة الاحتياطية')),
                ('backup_type', models.CharField(max_length=20, verbose_name='نوع النسخ الاحتياطي')),
                ('backup_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ النسخة الاحتياطية')),
                ('status', models.CharField(choices=[('success', 'ناجح'), ('failed', 'فاشل')], max_length=15, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'سجل الحذف',
                'verbose_name_plural': 'سجلات الحذف',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='backuplog',
            name='checksum',
            field=models.CharField(blank=True, max_length=64, null=True, verbose_name='التحقق من السلامة'),
        ),
        migrations.AddField(
            model_name='backuplog',
            name='cloud_storage_id',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='معرف التخزين السحابي'),
        ),
        migrations.AddField(
            model_name='backuplog',
            name='cloud_storage_link',
            field=models.URLField(blank=True, null=True, verbose_name='رابط التخزين السحابي'),
        ),
        migrations.AddField(
            model_name='backuplog',
            name='cloud_storage_type',
            field=models.CharField(choices=[('local', 'محلي'), ('google_drive', 'Google Drive'), ('dropbox', 'Dropbox')], default='local', max_length=20, verbose_name='نوع التخزين السحابي'),
        ),
        migrations.AddField(
            model_name='backuplog',
            name='encryption_key',
            field=models.TextField(blank=True, null=True, verbose_name='مفتاح التشفير'),
        ),
        migrations.AddField(
            model_name='backuplog',
            name='include_media',
            field=models.BooleanField(default=True, verbose_name='تضمين الوسائط'),
        ),
        migrations.AddField(
            model_name='backuplog',
            name='is_encrypted',
            field=models.BooleanField(default=False, verbose_name='مشفر'),
        ),
        migrations.AddField(
            model_name='backupsetting',
            name='encrypt_backups',
            field=models.BooleanField(default=False, verbose_name='تشفير النسخ الاحتياطية'),
        ),
        migrations.AddField(
            model_name='backupsetting',
            name='encryption_algorithm',
            field=models.CharField(choices=[('aes256', 'AES-256'), ('aes192', 'AES-192'), ('aes128', 'AES-128')], default='aes256', max_length=10, verbose_name='خوارزمية التشفير'),
        ),
        migrations.AddField(
            model_name='backupsetting',
            name='keep_min_backups',
            field=models.PositiveIntegerField(default=5, help_text='الحد الأدنى لعدد النسخ الاحتياطية للاحتفاظ بها', verbose_name='الحد الأدنى للنسخ الاحتياطية'),
        ),
        migrations.AddField(
            model_name='backupsetting',
            name='notification_email',
            field=models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني للإشعارات'),
        ),
        migrations.AddField(
            model_name='backupsetting',
            name='notify_on_failure',
            field=models.BooleanField(default=True, verbose_name='إشعار عند الفشل'),
        ),
        migrations.AddField(
            model_name='backupsetting',
            name='notify_on_success',
            field=models.BooleanField(default=False, verbose_name='إشعار عند النجاح'),
        ),
        migrations.AlterField(
            model_name='backuplog',
            name='backup_type',
            field=models.CharField(choices=[('auto', 'تلقائي'), ('manual', 'يدوي'), ('scheduled', 'مجدول')], max_length=10, verbose_name='نوع النسخ الاحتياطي'),
        ),
        migrations.AlterField(
            model_name='backuplog',
            name='status',
            field=models.CharField(choices=[('success', 'ناجح'), ('failed', 'فاشل'), ('in_progress', 'جاري')], max_length=15, verbose_name='الحالة'),
        ),
        migrations.CreateModel(
            name='RestoreLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('success', 'ناجح'), ('failed', 'فاشل'), ('in_progress', 'جاري')], max_length=15, verbose_name='الحالة')),
                ('is_partial', models.BooleanField(default=False, verbose_name='استعادة جزئية')),
                ('restored_items', models.TextField(blank=True, null=True, verbose_name='العناصر المستعادة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('backup_log', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='settings_app.backuplog', verbose_name='سجل النسخ الاحتياطي')),
            ],
            options={
                'verbose_name': 'سجل الاستعادة',
                'verbose_name_plural': 'سجلات الاستعادة',
                'ordering': ['-created_at'],
            },
        ),
    ]
