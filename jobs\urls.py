from django.urls import path
from . import views

app_name = 'jobs'

urlpatterns = [
    # عرض قائمة الأعمال
    path('', views.index, name='index'),
    
    # إنشاء عمل جديد
    path('new/', views.new_job, name='new_job'),
    
    # عرض تفاصيل عمل
    path('<int:job_id>/', views.view_job, name='view_job'),
    
    # تعديل عمل
    path('<int:job_id>/edit/', views.edit_job, name='edit_job'),
    
    # حذف عمل
    path('<int:job_id>/delete/', views.delete_job, name='delete_job'),
    
    # إضافة عنصر إلى العمل
    path('<int:job_id>/add-item/', views.add_job_item, name='add_job_item'),
    
    # حذف عنصر من العمل
    path('item/<int:item_id>/delete/', views.delete_job_item, name='delete_job_item'),
    
    # إضافة خدمة إلى العمل
    path('<int:job_id>/add-service/', views.add_job_service, name='add_job_service'),
    
    # حذف خدمة من العمل
    path('service/<int:service_id>/delete/', views.delete_job_service, name='delete_job_service'),
    
    # إضافة مرفق إلى العمل
    path('<int:job_id>/add-attachment/', views.add_job_attachment, name='add_job_attachment'),
    
    # حذف مرفق من العمل
    path('attachment/<int:attachment_id>/delete/', views.delete_job_attachment, name='delete_job_attachment'),
    
    # إضافة تعليق إلى العمل
    path('<int:job_id>/add-comment/', views.add_job_comment, name='add_job_comment'),
    
    # حذف تعليق من العمل
    path('comment/<int:comment_id>/delete/', views.delete_job_comment, name='delete_job_comment'),
    
    # تغيير حالة العمل
    path('<int:job_id>/change-status/', views.change_job_status, name='change_job_status'),
    
    # طباعة تقرير العمل
    path('<int:job_id>/print/', views.print_job, name='print_job'),
    
    # إرسال تقرير العمل بالبريد الإلكتروني
    path('<int:job_id>/email/', views.email_job, name='email_job'),
    
    # تقارير الأعمال
    path('reports/', views.job_reports, name='job_reports'),
    
    # AJAX endpoints
    path('api/get-customer-cars/', views.get_customer_cars, name='get_customer_cars'),
    path('api/get-product-price/', views.get_product_price, name='get_product_price'),
]
