{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "لوحة التحكم" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<style>
    .dashboard-card {
        transition: transform 0.3s;
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
    }
    .stat-icon {
        font-size: 2.5rem;
        opacity: 0.8;
    }

    .dataTables_length {
        margin-bottom: 15px;
        margin-right: 15px;
    }

    .dataTables_length select {
        padding: 0.375rem 2.25rem 0.375rem 0.75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #212529;
        background-color: #fff;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .dataTables_info {
        padding-top: 0.85em;
        white-space: nowrap;
        margin-right: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "لوحة التحكم" %}</h1>
        <div>
            <span class="text-muted">{% now "l, j F Y" %}</span>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <!-- Sales Today -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-primary shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                {% trans "مبيعات اليوم" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_sales_today|floatformat:2 }} {{ currency.symbol }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-day stat-icon text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sales This Week -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-success shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                {% trans "مبيعات الأسبوع" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_sales_week|floatformat:2 }} {{ currency.symbol }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-week stat-icon text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sales This Month -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-info shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                {% trans "مبيعات الشهر" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_sales_month|floatformat:2 }} {{ currency.symbol }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-alt stat-icon text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Monthly Profit -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-warning shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                {% trans "أرباح الشهر" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ profit_month|floatformat:2 }} {{ currency.symbol }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign stat-icon text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Second Row of Statistics -->
    <div class="row mb-4">
        <!-- Total Products -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-secondary shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                {% trans "إجمالي المنتجات" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_products }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-boxes stat-icon text-secondary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Low Stock Products -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-danger shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                {% trans "منتجات منخفضة المخزون" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ low_stock_products }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle stat-icon text-danger"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Customers -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-primary shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                {% trans "إجمالي العملاء" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_customers }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users stat-icon text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- New Customers This Month -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-success shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                {% trans "عملاء جدد هذا الشهر" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ new_customers_month }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-plus stat-icon text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Recent Sales -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "أحدث المبيعات" %}</h6>
                    <a href="{% url 'sales:index' %}" class="btn btn-sm btn-primary">
                        {% trans "عرض الكل" %}
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="recentSalesTable">
                            <thead class="thead-light">
                                <tr>
                                    <th>{% trans "رقم الفاتورة" %}</th>
                                    <th>{% trans "العميل" %}</th>
                                    <th>{% trans "التاريخ" %}</th>
                                    <th>{% trans "المبلغ" %}</th>
                                    <th>{% trans "الحالة" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sale in recent_sales %}
                                <tr>
                                    <td>
                                        <a href="{% url 'sales:view_sale' sale.id %}">{{ sale.invoice_number }}</a>
                                    </td>
                                    <td>{{ sale.customer.name }}</td>
                                    <td>{{ sale.date|date:"Y-m-d H:i" }}</td>
                                    <td>{{ sale.total_amount|floatformat:2 }} {{ currency.symbol }}</td>
                                    <td>
                                        {% if sale.status == 'completed' %}
                                        <span class="badge badge-success">{% trans "مكتمل" %}</span>
                                        {% elif sale.status == 'pending' %}
                                        <span class="badge badge-warning">{% trans "معلق" %}</span>
                                        {% else %}
                                        <span class="badge badge-danger">{% trans "ملغي" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center">{% trans "لا توجد مبيعات حديثة" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Low Stock Products -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-danger">{% trans "منتجات منخفضة المخزون" %}</h6>
                    <a href="{% url 'inventory:index' %}" class="btn btn-sm btn-danger">
                        {% trans "عرض الكل" %}
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="lowStockTable">
                            <thead class="thead-light">
                                <tr>
                                    <th>{% trans "الكود" %}</th>
                                    <th>{% trans "المنتج" %}</th>
                                    <th>{% trans "الكمية الحالية" %}</th>
                                    <th>{% trans "الحد الأدنى" %}</th>
                                    <th>{% trans "الإجراء" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in low_stock_items %}
                                <tr>
                                    <td>{{ product.code }}</td>
                                    <td>{{ product.name }}</td>
                                    <td class="{% if product.quantity == 0 %}text-danger font-weight-bold{% endif %}">
                                        {{ product.quantity }}
                                    </td>
                                    <td>{{ product.min_quantity }}</td>
                                    <td>
                                        <a href="{% url 'inventory:edit_product' product.id %}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center">{% trans "لا توجد منتجات منخفضة المخزون" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Third Row -->
    <div class="row">
        <!-- Top Selling Products -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "المنتجات الأكثر مبيعًا" %}</h6>
                </div>
                <div class="card-body">
                    {% if top_products %}
                    <div class="chart-bar">
                        <canvas id="topProductsChart"></canvas>
                    </div>
                    {% else %}
                    <p class="text-center">{% trans "لا توجد بيانات كافية لعرض المنتجات الأكثر مبيعًا" %}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Product Movements -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">{% trans "حركات المخزون الأخيرة" %}</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="recentMovementsTable">
                            <thead class="thead-light">
                                <tr>
                                    <th>{% trans "المنتج" %}</th>
                                    <th>{% trans "النوع" %}</th>
                                    <th>{% trans "الكمية" %}</th>
                                    <th>{% trans "التاريخ" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for movement in recent_movements %}
                                <tr>
                                    <td>{{ movement.product.name }}</td>
                                    <td>
                                        {% if movement.movement_type == 'in' %}
                                        <span class="badge badge-success">{% trans "وارد" %}</span>
                                        {% elif movement.movement_type == 'out' %}
                                        <span class="badge badge-danger">{% trans "صادر" %}</span>
                                        {% else %}
                                        <span class="badge badge-info">{% trans "تعديل" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ movement.quantity }}</td>
                                    <td>{{ movement.created_at|date:"Y-m-d H:i" }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center">{% trans "لا توجد حركات مخزون حديثة" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Top Products Chart
        {% if top_products %}
        var ctx = document.getElementById('topProductsChart').getContext('2d');
        var topProductsChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: [
                    {% for product in top_products %}
                    "{{ product.product__name }}",
                    {% endfor %}
                ],
                datasets: [{
                    label: '{% trans "الكمية المباعة" %}',
                    data: [
                        {% for product in top_products %}
                        {{ product.total_quantity }},
                        {% endfor %}
                    ],
                    backgroundColor: [
                        'rgba(78, 115, 223, 0.7)',
                        'rgba(28, 200, 138, 0.7)',
                        'rgba(54, 185, 204, 0.7)',
                        'rgba(246, 194, 62, 0.7)',
                        'rgba(231, 74, 59, 0.7)'
                    ],
                    borderColor: [
                        'rgba(78, 115, 223, 1)',
                        'rgba(28, 200, 138, 1)',
                        'rgba(54, 185, 204, 1)',
                        'rgba(246, 194, 62, 1)',
                        'rgba(231, 74, 59, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        {% endif %}

        // تهيئة جداول DataTables
        // جدول أحدث المبيعات
        $('#recentSalesTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20, -1], [5, 10, 15, 20, "الكل"]],
            "dom": 'lfrtip',
            "ordering": false,
            "searching": false,
            "paging": true
        });

        // جدول منتجات منخفضة المخزون
        $('#lowStockTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20, -1], [5, 10, 15, 20, "الكل"]],
            "dom": 'lfrtip',
            "ordering": false,
            "searching": false,
            "paging": true
        });

        // جدول حركات المخزون الأخيرة
        $('#recentMovementsTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20, -1], [5, 10, 15, 20, "الكل"]],
            "dom": 'lfrtip',
            "ordering": false,
            "searching": false,
            "paging": true
        });
    });
</script>
{% endblock %}
