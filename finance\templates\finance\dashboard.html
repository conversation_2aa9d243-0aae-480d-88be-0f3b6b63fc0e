{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "لوحة المعلومات المالية" %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <h5 class="card-title">{% trans "إجمالي الرصيد" %}</h5>
                    <h3 class="card-text">{{ total_balance }} {% trans "د.م" %}</h3>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a href="{% url 'finance:accounts' %}" class="text-white">{% trans "عرض الحسابات" %}</a>
                    <i class="fas fa-wallet fa-2x"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <h5 class="card-title">{% trans "إجمالي الإيرادات" %}</h5>
                    <h3 class="card-text">{{ total_income }} {% trans "د.م" %}</h3>
                    <small>{% trans "هذا الشهر" %}</small>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a href="{% url 'finance:income' %}" class="text-white">{% trans "عرض الإيرادات" %}</a>
                    <i class="fas fa-arrow-up fa-2x"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white h-100">
                <div class="card-body">
                    <h5 class="card-title">{% trans "إجمالي المصروفات" %}</h5>
                    <h3 class="card-text">{{ total_expenses }} {% trans "د.م" %}</h3>
                    <small>{% trans "هذا الشهر" %}</small>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a href="{% url 'finance:expenses' %}" class="text-white">{% trans "عرض المصروفات" %}</a>
                    <i class="fas fa-arrow-down fa-2x"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card {% if net_profit >= 0 %}bg-info{% else %}bg-warning{% endif %} text-white h-100">
                <div class="card-body">
                    <h5 class="card-title">{% trans "صافي الربح" %}</h5>
                    <h3 class="card-text">{{ net_profit }} {% trans "د.م" %}</h3>
                    <small>{% trans "هذا الشهر" %}</small>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a href="{% url 'finance:transactions' %}" class="text-white">{% trans "عرض المعاملات" %}</a>
                    <i class="fas fa-chart-line fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- الحسابات -->
        <div class="col-md-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{% trans "الحسابات" %}</h5>
                    <a href="{% url 'finance:accounts' %}" class="btn btn-light btn-sm">
                        <i class="fas fa-external-link-alt"></i>
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "الحساب" %}</th>
                                    <th>{% trans "النوع" %}</th>
                                    <th>{% trans "الرصيد" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for account in accounts %}
                                <tr>
                                    <td>{{ account.name }}</td>
                                    <td>{{ account.get_account_type_display }}</td>
                                    <td class="{% if account.current_balance < 0 %}text-danger{% elif account.current_balance > 0 %}text-success{% endif %}">
                                        {{ account.current_balance }} {% trans "د.م" %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="3" class="text-center">{% trans "لا توجد حسابات مضافة بعد" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- المعاملات الأخيرة -->
        <div class="col-md-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{% trans "المعاملات الأخيرة" %}</h5>
                    <a href="{% url 'finance:transactions' %}" class="btn btn-light btn-sm">
                        <i class="fas fa-external-link-alt"></i>
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "التاريخ" %}</th>
                                    <th>{% trans "النوع" %}</th>
                                    <th>{% trans "المبلغ" %}</th>
                                    <th>{% trans "الحالة" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in recent_transactions %}
                                <tr>
                                    <td>{{ transaction.date }}</td>
                                    <td>{{ transaction.get_transaction_type_display }}</td>
                                    <td>{{ transaction.amount }} {% trans "د.م" %}</td>
                                    <td>
                                        {% if transaction.payment_status == 'paid' %}
                                        <span class="badge bg-success">{% trans "مدفوعة" %}</span>
                                        {% elif transaction.payment_status == 'partial' %}
                                        <span class="badge bg-warning">{% trans "جزئية" %}</span>
                                        {% else %}
                                        <span class="badge bg-danger">{% trans "غير مدفوعة" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center">{% trans "لا توجد معاملات مسجلة بعد" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تنبيهات مالية -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{% trans "تنبيهات مالية" %}</h5>
                    <span class="badge bg-danger">{{ unpaid_transactions.count }}</span>
                </div>
                <div class="card-body">
                    {% if unpaid_transactions %}
                    <div class="alert alert-warning">
                        <h6>{% trans "معاملات غير مدفوعة" %}</h6>
                        <p>{% trans "لديك" %} {{ unpaid_transactions.count }} {% trans "معاملة غير مدفوعة بقيمة" %} {{ total_unpaid }} {% trans "د.م" %}</p>
                        <a href="{% url 'finance:transactions' %}?status=unpaid" class="btn btn-sm btn-warning">{% trans "عرض المعاملات غير المدفوعة" %}</a>
                    </div>
                    {% else %}
                    <div class="alert alert-success">
                        <h6>{% trans "لا توجد تنبيهات" %}</h6>
                        <p>{% trans "جميع المعاملات المالية مدفوعة بالكامل" %}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
