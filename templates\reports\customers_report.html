{% extends 'base.html' %}
{% load i18n %}
{% load static %}
{% load report_filters %}

{% block title %}{% trans "تقرير العملاء" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .chart-container {
        position: relative;
        height: 300px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "تقرير العملاء" %}</h1>
        <div>
            <a href="{% url 'reports:index' %}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i> {% trans "العودة" %}
            </a>
            <div class="btn-group">
                <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-download me-1"></i> {% trans "تصدير" %}
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="#">PDF</a></li>
                    <li><a class="dropdown-item" href="#">Excel</a></li>
                    <li><a class="dropdown-item" href="#">CSV</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Customers Summary Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                {% trans "إجمالي العملاء" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_customers }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                {% trans "متوسط المشتريات" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% if total_customers > 0 and top_customers %}
                                    {{ top_customers.total_spent__sum|default:0|floatformat:2 }} {% trans "د.م." %}
                                {% else %}
                                    0 {% trans "د.م." %}
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                {% trans "العملاء النشطين" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ customers.active_count|default:0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                {% trans "إجمالي الرصيد" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ customers.total_balance|default:0|floatformat:2 }} {% trans "د.م." %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-wallet fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Top Customers Chart -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "أفضل العملاء من حيث المشتريات" %}</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="topCustomersChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customers by Category Chart -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "العملاء حسب الفئة" %}</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="customersByCategoryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Customers Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "أفضل العملاء" %}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="topCustomersTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>{% trans "العميل" %}</th>
                            <th>{% trans "الفئة" %}</th>
                            <th>{% trans "عدد المشتريات" %}</th>
                            <th>{% trans "إجمالي المشتريات" %}</th>
                            <th>{% trans "آخر شراء" %}</th>
                            <th>{% trans "الرصيد الحالي" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customer in top_customers %}
                        <tr>
                            <td>{{ customer.name }}</td>
                            <td>{{ customer.category.name|default:"-" }}</td>
                            <td>{{ customer.purchases_count }}</td>
                            <td>{{ customer.total_spent|floatformat:2 }} {% trans "د.م." %}</td>
                            <td>{{ customer.last_purchase_date|date:"Y-m-d"|default:"-" }}</td>
                            <td>{{ customer.balance|floatformat:2 }} {% trans "د.م." %}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">{% trans "لا توجد بيانات" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- All Customers Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "جميع العملاء" %}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="customersTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>{% trans "العميل" %}</th>
                            <th>{% trans "رقم الهاتف" %}</th>
                            <th>{% trans "البريد الإلكتروني" %}</th>
                            <th>{% trans "المدينة" %}</th>
                            <th>{% trans "الفئة" %}</th>
                            <th>{% trans "حد الائتمان" %}</th>
                            <th>{% trans "الرصيد" %}</th>
                            <th>{% trans "الحالة" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customer in customers %}
                        <tr>
                            <td>{{ customer.name }}</td>
                            <td>{{ customer.phone }}</td>
                            <td>{{ customer.email|default:"-" }}</td>
                            <td>{{ customer.city|default:"-" }}</td>
                            <td>{{ customer.category.name|default:"-" }}</td>
                            <td>{{ customer.credit_limit|floatformat:2 }}</td>
                            <td>{{ customer.balance|floatformat:2 }}</td>
                            <td>
                                {% if customer.is_active %}
                                <span class="badge bg-success">{% trans "نشط" %}</span>
                                {% else %}
                                <span class="badge bg-secondary">{% trans "غير نشط" %}</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center">{% trans "لا يوجد عملاء" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/chart.min.js' %}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize DataTables
        $('#customersTable').DataTable({
            "language": {
                "url": "{% static 'js/dataTables.arabic.json' %}"
            },
            "order": [[0, "asc"]]
        });

        $('#topCustomersTable').DataTable({
            "language": {
                "url": "{% static 'js/dataTables.arabic.json' %}"
            },
            "order": [[3, "desc"]],
            "paging": false,
            "searching": false
        });

        // Top Customers Chart
        const topCustomersCtx = document.getElementById('topCustomersChart').getContext('2d');
        const topCustomersChart = new Chart(topCustomersCtx, {
            type: 'bar',
            data: {
                labels: [
                    {% for customer in top_customers|slice:":5" %}
                        '{{ customer.name }}',
                    {% endfor %}
                ],
                datasets: [{
                    label: '{% trans "إجمالي المشتريات" %}',
                    data: [
                        {% for customer in top_customers|slice:":5" %}
                            {{ customer.total_spent|default:0 }},
                        {% endfor %}
                    ],
                    backgroundColor: 'rgba(78, 115, 223, 0.8)',
                    borderColor: 'rgba(78, 115, 223, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value + ' {% trans "د.م." %}';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                var label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += context.parsed.y + ' {% trans "د.م." %}';
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });

        // Customers by Category Chart - This is a placeholder, you would need to modify this with actual data
        const categoryCtx = document.getElementById('customersByCategoryChart').getContext('2d');
        const customersByCategoryChart = new Chart(categoryCtx, {
            type: 'pie',
            data: {
                labels: [
                    // This would be populated with actual category names from your data
                    '{% trans "عادي" %}',
                    '{% trans "VIP" %}',
                    '{% trans "تاجر" %}',
                    '{% trans "ورشة" %}',
                    '{% trans "شركة" %}'
                ],
                datasets: [{
                    data: [
                        // This would be populated with actual counts from your data
                        45, 20, 15, 10, 5
                    ],
                    backgroundColor: [
                        '#4e73df',
                        '#1cc88a',
                        '#36b9cc',
                        '#f6c23e',
                        '#e74a3b'
                    ],
                    hoverBackgroundColor: [
                        '#2e59d9',
                        '#17a673',
                        '#2c9faf',
                        '#dda20a',
                        '#be2617'
                    ],
                    hoverBorderColor: "rgba(234, 236, 244, 1)",
                }]
            },
            options: {
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: "rgb(255,255,255)",
                        bodyColor: "#858796",
                        borderColor: '#dddfeb',
                        borderWidth: 1,
                        xPadding: 15,
                        yPadding: 15,
                        displayColors: false,
                        caretPadding: 10,
                    }
                },
                cutout: '70%'
            }
        });
    });
</script>
{% endblock %}
