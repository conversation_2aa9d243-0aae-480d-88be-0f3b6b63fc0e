# Generated by Django 5.2 on 2025-04-18 17:52

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='StorageLocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'مكان التخزين',
                'verbose_name_plural': 'أماكن التخزين',
                'ordering': ['name'],
            },
        ),
        migrations.RemoveField(
            model_name='product',
            name='car_type',
        ),
        migrations.AddField(
            model_name='product',
            name='storage_location',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='products', to='inventory.storagelocation', verbose_name='مكان التخزين'),
        ),
        migrations.DeleteModel(
            name='CarType',
        ),
    ]
