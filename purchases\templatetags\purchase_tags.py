from django import template
from django.db.models import Sum

register = template.Library()

@register.filter
def sum(queryset, attr):
    """Sum a queryset by attribute"""
    if not queryset:
        return 0
    return queryset.aggregate(total=Sum(attr))['total'] or 0

@register.filter
def sub(value, queryset_sum):
    """Subtract a queryset sum from a value"""
    return value - queryset_sum

@register.filter
def mul(value, arg):
    """Multiply a value by an argument"""
    return value * arg

@register.filter
def div(value, arg):
    """Divide a value by an argument"""
    if arg == 0:
        return 0
    return value / arg

@register.filter
def filter_by_attr(queryset, arg):
    """Filter a queryset by attribute value
    Usage: {{ queryset|filter_by_attr:"attribute,value" }}
    """
    if not queryset:
        return queryset
    
    args = arg.split(',')
    if len(args) != 2:
        return queryset
    
    attr, value = args
    kwargs = {attr: value}
    return queryset.filter(**kwargs)

@register.filter
def filter_by_id(queryset, id_value):
    """Filter a queryset by id"""
    if not queryset:
        return []
    
    return queryset.filter(id=id_value)

@register.filter
def get_attr(obj, attr):
    """Get an attribute from an object"""
    if not obj:
        return ""
    
    return getattr(obj, attr, "")
