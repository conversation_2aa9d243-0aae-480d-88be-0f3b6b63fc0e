# Generated by Django 5.2 on 2025-04-21 09:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0002_storagelocation_remove_product_car_type_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='has_promotion',
            field=models.BooleanField(default=False, verbose_name='يوجد عرض خاص'),
        ),
        migrations.AddField(
            model_name='product',
            name='promotion_description',
            field=models.TextField(blank=True, null=True, verbose_name='وصف العرض'),
        ),
        migrations.AddField(
            model_name='product',
            name='promotion_end_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ نهاية العرض'),
        ),
        migrations.AddField(
            model_name='product',
            name='promotion_price',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='سعر العرض'),
        ),
        migrations.AddField(
            model_name='product',
            name='promotion_start_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ بداية العرض'),
        ),
    ]
