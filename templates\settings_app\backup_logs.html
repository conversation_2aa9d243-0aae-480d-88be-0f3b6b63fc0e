{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/settings.css' %}">
<style>
    .log-success {
        color: #1cc88a;
    }
    .log-failed {
        color: #e74a3b;
    }
    .log-warning {
        color: #f6c23e;
    }
    .log-info {
        color: #36b9cc;
    }
    .log-details {
        max-height: 100px;
        overflow-y: auto;
        font-size: 0.85rem;
        background-color: #f8f9fc;
        padding: 10px;
        border-radius: 5px;
        border: 1px solid #eaecf4;
    }
    .filter-section {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block title %}{% trans "سجل عمليات النسخ الاحتياطي" %} | {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "سجل عمليات النسخ الاحتياطي" %}</h1>
        <div>
            <a href="{% url 'settings_app:backup' %}" class="btn btn-primary">
                <i class="fas fa-arrow-left me-1"></i> {% trans "العودة إلى النسخ الاحتياطي" %}
            </a>
        </div>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- قسم التصفية -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "تصفية السجلات" %}</h6>
        </div>
        <div class="card-body">
            <form method="get" action="{% url 'settings_app:backup_logs' %}" class="row g-3">
                <div class="col-md-3">
                    <label for="operation_type" class="form-label">{% trans "نوع العملية" %}</label>
                    <select class="form-select" id="operation_type" name="operation_type">
                        <option value="">{% trans "الكل" %}</option>
                        <option value="backup" {% if request.GET.operation_type == 'backup' %}selected{% endif %}>{% trans "نسخ احتياطي" %}</option>
                        <option value="restore" {% if request.GET.operation_type == 'restore' %}selected{% endif %}>{% trans "استعادة" %}</option>
                        <option value="delete" {% if request.GET.operation_type == 'delete' %}selected{% endif %}>{% trans "حذف" %}</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">{% trans "الحالة" %}</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">{% trans "الكل" %}</option>
                        <option value="success" {% if request.GET.status == 'success' %}selected{% endif %}>{% trans "ناجح" %}</option>
                        <option value="failed" {% if request.GET.status == 'failed' %}selected{% endif %}>{% trans "فاشل" %}</option>
                        <option value="in_progress" {% if request.GET.status == 'in_progress' %}selected{% endif %}>{% trans "جاري" %}</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="date_from" class="form-label">{% trans "من تاريخ" %}</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request.GET.date_from }}">
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label">{% trans "إلى تاريخ" %}</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request.GET.date_to }}">
                </div>
                <div class="col-12 mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i> {% trans "تصفية" %}
                    </button>
                    <a href="{% url 'settings_app:backup_logs' %}" class="btn btn-secondary">
                        <i class="fas fa-redo me-1"></i> {% trans "إعادة ضبط" %}
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول سجل العمليات -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "سجل العمليات" %}</h6>
            <div class="dropdown no-arrow">
                <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                    <div class="dropdown-header">{% trans "خيارات" %}:</div>
                    <a class="dropdown-item" href="#" id="exportLogsBtn">
                        <i class="fas fa-file-export me-1"></i> {% trans "تصدير السجلات" %}
                    </a>
                    <div class="dropdown-divider"></div>
                    <form method="post" action="{% url 'settings_app:backup_logs' %}" id="clearLogsForm">
                        {% csrf_token %}
                        <input type="hidden" name="action" value="clear_logs">
                        <a class="dropdown-item text-danger" href="#" id="clearLogsBtn">
                            <i class="fas fa-trash me-1"></i> {% trans "مسح السجلات" %}
                        </a>
                    </form>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="operationsTable" width="100%" cellspacing="0">
                    <thead class="thead-light">
                        <tr>
                            <th>{% trans "نوع العملية" %}</th>
                            <th>{% trans "التاريخ والوقت" %}</th>
                            <th>{% trans "المستخدم" %}</th>
                            <th>{% trans "الملف" %}</th>
                            <th>{% trans "الحالة" %}</th>
                            <th>{% trans "التفاصيل" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for log in logs %}
                        <tr>
                            <td>
                                {% if log.operation_type == 'backup' %}
                                    <span class="badge bg-primary">{% trans "نسخ احتياطي" %}</span>
                                {% elif log.operation_type == 'restore' %}
                                    <span class="badge bg-warning">{% trans "استعادة" %}</span>
                                {% elif log.operation_type == 'delete' %}
                                    <span class="badge bg-danger">{% trans "حذف" %}</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ log.operation_type }}</span>
                                {% endif %}
                            </td>
                            <td>{{ log.created_at|date:"Y-m-d H:i:s" }}</td>
                            <td>{{ log.user.username|default:"-" }}</td>
                            <td>{{ log.file_name|default:"-" }}</td>
                            <td>
                                {% if log.status == 'success' %}
                                    <span class="badge bg-success">{% trans "ناجح" %}</span>
                                {% elif log.status == 'failed' %}
                                    <span class="badge bg-danger">{% trans "فاشل" %}</span>
                                {% elif log.status == 'in_progress' %}
                                    <span class="badge bg-warning">{% trans "جاري" %}</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ log.status }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if log.details %}
                                <button class="btn btn-sm btn-info view-details" data-details="{{ log.details }}">
                                    <i class="fas fa-eye"></i> {% trans "عرض" %}
                                </button>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">{% trans "لا توجد سجلات" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if logs.has_other_pages %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if logs.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ logs.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for i in logs.paginator.page_range %}
                        {% if logs.number == i %}
                        <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                        {% elif i > logs.number|add:'-3' and i < logs.number|add:'3' %}
                        <li class="page-item"><a class="page-link" href="?page={{ i }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ i }}</a></li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if logs.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ logs.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ logs.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
    
    <!-- Modal for Log Details -->
    <div class="modal fade" id="logDetailsModal" tabindex="-1" aria-labelledby="logDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="logDetailsModalLabel">{% trans "تفاصيل السجل" %}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="logDetailsContent" class="log-details"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إغلاق" %}</button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $('#operationsTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/ar.json"
            },
            "order": [[1, "desc"]],
            "paging": false,
            "searching": false,
            "info": false
        });
        
        // View Details Button
        $('.view-details').click(function() {
            var details = $(this).data('details');
            $('#logDetailsContent').text(details);
            $('#logDetailsModal').modal('show');
        });
        
        // Clear Logs Button
        $('#clearLogsBtn').click(function(e) {
            e.preventDefault();
            if (confirm('{% trans "هل أنت متأكد من رغبتك في مسح جميع السجلات؟ هذا الإجراء لا يمكن التراجع عنه." %}')) {
                $('#clearLogsForm').submit();
            }
        });
        
        // Export Logs Button
        $('#exportLogsBtn').click(function(e) {
            e.preventDefault();
            window.location.href = '{% url "settings_app:export_logs" %}' + window.location.search;
        });
    });
</script>
{% endblock %}
