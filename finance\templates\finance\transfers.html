{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "التحويلات بين الحسابات" %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card shadow">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{% trans "التحويلات بين الحسابات" %}</h5>
            <a href="{% url 'finance:add_transfer' %}" class="btn btn-light btn-sm">
                <i class="fas fa-plus"></i> {% trans "إضافة تحويل جديد" %}
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>{% trans "التاريخ" %}</th>
                            <th>{% trans "من حساب" %}</th>
                            <th>{% trans "إلى حساب" %}</th>
                            <th>{% trans "المبلغ" %}</th>
                            <th>{% trans "المرجع" %}</th>
                            <th>{% trans "الوصف" %}</th>
                            <th>{% trans "الموظف" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for transfer in transfers %}
                        <tr>
                            <td>{{ transfer.date }}</td>
                            <td>{{ transfer.from_account.name }}</td>
                            <td>{{ transfer.to_account.name }}</td>
                            <td>{{ transfer.amount }} {% trans "د.م" %}</td>
                            <td>{{ transfer.reference|default:"-" }}</td>
                            <td>{{ transfer.description|default:"-"|truncatechars:30 }}</td>
                            <td>{{ transfer.employee.get_full_name }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center">{% trans "لا توجد تحويلات مسجلة بعد" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
