{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "حذف المصروف" %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card shadow">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0">{% trans "حذف المصروف" %}</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <h5>{% trans "هل أنت متأكد من حذف هذا المصروف؟" %}</h5>
                <p>{% trans "هذا الإجراء لا يمكن التراجع عنه. سيتم إعادة المبلغ إلى رصيد الحساب." %}</p>
            </div>
            
            <div class="card mb-4">
                <div class="card-body">
                    <h6 class="card-subtitle mb-3 text-muted">{% trans "تفاصيل المصروف" %}</h6>
                    <table class="table table-sm">
                        <tr>
                            <th>{% trans "التاريخ" %}</th>
                            <td>{{ expense.date }}</td>
                        </tr>
                        <tr>
                            <th>{% trans "الفئة" %}</th>
                            <td>{{ expense.category.name }}</td>
                        </tr>
                        <tr>
                            <th>{% trans "المبلغ" %}</th>
                            <td class="text-danger">{{ expense.amount }} {% trans "د.م" %}</td>
                        </tr>
                        <tr>
                            <th>{% trans "الحساب" %}</th>
                            <td>{{ expense.account.name }}</td>
                        </tr>
                        <tr>
                            <th>{% trans "الوصف" %}</th>
                            <td>{{ expense.description|default:"-" }}</td>
                        </tr>
                        <tr>
                            <th>{% trans "المرجع" %}</th>
                            <td>{{ expense.reference|default:"-" }}</td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <form method="post" action="{% url 'finance:delete_expense' expense.id %}">
                {% csrf_token %}
                <div class="form-group">
                    <button type="submit" class="btn btn-danger">{% trans "نعم، حذف المصروف" %}</button>
                    <a href="{% url 'finance:expenses' %}" class="btn btn-secondary">{% trans "إلغاء" %}</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
