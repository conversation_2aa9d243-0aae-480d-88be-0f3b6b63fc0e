{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "عرض المورد" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<style>
    .supplier-header {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .supplier-info {
        margin-bottom: 0;
    }

    .supplier-info dt {
        font-weight: bold;
    }

    .supplier-info dd {
        margin-bottom: 0.5rem;
    }

    .status-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }

    .payment-badge {
        font-size: 0.75rem;
    }

    .purchase-row {
        transition: all 0.2s;
    }

    .purchase-row:hover {
        background-color: #f8f9fa;
    }

    .stat-card {
        transition: all 0.3s;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .stat-icon {
        font-size: 2rem;
        opacity: 0.7;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "عرض المورد" %}</h1>
    <div>
        <a href="{% url 'purchases:suppliers' %}" class="btn btn-primary">
            <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى الموردين" %}
        </a>
    </div>
</div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<!-- Supplier Header -->
<div class="supplier-header">
    <div class="row">
        <div class="col-md-6">
            <h2>{{ supplier.name }}</h2>
            {% if supplier.category %}
            <span class="badge bg-info">{{ supplier.category.name }}</span>
            {% endif %}
            <span class="badge {% if supplier.is_active %}bg-success{% else %}bg-danger{% endif %} ms-2">
                {% if supplier.is_active %}{% trans "نشط" %}{% else %}{% trans "غير نشط" %}{% endif %}
            </span>
        </div>
        <div class="col-md-6 text-md-end">
            <div class="btn-group">
                <a href="{% url 'purchases:edit_supplier' supplier_id=supplier.id %}" class="btn btn-primary">
                    <i class="fas fa-edit me-1"></i> {% trans "تعديل" %}
                </a>
                <a href="{% url 'purchases:delete_supplier' supplier_id=supplier.id %}" class="btn btn-danger">
                    <i class="fas fa-trash me-1"></i> {% trans "حذف" %}
                </a>
                <a href="{% url 'purchases:new_purchase' %}?supplier={{ supplier.id }}" class="btn btn-success">
                    <i class="fas fa-plus me-1"></i> {% trans "طلب شراء جديد" %}
                </a>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-6">
            <h5>{% trans "معلومات الاتصال" %}</h5>
            <dl class="row supplier-info">
                {% if supplier.contact_person %}
                <dt class="col-sm-4">{% trans "الشخص المسؤول" %}</dt>
                <dd class="col-sm-8">{{ supplier.contact_person }}</dd>
                {% endif %}

                <dt class="col-sm-4">{% trans "رقم الهاتف" %}</dt>
                <dd class="col-sm-8">{{ supplier.phone }}</dd>

                {% if supplier.email %}
                <dt class="col-sm-4">{% trans "البريد الإلكتروني" %}</dt>
                <dd class="col-sm-8">{{ supplier.email }}</dd>
                {% endif %}

                {% if supplier.website %}
                <dt class="col-sm-4">{% trans "الموقع الإلكتروني" %}</dt>
                <dd class="col-sm-8">
                    <a href="{{ supplier.website }}" target="_blank">{{ supplier.website }}</a>
                </dd>
                {% endif %}
            </dl>
        </div>
        <div class="col-md-6">
            <h5>{% trans "معلومات العنوان" %}</h5>
            <dl class="row supplier-info">
                {% if supplier.address %}
                <dt class="col-sm-4">{% trans "العنوان" %}</dt>
                <dd class="col-sm-8">{{ supplier.address }}</dd>
                {% endif %}

                {% if supplier.city %}
                <dt class="col-sm-4">{% trans "المدينة" %}</dt>
                <dd class="col-sm-8">{{ supplier.city }}</dd>
                {% endif %}

                {% if supplier.country %}
                <dt class="col-sm-4">{% trans "الدولة" %}</dt>
                <dd class="col-sm-8">{{ supplier.country }}</dd>
                {% endif %}

                {% if supplier.tax_number %}
                <dt class="col-sm-4">{% trans "الرقم الضريبي" %}</dt>
                <dd class="col-sm-8">{{ supplier.tax_number }}</dd>
                {% endif %}
            </dl>
        </div>
    </div>

    {% if supplier.notes %}
    <div class="row mt-3">
        <div class="col-12">
            <h5>{% trans "ملاحظات" %}</h5>
            <div class="p-3 bg-light rounded">
                {{ supplier.notes|linebreaks }}
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2 stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {% trans "إجمالي المشتريات" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_purchases }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-primary stat-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2 stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {% trans "المشتريات المستلمة" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ received_purchases }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-success stat-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2 stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            {% trans "المشتريات المعلقة" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ pending_purchases }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-warning stat-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2 stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {% trans "إجمالي المبالغ" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_amount|floatformat:2 }} ر.س</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-money-bill-wave fa-2x text-info stat-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Purchases Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "طلبات الشراء" %}</h6>
        <a href="{% url 'purchases:new_purchase' %}?supplier={{ supplier.id }}" class="btn btn-sm btn-primary">
            <i class="fas fa-plus me-1"></i> {% trans "طلب شراء جديد" %}
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover" id="purchasesTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>{% trans "رقم المرجع" %}</th>
                        <th>{% trans "التاريخ" %}</th>
                        <th>{% trans "تاريخ التسليم المتوقع" %}</th>
                        <th>{% trans "المبلغ الإجمالي" %}</th>
                        <th>{% trans "حالة الطلب" %}</th>
                        <th>{% trans "حالة الدفع" %}</th>
                        <th>{% trans "الإجراءات" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for purchase in purchases %}
                    <tr class="purchase-row">
                        <td>
                            <a href="{% url 'purchases:view_purchase' purchase_id=purchase.id %}">
                                {{ purchase.reference_number }}
                            </a>
                        </td>
                        <td>{{ purchase.date|date:"Y-m-d" }}</td>
                        <td>
                            {% if purchase.expected_delivery_date %}
                                {{ purchase.expected_delivery_date|date:"Y-m-d" }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>{{ purchase.total_amount|floatformat:2 }} ر.س</td>
                        <td>
                            {% if purchase.status == 'pending' %}
                            <span class="badge bg-warning text-dark status-badge">{% trans "معلق" %}</span>
                            {% elif purchase.status == 'received' %}
                            <span class="badge bg-success status-badge">{% trans "تم الاستلام" %}</span>
                            {% elif purchase.status == 'cancelled' %}
                            <span class="badge bg-danger status-badge">{% trans "ملغي" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if purchase.payment_status == 'unpaid' %}
                            <span class="badge bg-danger payment-badge">{% trans "غير مدفوع" %}</span>
                            {% elif purchase.payment_status == 'partial' %}
                            <span class="badge bg-warning text-dark payment-badge">{% trans "مدفوع جزئياً" %}</span>
                            {% elif purchase.payment_status == 'paid' %}
                            <span class="badge bg-success payment-badge">{% trans "مدفوع بالكامل" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'purchases:view_purchase' purchase_id=purchase.id %}" class="btn btn-sm btn-info" title="{% trans 'عرض' %}">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if purchase.status == 'pending' %}
                                <a href="{% url 'purchases:edit_purchase' purchase_id=purchase.id %}" class="btn btn-sm btn-primary" title="{% trans 'تعديل' %}">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'purchases:receive_purchase' purchase_id=purchase.id %}" class="btn btn-sm btn-success" title="{% trans 'استلام' %}">
                                    <i class="fas fa-check"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="text-center">{% trans "لا توجد طلبات شراء لهذا المورد" %}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $('#purchasesTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "order": [[1, "desc"]],
            "pageLength": 10,
            "lengthMenu": [[5, 10, 15, 20, 25, 50, -1], [5, 10, 15, 20, 25, 50, "الكل"]],
            "dom": 'lfrtip'
        });
    });
</script>
{% endblock %}
