{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">{{ title }}</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "Edit Barcode for Product:" %} {{ product.name }}</h6>
        </div>
        <div class="card-body">
            <form method="post">
                {% csrf_token %}
                <div class="form-group">
                    <label for="barcode_type">{% trans "Barcode Type" %}</label>
                    <select name="barcode_type" id="barcode_type" class="form-control" required>
                        {% for type in barcode_types %}
                            <option value="{{ type.id }}" {% if barcode.barcode_type.id == type.id %}selected{% endif %}>{{ type.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group">
                    <label for="barcode_number">{% trans "Barcode Number" %}</label>
                    <input type="text" name="barcode_number" id="barcode_number" class="form-control" value="{{ barcode.barcode }}" required>
                </div>
                <div class="form-group form-check">
                    <input type="checkbox" name="is_primary" id="is_primary" class="form-check-input" {% if barcode.is_primary %}checked{% endif %}>
                    <label for="is_primary" class="form-check-label">{% trans "Set as Primary Barcode" %}</label>
                </div>
                <div class="form-group form-check">
                    <input type="checkbox" name="include_price" id="include_price" class="form-check-input" {% if barcode.include_price %}checked{% endif %}>
                    <label for="include_price" class="form-check-label">{% trans "Include Price in Barcode" %}</label>
                </div>
                <div class="form-group form-check">
                    <input type="checkbox" name="include_name" id="include_name" class="form-check-input" {% if barcode.include_product_name %}checked{% endif %}>
                    <label for="include_name" class="form-check-label">{% trans "Include Product Name in Barcode" %}</label>
                </div>
                <button type="submit" class="btn btn-primary">{% trans "Save Changes" %}</button>
                <a href="{% url 'inventory:product_barcodes' product.id %}" class="btn btn-secondary">{% trans "Cancel" %}</a>
            </form>
        </div>
    </div>
</div>
{% endblock %}