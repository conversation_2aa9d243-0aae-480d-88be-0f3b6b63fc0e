{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "تصدير واستيراد الباركود" %}{% endblock %}

{% block styles %}
<style>
    .import-export-container {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .upload-area {
        border: 2px dashed #ccc;
        border-radius: 5px;
        padding: 30px;
        text-align: center;
        margin-bottom: 20px;
        cursor: pointer;
        transition: all 0.3s;
    }
    .upload-area:hover {
        border-color: #007bff;
        background-color: rgba(0, 123, 255, 0.05);
    }
    .upload-icon {
        font-size: 48px;
        color: #6c757d;
        margin-bottom: 15px;
    }
    .preview-table {
        max-height: 300px;
        overflow-y: auto;
    }
    .export-options {
        border-top: 1px solid #eee;
        padding-top: 15px;
        margin-top: 15px;
    }
    .validation-error {
        color: #dc3545;
        margin-top: 5px;
        font-size: 0.9rem;
    }
    .validation-success {
        color: #28a745;
        margin-top: 5px;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-exchange-alt me-2"></i>{% trans "تصدير واستيراد الباركود" %}</h5>
                </div>
                <div class="card-body">
                    <ul class="nav nav-tabs" id="importExportTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="import-tab" data-bs-toggle="tab" data-bs-target="#import" type="button" role="tab" aria-controls="import" aria-selected="true">
                                <i class="fas fa-file-import me-1"></i> {% trans "استيراد الباركود" %}
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="export-tab" data-bs-toggle="tab" data-bs-target="#export" type="button" role="tab" aria-controls="export" aria-selected="false">
                                <i class="fas fa-file-export me-1"></i> {% trans "تصدير الباركود" %}
                            </button>
                        </li>
                    </ul>
                    
                    <div class="tab-content mt-3" id="importExportTabContent">
                        <!-- استيراد الباركود -->
                        <div class="tab-pane fade show active" id="import" role="tabpanel" aria-labelledby="import-tab">
                            <div class="import-export-container">
                                <h6 class="mb-3">{% trans "استيراد الباركود من ملف" %}</h6>
                                <p class="text-muted mb-4">{% trans "قم برفع ملف Excel أو CSV يحتوي على بيانات الباركود. يجب أن يحتوي الملف على الأعمدة التالية: رمز المنتج، نوع الباركود، رقم الباركود (اختياري)." %}</p>
                                
                                <form id="import-form" enctype="multipart/form-data">
                                    <!-- منطقة رفع الملف -->
                                    <div class="upload-area" id="upload-area">
                                        <input type="file" id="file-upload" name="file" accept=".xlsx,.xls,.csv" style="display: none;">
                                        <div class="upload-icon">
                                            <i class="fas fa-file-upload"></i>
                                        </div>
                                        <h6>{% trans "اسحب وأفلت الملف هنا أو انقر للاختيار" %}</h6>
                                        <p class="text-muted">{% trans "الصيغ المدعومة: Excel (.xlsx, .xls) أو CSV" %}</p>
                                    </div>
                                    
                                    <div id="file-info" style="display: none;" class="alert alert-info">
                                        <i class="fas fa-file-alt me-2"></i> <span id="file-name"></span>
                                        <button type="button" class="btn-close float-end" id="remove-file"></button>
                                    </div>
                                    
                                    <!-- خيارات الاستيراد -->
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="import-mode" class="form-label">{% trans "وضع الاستيراد" %}</label>
                                            <select class="form-select" id="import-mode">
                                                <option value="add">{% trans "إضافة باركود جديد فقط" %}</option>
                                                <option value="update">{% trans "تحديث الباركود الموجود فقط" %}</option>
                                                <option value="both">{% trans "إضافة وتحديث الباركود" %}</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="default-barcode-type" class="form-label">{% trans "نوع الباركود الافتراضي" %}</label>
                                            <select class="form-select" id="default-barcode-type">
                                                {% for type in barcode_types %}
                                                <option value="{{ type.id }}">{{ type.name }}</option>
                                                {% endfor %}
                                            </select>
                                            <small class="text-muted">{% trans "يستخدم إذا لم يتم تحديد نوع الباركود في الملف" %}</small>
                                        </div>
                                    </div>
                                    
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="validate-only" checked>
                                        <label class="form-check-label" for="validate-only">
                                            {% trans "التحقق من صحة البيانات قبل الاستيراد" %}
                                        </label>
                                    </div>
                                    
                                    <div class="d-flex justify-content-between">
                                        <a href="{% static 'templates/barcode_import_template.xlsx' %}" class="btn btn-outline-secondary">
                                            <i class="fas fa-download me-1"></i> {% trans "تنزيل قالب الاستيراد" %}
                                        </a>
                                        <button type="submit" class="btn btn-primary" id="import-btn" disabled>
                                            <i class="fas fa-file-import me-1"></i> {% trans "استيراد الباركود" %}
                                        </button>
                                    </div>
                                </form>
                                
                                <!-- معاينة البيانات -->
                                <div id="preview-container" style="display: none;" class="mt-4">
                                    <h6 class="mb-3">{% trans "معاينة البيانات" %}</h6>
                                    <div class="preview-table">
                                        <table class="table table-striped table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>{% trans "رمز المنتج" %}</th>
                                                    <th>{% trans "اسم المنتج" %}</th>
                                                    <th>{% trans "نوع الباركود" %}</th>
                                                    <th>{% trans "رقم الباركود" %}</th>
                                                    <th>{% trans "الحالة" %}</th>
                                                </tr>
                                            </thead>
                                            <tbody id="preview-data">
                                                <!-- سيتم إضافة البيانات هنا بواسطة JavaScript -->
                                            </tbody>
                                        </table>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <div class="alert alert-info" id="validation-summary">
                                            <i class="fas fa-info-circle me-2"></i> {% trans "جاري التحقق من البيانات..." %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- تصدير الباركود -->
                        <div class="tab-pane fade" id="export" role="tabpanel" aria-labelledby="export-tab">
                            <div class="import-export-container">
                                <h6 class="mb-3">{% trans "تصدير الباركود إلى ملف" %}</h6>
                                <p class="text-muted mb-4">{% trans "قم بتصدير بيانات الباركود إلى ملف Excel أو CSV. يمكنك تصفية البيانات حسب المنتج أو نوع الباركود." %}</p>
                                
                                <form id="export-form">
                                    <div class="row mb-3">
                                        <!-- تصفية حسب المنتج -->
                                        <div class="col-md-6">
                                            <label for="product-filter" class="form-label">{% trans "تصفية حسب المنتج" %}</label>
                                            <select class="form-select" id="product-filter">
                                                <option value="">{% trans "جميع المنتجات" %}</option>
                                                {% for product in products %}
                                                <option value="{{ product.id }}">{{ product.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        
                                        <!-- تصفية حسب نوع الباركود -->
                                        <div class="col-md-6">
                                            <label for="barcode-type-filter" class="form-label">{% trans "تصفية حسب نوع الباركود" %}</label>
                                            <select class="form-select" id="barcode-type-filter">
                                                <option value="">{% trans "جميع أنواع الباركود" %}</option>
                                                {% for type in barcode_types %}
                                                <option value="{{ type.id }}">{{ type.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <!-- تصفية حسب التاريخ -->
                                        <div class="col-md-6">
                                            <label for="date-from" class="form-label">{% trans "من تاريخ" %}</label>
                                            <input type="date" class="form-control" id="date-from">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="date-to" class="form-label">{% trans "إلى تاريخ" %}</label>
                                            <input type="date" class="form-control" id="date-to">
                                        </div>
                                    </div>
                                    
                                    <!-- خيارات التصدير -->
                                    <div class="export-options">
                                        <h6 class="mb-3">{% trans "خيارات التصدير" %}</h6>
                                        
                                        <div class="row mb-3">
                                            <!-- صيغة الملف -->
                                            <div class="col-md-6">
                                                <label for="export-format" class="form-label">{% trans "صيغة الملف" %}</label>
                                                <select class="form-select" id="export-format">
                                                    <option value="xlsx">Excel (.xlsx)</option>
                                                    <option value="csv">CSV</option>
                                                </select>
                                            </div>
                                            
                                            <!-- الأعمدة المطلوبة -->
                                            <div class="col-md-6">
                                                <label class="form-label">{% trans "الأعمدة المطلوبة" %}</label>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="include-product-name" checked>
                                                    <label class="form-check-label" for="include-product-name">
                                                        {% trans "اسم المنتج" %}
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="include-product-code" checked>
                                                    <label class="form-check-label" for="include-product-code">
                                                        {% trans "رمز المنتج" %}
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="include-barcode-type" checked>
                                                    <label class="form-check-label" for="include-barcode-type">
                                                        {% trans "نوع الباركود" %}
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="include-barcode-number" checked>
                                                    <label class="form-check-label" for="include-barcode-number">
                                                        {% trans "رقم الباركود" %}
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="include-creation-date" checked>
                                                    <label class="form-check-label" for="include-creation-date">
                                                        {% trans "تاريخ الإنشاء" %}
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="d-flex justify-content-end mt-3">
                                        <button type="submit" class="btn btn-primary" id="export-btn">
                                            <i class="fas fa-file-export me-1"></i> {% trans "تصدير الباركود" %}
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // منطقة رفع الملف
        $('#upload-area').click(function() {
            $('#file-upload').click();
        });
        
        // عند اختيار ملف
        $('#file-upload').change(function() {
            const file = this.files[0];
            if (file) {
                $('#file-name').text(file.name);
                $('#file-info').show();
                $('#import-btn').prop('disabled', false);
                
                // معاينة البيانات
                previewFile(file);
            }
        });
        
        // إزالة الملف
        $('#remove-file').click(function() {
            $('#file-upload').val('');
            $('#file-info').hide();
            $('#preview-container').hide();
            $('#import-btn').prop('disabled', true);
        });
        
        // نموذج الاستيراد
        $('#import-form').submit(function(e) {
            e.preventDefault();
            
            // إذا كان التحقق فقط مفعل، نعرض رسالة نجاح التحقق
            if ($('#validate-only').is(':checked')) {
                $('#validation-summary').removeClass('alert-info').addClass('alert-success');
                $('#validation-summary').html('<i class="fas fa-check-circle me-2"></i> {% trans "تم التحقق من البيانات بنجاح. انقر على زر الاستيراد لإكمال العملية." %}');
                $('#validate-only').prop('checked', false);
                return;
            }
            
            // محاكاة عملية الاستيراد
            $('#import-btn').html('<span class="spinner-border spinner-border-sm me-2"></span> {% trans "جاري الاستيراد..." %}');
            $('#import-btn').prop('disabled', true);
            
            setTimeout(function() {
                $('#import-btn').html('<i class="fas fa-file-import me-1"></i> {% trans "استيراد الباركود" %}');
                $('#import-btn').prop('disabled', false);
                
                // عرض رسالة نجاح
                alert('{% trans "تم استيراد البيانات بنجاح!" %}');
                
                // إعادة تعيين النموذج
                $('#file-upload').val('');
                $('#file-info').hide();
                $('#preview-container').hide();
                $('#import-btn').prop('disabled', true);
                $('#validate-only').prop('checked', true);
            }, 2000);
        });
        
        // نموذج التصدير
        $('#export-form').submit(function(e) {
            e.preventDefault();
            
            // محاكاة عملية التصدير
            $('#export-btn').html('<span class="spinner-border spinner-border-sm me-2"></span> {% trans "جاري التصدير..." %}');
            $('#export-btn').prop('disabled', true);
            
            setTimeout(function() {
                $('#export-btn').html('<i class="fas fa-file-export me-1"></i> {% trans "تصدير الباركود" %}');
                $('#export-btn').prop('disabled', false);
                
                // محاكاة تنزيل الملف
                const format = $('#export-format').val();
                const link = document.createElement('a');
                link.href = '#';
                link.download = 'barcodes_export.' + format;
                link.click();
                
                // عرض رسالة نجاح
                alert('{% trans "تم تصدير البيانات بنجاح!" %}');
            }, 2000);
        });
        
        // وظيفة معاينة الملف
        function previewFile(file) {
            // محاكاة قراءة الملف وعرض البيانات
            setTimeout(function() {
                // بيانات وهمية للمعاينة
                const previewData = [
                    { code: 'P001', name: 'زيت محرك 5W-30', type: 'EAN-13', barcode: '5901234123457', status: 'valid' },
                    { code: 'P002', name: 'فلتر زيت', type: 'EAN-13', barcode: '5901234123458', status: 'valid' },
                    { code: 'P003', name: 'فلتر هواء', type: 'EAN-13', barcode: '', status: 'missing' },
                    { code: 'P004', name: 'شمعة احتراق', type: 'EAN-13', barcode: '5901234123460', status: 'valid' },
                    { code: 'P005', name: 'زيت ناقل الحركة', type: 'EAN-13', barcode: '5901234123461', status: 'duplicate' }
                ];
                
                // إضافة البيانات إلى جدول المعاينة
                let html = '';
                let validCount = 0;
                let errorCount = 0;
                
                previewData.forEach(function(item) {
                    let statusClass = '';
                    let statusText = '';
                    
                    if (item.status === 'valid') {
                        statusClass = 'text-success';
                        statusText = '{% trans "صالح" %}';
                        validCount++;
                    } else if (item.status === 'missing') {
                        statusClass = 'text-warning';
                        statusText = '{% trans "رقم باركود مفقود" %}';
                        errorCount++;
                    } else if (item.status === 'duplicate') {
                        statusClass = 'text-danger';
                        statusText = '{% trans "رقم باركود مكرر" %}';
                        errorCount++;
                    }
                    
                    html += `
                        <tr>
                            <td>${item.code}</td>
                            <td>${item.name}</td>
                            <td>${item.type}</td>
                            <td>${item.barcode || '-'}</td>
                            <td class="${statusClass}">${statusText}</td>
                        </tr>
                    `;
                });
                
                $('#preview-data').html(html);
                $('#preview-container').show();
                
                // تحديث ملخص التحقق
                $('#validation-summary').html(`
                    <i class="fas fa-info-circle me-2"></i>
                    {% trans "تم التحقق من" %} <strong>${previewData.length}</strong> {% trans "سجل" %}:
                    <span class="text-success"><strong>${validCount}</strong> {% trans "صالح" %}</span>,
                    <span class="text-danger"><strong>${errorCount}</strong> {% trans "به أخطاء" %}</span>.
                    ${errorCount > 0 ? '{% trans "يرجى تصحيح الأخطاء قبل الاستيراد." %}' : ''}
                `);
                
                // تعطيل زر الاستيراد إذا كانت هناك أخطاء والتحقق مفعل
                if (errorCount > 0 && $('#validate-only').is(':checked')) {
                    $('#import-btn').prop('disabled', true);
                } else {
                    $('#import-btn').prop('disabled', false);
                }
            }, 1000);
        }
    });
</script>
{% endblock %}