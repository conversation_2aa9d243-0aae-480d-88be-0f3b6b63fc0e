#!/usr/bin/env python
import os
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'auto_parts_pos.settings')
django.setup()

from inventory.barcode_models import Barcode
from inventory.models import Product

def check_barcodes():
    # التحقق من الباركودات الموجودة
    barcodes = Barcode.objects.all()
    print("All barcodes in database:")
    for b in barcodes:
        print(f"Barcode: '{b.barcode_number}', Product: {b.product.name}, Active: {b.is_active}")
    
    print(f"\nTotal barcodes: {barcodes.count()}")
    
    # اختبار البحث عن الباركود المحدد
    test_barcode = "1282235856942741"
    print(f"\nSearching for barcode: '{test_barcode}'")
    result = Barcode.objects.filter(barcode_number=test_barcode).first()
    if result:
        print(f"Found barcode: {result.barcode_number} for product: {result.product.name}")
    else:
        print(f"Barcode {test_barcode} not found")
        
        # إنشاء باركود جديد للاختبار
        products = Product.objects.all()
        if products.exists():
            from inventory.barcode_models import BarcodeType
            barcode_types = BarcodeType.objects.all()
            if barcode_types.exists():
                product = products.first()
                barcode_type = barcode_types.first()
                
                new_barcode = Barcode.objects.create(
                    product=product,
                    barcode_type=barcode_type,
                    barcode_number=test_barcode,
                    is_primary=True,
                    is_active=True
                )
                print(f"Created new barcode: {new_barcode.barcode_number} for product: {product.name}")

if __name__ == '__main__':
    check_barcodes()
