from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.db.models import Sum, Q
from django.utils import timezone
from django.core.paginator import Paginator

from .models import Account, ExpenseCategory, IncomeCategory, Expense, Income, AccountTransfer, Transaction, Payment

@login_required
def index(request):
    accounts = Account.objects.filter(is_active=True).order_by('name')
    expenses = Expense.objects.all().order_by('-date')[:10]
    incomes = Income.objects.all().order_by('-date')[:10]

    context = {
        'accounts': accounts,
        'expenses': expenses,
        'incomes': incomes,
    }
    return render(request, 'finance/index.html', context)

@login_required
def expenses(request):
    expenses_list = Expense.objects.all().order_by('-date')
    expense_categories = ExpenseCategory.objects.all()
    accounts = Account.objects.filter(is_active=True)

    context = {
        'expenses': expenses_list,
        'expense_categories': expense_categories,
        'accounts': accounts,
    }
    return render(request, 'finance/expenses.html', context)

@login_required
def add_expense(request):
    expense_categories = ExpenseCategory.objects.all()
    accounts = Account.objects.filter(is_active=True)

    if request.method == 'POST':
        # Process form data
        try:
            date = request.POST.get('date')
            amount = float(request.POST.get('amount'))
            category_id = request.POST.get('category')
            account_id = request.POST.get('account')
            description = request.POST.get('description')
            reference = request.POST.get('reference')

            # Validate required fields
            if not all([date, amount, category_id, account_id]):
                messages.error(request, _('جميع الحقول المطلوبة يجب ملؤها'))
                return redirect('finance:add_expense')

            # Get category and account objects
            category = ExpenseCategory.objects.get(id=category_id)
            account = Account.objects.get(id=account_id)

            # Check if account has enough balance
            if account.current_balance < amount:
                messages.error(request, _('رصيد الحساب غير كافٍ لهذا المصروف'))
                return redirect('finance:add_expense')

            # تحويل المبلغ إلى Decimal لتجنب مشاكل التوافق
            from decimal import Decimal
            decimal_amount = Decimal(str(amount))

            # Create expense record
            expense = Expense.objects.create(
                date=date,
                amount=decimal_amount,
                category=category,
                account=account,
                description=description,
                reference=reference,
                employee=request.user,
                payment_method='cash'  # افتراضي
            )

            # Update account balance
            account.current_balance -= decimal_amount
            account.save(update_fields=['current_balance'])

            messages.success(request, _('تم إضافة المصروف بنجاح'))
            return redirect('finance:expenses')

        except Exception as e:
            messages.error(request, _('حدث خطأ أثناء إضافة المصروف: ') + str(e))

    from django.utils import timezone
    context = {
        'expense_categories': expense_categories,
        'accounts': accounts,
        'today': timezone.now()
    }
    return render(request, 'finance/add_expense.html', context)

@login_required
def edit_expense(request, expense_id):
    expense = get_object_or_404(Expense, id=expense_id)
    expense_categories = ExpenseCategory.objects.all()
    accounts = Account.objects.filter(is_active=True)

    if request.method == 'POST':
        try:
            date = request.POST.get('date')
            new_amount = float(request.POST.get('amount'))
            category_id = request.POST.get('category')
            account_id = request.POST.get('account')
            description = request.POST.get('description')
            reference = request.POST.get('reference')

            # Validate required fields
            if not all([date, new_amount, category_id, account_id]):
                messages.error(request, _('جميع الحقول المطلوبة يجب ملؤها'))
                return redirect('finance:edit_expense', expense_id=expense_id)

            # Get category and account objects
            category = ExpenseCategory.objects.get(id=category_id)
            account = Account.objects.get(id=account_id)

            # تحويل المبلغ إلى Decimal لتجنب مشاكل التوافق
            from decimal import Decimal
            decimal_amount = Decimal(str(new_amount))

            # Calculate the difference in amount
            amount_diff = decimal_amount - expense.amount

            # If account is changed or amount is increased, check if new account has enough balance
            if (account.id != expense.account.id or amount_diff > 0) and account.current_balance < amount_diff:
                messages.error(request, _('رصيد الحساب غير كافٍ للمبلغ الجديد'))
                return redirect('finance:edit_expense', expense_id=expense_id)

            # Update account balances
            if account.id == expense.account.id:
                # Same account, just update the difference
                account.current_balance -= amount_diff
                account.save(update_fields=['current_balance'])
            else:
                # Different account, restore old account balance and update new account
                expense.account.current_balance += expense.amount
                expense.account.save(update_fields=['current_balance'])

                account.current_balance -= decimal_amount
                account.save(update_fields=['current_balance'])

            # Update expense record
            expense.date = date
            expense.amount = new_amount
            expense.category = category
            expense.account = account
            expense.description = description
            expense.reference = reference
            # التأكد من وجود طريقة الدفع
            if not expense.payment_method:
                expense.payment_method = 'cash'
            expense.save()

            messages.success(request, _('تم تحديث المصروف بنجاح'))
            return redirect('finance:expenses')

        except Exception as e:
            messages.error(request, _('حدث خطأ أثناء تحديث المصروف: ') + str(e))

    context = {
        'expense': expense,
        'expense_categories': expense_categories,
        'accounts': accounts,
    }
    return render(request, 'finance/edit_expense.html', context)

@login_required
def delete_expense(request, expense_id):
    expense = get_object_or_404(Expense, id=expense_id)

    if request.method == 'POST':
        # Restore account balance
        expense.account.current_balance += expense.amount
        expense.account.save(update_fields=['current_balance'])

        expense.delete()
        messages.success(request, _('تم حذف المصروف بنجاح'))
        return redirect('finance:expenses')

    context = {
        'expense': expense,
    }
    return render(request, 'finance/delete_expense.html', context)

@login_required
def income(request):
    income_list = Income.objects.all().order_by('-date')
    income_categories = IncomeCategory.objects.all()
    accounts = Account.objects.filter(is_active=True)

    context = {
        'incomes': income_list,
        'income_categories': income_categories,
        'accounts': accounts,
    }
    return render(request, 'finance/income.html', context)

@login_required
def add_income(request):
    income_categories = IncomeCategory.objects.all()
    accounts = Account.objects.filter(is_active=True)

    if request.method == 'POST':
        try:
            date = request.POST.get('date')
            amount = float(request.POST.get('amount'))
            category_id = request.POST.get('category')
            account_id = request.POST.get('account')
            description = request.POST.get('description')
            reference = request.POST.get('reference')

            # التحقق من الحقول المطلوبة
            if not all([date, amount, category_id, account_id]):
                messages.error(request, _('جميع الحقول المطلوبة يجب ملؤها'))
                return redirect('finance:add_income')

            # التحقق من المبلغ
            if amount <= 0:
                messages.error(request, _('يجب أن يكون المبلغ أكبر من صفر'))
                return redirect('finance:add_income')

            # الحصول على الفئة والحساب
            category = IncomeCategory.objects.get(id=category_id)
            account = Account.objects.get(id=account_id)

            # تحويل المبلغ إلى Decimal لتجنب مشاكل التوافق
            from decimal import Decimal
            decimal_amount = Decimal(str(amount))

            # إنشاء الإيراد
            income = Income.objects.create(
                date=date,
                amount=decimal_amount,
                category=category,
                description=description,
                payment_method='cash',  # افتراضي
                account=account,
                reference=reference,
                employee=request.user
            )

            # تحديث رصيد الحساب
            account.current_balance += decimal_amount
            account.save(update_fields=['current_balance'])

            messages.success(request, _('تم إضافة الإيراد بنجاح'))
            return redirect('finance:income')

        except Exception as e:
            messages.error(request, _('حدث خطأ أثناء إضافة الإيراد: ') + str(e))

    from django.utils import timezone
    context = {
        'income_categories': income_categories,
        'accounts': accounts,
        'today': timezone.now().date()
    }
    return render(request, 'finance/add_income.html', context)

@login_required
def edit_income(request, income_id):
    income = get_object_or_404(Income, id=income_id)
    income_categories = IncomeCategory.objects.all()
    accounts = Account.objects.filter(is_active=True)

    if request.method == 'POST':
        try:
            date = request.POST.get('date')
            new_amount = float(request.POST.get('amount'))
            category_id = request.POST.get('category')
            account_id = request.POST.get('account')
            description = request.POST.get('description')
            reference = request.POST.get('reference')

            # التحقق من الحقول المطلوبة
            if not all([date, new_amount, category_id, account_id]):
                messages.error(request, _('جميع الحقول المطلوبة يجب ملؤها'))
                return redirect('finance:edit_income', income_id=income_id)

            # الحصول على الفئة والحساب
            category = IncomeCategory.objects.get(id=category_id)
            account = Account.objects.get(id=account_id)

            # تحويل المبلغ إلى Decimal لتجنب مشاكل التوافق
            from decimal import Decimal
            decimal_amount = Decimal(str(new_amount))

            # حساب الفرق في المبلغ
            amount_diff = decimal_amount - income.amount

            # تحديث أرصدة الحسابات
            if account.id == income.account.id:
                # نفس الحساب، تحديث الفرق فقط
                account.current_balance += amount_diff
                account.save(update_fields=['current_balance'])
            else:
                # حساب مختلف، استعادة رصيد الحساب القديم وتحديث الحساب الجديد
                income.account.current_balance -= income.amount
                income.account.save(update_fields=['current_balance'])

                account.current_balance += decimal_amount
                account.save(update_fields=['current_balance'])

            # تحديث سجل الإيراد
            income.date = date
            income.amount = new_amount
            income.category = category
            income.account = account
            income.description = description
            income.reference = reference
            income.save()

            messages.success(request, _('تم تحديث الإيراد بنجاح'))
            return redirect('finance:income')

        except Exception as e:
            messages.error(request, _('حدث خطأ أثناء تحديث الإيراد: ') + str(e))
            return redirect('finance:edit_income', income_id=income_id)

    context = {
        'income': income,
        'income_categories': income_categories,
        'accounts': accounts,
    }
    return render(request, 'finance/edit_income.html', context)

@login_required
def delete_income(request, income_id):
    income = get_object_or_404(Income, id=income_id)

    if request.method == 'POST':
        # Restore account balance
        income.account.current_balance -= income.amount
        income.account.save(update_fields=['current_balance'])

        income.delete()
        messages.success(request, _('تم حذف الإيراد بنجاح'))
        return redirect('finance:income')

    context = {
        'income': income,
    }
    return render(request, 'finance/delete_income.html', context)

@login_required
def accounts(request):
    accounts_list = Account.objects.all().order_by('name')

    context = {
        'accounts': accounts_list,
    }
    return render(request, 'finance/accounts.html', context)

@login_required
def add_account(request):
    if request.method == 'POST':
        try:
            name = request.POST.get('name')
            account_type = request.POST.get('account_type')
            initial_balance = float(request.POST.get('initial_balance') or 0)
            description = request.POST.get('description')

            # التحقق من الحقول المطلوبة
            if not all([name, account_type]):
                messages.error(request, _('يجب تعبئة جميع الحقول المطلوبة'))
                return redirect('finance:accounts')

            # تحويل المبلغ إلى Decimal لتجنب مشاكل التوافق
            from decimal import Decimal
            decimal_balance = Decimal(str(initial_balance))

            # إنشاء حساب جديد
            Account.objects.create(
                name=name,
                account_type=account_type,
                initial_balance=decimal_balance,
                current_balance=decimal_balance,
                notes=description
            )

            messages.success(request, _('تم إنشاء الحساب بنجاح'))
            return redirect('finance:accounts')

        except Exception as e:
            messages.error(request, _('حدث خطأ أثناء إنشاء الحساب: ') + str(e))
            return redirect('finance:accounts')

    return redirect('finance:accounts')

@login_required
def edit_account(request, account_id):
    account = get_object_or_404(Account, id=account_id)

    if request.method == 'POST':
        try:
            name = request.POST.get('name')
            account_type = request.POST.get('account_type')
            description = request.POST.get('description')

            # التحقق من الحقول المطلوبة
            if not all([name, account_type]):
                messages.error(request, _('يجب تعبئة جميع الحقول المطلوبة'))
                return redirect('finance:accounts')

            # تحديث الحساب
            account.name = name
            account.account_type = account_type
            account.notes = description
            account.save()

            messages.success(request, _('تم تحديث الحساب بنجاح'))
            return redirect('finance:accounts')

        except Exception as e:
            messages.error(request, _('حدث خطأ أثناء تحديث الحساب: ') + str(e))
            return redirect('finance:accounts')

    return redirect('finance:accounts')

@login_required
def activate_account(request, account_id):
    account = get_object_or_404(Account, id=account_id)

    if request.method == 'POST':
        try:
            account.is_active = True
            account.save(update_fields=['is_active'])

            messages.success(request, _('تم تفعيل الحساب بنجاح'))
        except Exception as e:
            messages.error(request, _('حدث خطأ أثناء تفعيل الحساب: ') + str(e))

    return redirect('finance:accounts')

@login_required
def deactivate_account(request, account_id):
    account = get_object_or_404(Account, id=account_id)

    if request.method == 'POST':
        try:
            account.is_active = False
            account.save(update_fields=['is_active'])

            messages.success(request, _('تم تعطيل الحساب بنجاح'))
        except Exception as e:
            messages.error(request, _('حدث خطأ أثناء تعطيل الحساب: ') + str(e))

    return redirect('finance:accounts')

@login_required
def transfers(request):
    transfers_list = AccountTransfer.objects.all().order_by('-date')

    context = {
        'transfers': transfers_list,
    }
    return render(request, 'finance/transfers.html', context)

@login_required
def add_transfer(request):
    accounts = Account.objects.filter(is_active=True)

    if request.method == 'POST':
        try:
            date = request.POST.get('date')
            from_account_id = request.POST.get('from_account')
            to_account_id = request.POST.get('to_account')
            amount = float(request.POST.get('amount'))
            description = request.POST.get('description')
            reference = request.POST.get('reference')

            # التحقق من الحقول المطلوبة
            if not all([date, from_account_id, to_account_id, amount]):
                messages.error(request, _('يجب تعبئة جميع الحقول المطلوبة'))
                return redirect('finance:add_transfer')

            # التحقق من المبلغ
            if amount <= 0:
                messages.error(request, _('يجب أن يكون المبلغ أكبر من صفر'))
                return redirect('finance:add_transfer')

            # التحقق من أن الحسابين مختلفين
            if from_account_id == to_account_id:
                messages.error(request, _('لا يمكن التحويل إلى نفس الحساب'))
                return redirect('finance:add_transfer')

            # الحصول على الحسابات
            from_account = Account.objects.get(id=from_account_id)
            to_account = Account.objects.get(id=to_account_id)

            # التحقق من رصيد الحساب المصدر
            if from_account.current_balance < amount:
                messages.error(request, _('لا يوجد رصيد كافٍ في الحساب المصدر'))
                return redirect('finance:add_transfer')

            # تحويل المبلغ إلى Decimal لتجنب مشاكل التوافق
            from decimal import Decimal
            decimal_amount = Decimal(str(amount))

            # إنشاء التحويل
            AccountTransfer.objects.create(
                date=date,
                from_account=from_account,
                to_account=to_account,
                amount=decimal_amount,
                description=description,
                reference=reference,
                employee=request.user
            )

            messages.success(request, _('تم إنشاء التحويل بنجاح'))
            return redirect('finance:transfers')

        except Exception as e:
            messages.error(request, _('حدث خطأ أثناء إنشاء التحويل: ') + str(e))
            return redirect('finance:add_transfer')

    context = {
        'accounts': accounts,
        'today': timezone.now().date(),
    }
    return render(request, 'finance/add_transfer.html', context)

@login_required
def transactions(request):
    # الحصول على معايير التصفية
    transaction_type = request.GET.get('type', '')
    payment_status = request.GET.get('status', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    search_query = request.GET.get('q', '')

    # الحصول على جميع المعاملات
    transactions_list = Transaction.objects.all()

    # تطبيق معايير التصفية
    if transaction_type:
        transactions_list = transactions_list.filter(transaction_type=transaction_type)

    if payment_status:
        transactions_list = transactions_list.filter(payment_status=payment_status)

    if date_from:
        transactions_list = transactions_list.filter(date__gte=date_from)

    if date_to:
        transactions_list = transactions_list.filter(date__lte=date_to)

    if search_query:
        transactions_list = transactions_list.filter(
            Q(transaction_number__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # ترتيب المعاملات حسب التاريخ والوقت
    transactions_list = transactions_list.order_by('-date', '-created_at')

    # تقسيم النتائج إلى صفحات
    paginator = Paginator(transactions_list, 20)  # 20 معاملة في كل صفحة
    page_number = request.GET.get('page')
    transactions_page = paginator.get_page(page_number)

    # الحصول على إحصائيات المعاملات
    total_amount = transactions_list.aggregate(Sum('amount'))['amount__sum'] or 0
    paid_amount = transactions_list.aggregate(Sum('paid_amount'))['paid_amount__sum'] or 0
    remaining_amount = total_amount - paid_amount

    # عدد المعاملات حسب الحالة
    paid_count = transactions_list.filter(payment_status='paid').count()
    unpaid_count = transactions_list.filter(payment_status='unpaid').count()
    partial_count = transactions_list.filter(payment_status='partial').count()

    context = {
        'transactions': transactions_page,
        'transaction_type': transaction_type,
        'payment_status': payment_status,
        'date_from': date_from,
        'date_to': date_to,
        'search_query': search_query,
        'total_amount': total_amount,
        'paid_amount': paid_amount,
        'remaining_amount': remaining_amount,
        'paid_count': paid_count,
        'unpaid_count': unpaid_count,
        'partial_count': partial_count,
    }
    return render(request, 'finance/transactions.html', context)

@login_required
def transaction_detail(request, transaction_id):
    transaction = get_object_or_404(Transaction, id=transaction_id)
    payments = transaction.payments.all().order_by('-date')

    context = {
        'transaction': transaction,
        'payments': payments,
    }
    return render(request, 'finance/transaction_detail.html', context)

@login_required
def add_payment_to_transaction(request, transaction_id):
    transaction = get_object_or_404(Transaction, id=transaction_id)
    accounts = Account.objects.filter(is_active=True)

    if request.method == 'POST':
        try:
            date = request.POST.get('date')
            amount = float(request.POST.get('amount'))
            payment_method = request.POST.get('payment_method')
            account_id = request.POST.get('account')
            reference = request.POST.get('reference')
            notes = request.POST.get('notes')

            # التحقق من الحقول المطلوبة
            if not all([date, amount, payment_method, account_id]):
                messages.error(request, _('جميع الحقول المطلوبة يجب ملؤها'))
                return redirect('finance:add_payment_to_transaction', transaction_id=transaction_id)

            # التحقق من المبلغ
            if amount <= 0:
                messages.error(request, _('يجب أن يكون المبلغ أكبر من صفر'))
                return redirect('finance:add_payment_to_transaction', transaction_id=transaction_id)

            # التحقق من أن المبلغ لا يتجاوز المبلغ المتبقي
            if amount > transaction.remaining_amount:
                messages.error(request, _('المبلغ المدخل يتجاوز المبلغ المتبقي للمعاملة'))
                return redirect('finance:add_payment_to_transaction', transaction_id=transaction_id)

            # الحصول على الحساب
            account = Account.objects.get(id=account_id)

            # تحويل المبلغ إلى Decimal لتجنب مشاكل التوافق
            from decimal import Decimal
            decimal_amount = Decimal(str(amount))

            # إنشاء الدفعة
            Payment.objects.create(
                transaction=transaction,
                date=date,
                amount=decimal_amount,
                payment_method=payment_method,
                account=account,
                reference=reference,
                notes=notes,
                created_by=request.user
            )

            messages.success(request, _('تمت إضافة الدفعة بنجاح'))
            return redirect('finance:transaction_detail', transaction_id=transaction_id)

        except Exception as e:
            messages.error(request, _('حدث خطأ أثناء إضافة الدفعة: ') + str(e))
            return redirect('finance:add_payment_to_transaction', transaction_id=transaction_id)

    context = {
        'transaction': transaction,
        'accounts': accounts,
        'today': timezone.now().date(),
    }
    return render(request, 'finance/add_payment_to_transaction.html', context)

@login_required
def income_categories(request):
    categories = IncomeCategory.objects.all().order_by('name')

    context = {
        'categories': categories,
    }
    return render(request, 'finance/income_categories.html', context)

@login_required
def add_income_category(request):
    if request.method == 'POST':
        try:
            name = request.POST.get('name')
            description = request.POST.get('description')

            # التحقق من الحقول المطلوبة
            if not name:
                messages.error(request, _('يجب تعبئة اسم الفئة'))
                return redirect('finance:income_categories')

            # التحقق من عدم وجود فئة بنفس الاسم
            if IncomeCategory.objects.filter(name=name).exists():
                messages.error(request, _('توجد فئة بهذا الاسم بالفعل'))
                return redirect('finance:income_categories')

            # إنشاء فئة جديدة
            IncomeCategory.objects.create(
                name=name,
                description=description
            )

            messages.success(request, _('تم إنشاء فئة الإيرادات بنجاح'))
            return redirect('finance:income_categories')

        except Exception as e:
            messages.error(request, _('حدث خطأ أثناء إنشاء فئة الإيرادات: ') + str(e))
            return redirect('finance:income_categories')

    return redirect('finance:income_categories')

@login_required
def edit_income_category(request, category_id):
    category = get_object_or_404(IncomeCategory, id=category_id)

    if request.method == 'POST':
        try:
            name = request.POST.get('name')
            description = request.POST.get('description')

            # التحقق من الحقول المطلوبة
            if not name:
                messages.error(request, _('يجب تعبئة اسم الفئة'))
                return redirect('finance:income_categories')

            # التحقق من عدم وجود فئة أخرى بنفس الاسم
            if IncomeCategory.objects.filter(name=name).exclude(id=category_id).exists():
                messages.error(request, _('توجد فئة أخرى بهذا الاسم'))
                return redirect('finance:income_categories')

            # تحديث الفئة
            category.name = name
            category.description = description
            category.save()

            messages.success(request, _('تم تحديث فئة الإيرادات بنجاح'))
            return redirect('finance:income_categories')

        except Exception as e:
            messages.error(request, _('حدث خطأ أثناء تحديث فئة الإيرادات: ') + str(e))
            return redirect('finance:income_categories')

    context = {
        'category': category,
    }
    return render(request, 'finance/edit_income_category.html', context)

@login_required
def delete_income_category(request, category_id):
    category = get_object_or_404(IncomeCategory, id=category_id)

    if request.method == 'POST':
        try:
            # التحقق من عدم وجود إيرادات مرتبطة بهذه الفئة
            if Income.objects.filter(category=category).exists():
                messages.error(request, _('لا يمكن حذف هذه الفئة لأنها مرتبطة بإيرادات'))
                return redirect('finance:income_categories')

            category.delete()
            messages.success(request, _('تم حذف فئة الإيرادات بنجاح'))
            return redirect('finance:income_categories')

        except Exception as e:
            messages.error(request, _('حدث خطأ أثناء حذف فئة الإيرادات: ') + str(e))
            return redirect('finance:income_categories')

    context = {
        'category': category,
    }
    return render(request, 'finance/delete_income_category.html', context)

@login_required
def dashboard(request):
    # الحصول على التاريخ الحالي
    today = timezone.now().date()
    start_of_month = today.replace(day=1)

    # إحصائيات الحسابات
    accounts = Account.objects.filter(is_active=True)
    total_balance = accounts.aggregate(Sum('current_balance'))['current_balance__sum'] or 0

    # إحصائيات المصروفات
    expenses_this_month = Expense.objects.filter(date__gte=start_of_month)
    total_expenses = expenses_this_month.aggregate(Sum('amount'))['amount__sum'] or 0

    # إحصائيات الإيرادات
    incomes_this_month = Income.objects.filter(date__gte=start_of_month)
    total_income = incomes_this_month.aggregate(Sum('amount'))['amount__sum'] or 0

    # صافي الربح
    net_profit = total_income - total_expenses

    # المعاملات غير المدفوعة
    unpaid_transactions = Transaction.objects.filter(payment_status__in=['unpaid', 'partial'])
    total_unpaid = unpaid_transactions.aggregate(Sum('amount') - Sum('paid_amount'))['amount__sum'] or 0

    # المعاملات الأخيرة
    recent_transactions = Transaction.objects.all().order_by('-date')[:10]

    context = {
        'accounts': accounts,
        'total_balance': total_balance,
        'total_expenses': total_expenses,
        'total_income': total_income,
        'net_profit': net_profit,
        'total_unpaid': total_unpaid,
        'recent_transactions': recent_transactions,
        'today': today,
    }
    return render(request, 'finance/dashboard.html', context)
