# Generated by Django 5.2 on 2025-04-21 16:17

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('settings_app', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='BackupSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_auto_backup', models.BooleanField(default=True, verbose_name='نسخ احتياطي تلقائي')),
                ('frequency', models.CharField(choices=[('daily', 'يومي'), ('weekly', 'أسبوعي'), ('monthly', 'شهري')], default='daily', max_length=10, verbose_name='التكرار')),
                ('backup_time', models.TimeField(default='00:00', verbose_name='وقت النسخ الاحتياطي')),
                ('storage_type', models.CharField(choices=[('local', 'محلي'), ('google_drive', 'Google Drive'), ('dropbox', 'Dropbox')], default='local', max_length=20, verbose_name='نوع التخزين')),
                ('storage_path', models.CharField(blank=True, max_length=255, null=True, verbose_name='مسار التخزين')),
                ('retention_days', models.PositiveIntegerField(default=30, verbose_name='أيام الاحتفاظ')),
                ('include_media', models.BooleanField(default=True, verbose_name='تضمين الوسائط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إعداد النسخ الاحتياطي',
                'verbose_name_plural': 'إعدادات النسخ الاحتياطي',
            },
        ),
        migrations.CreateModel(
            name='CurrencySetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('currency', models.CharField(choices=[('MAD', 'درهم مغربي'), ('USD', 'دولار أمريكي'), ('EUR', 'يورو'), ('SAR', 'ريال سعودي'), ('AED', 'درهم إماراتي'), ('EGP', 'جنيه مصري')], default='MAD', max_length=3, verbose_name='العملة')),
                ('symbol', models.CharField(default='د.م.', max_length=5, verbose_name='الرمز')),
                ('position', models.CharField(choices=[('before', 'قبل المبلغ'), ('after', 'بعد المبلغ')], default='after', max_length=10, verbose_name='موضع الرمز')),
                ('decimal_places', models.PositiveSmallIntegerField(default=2, verbose_name='منازل عشرية')),
                ('thousands_separator', models.CharField(default=',', max_length=1, verbose_name='فاصل الآلاف')),
                ('decimal_separator', models.CharField(default='.', max_length=1, verbose_name='فاصل العشري')),
                ('is_default', models.BooleanField(default=True, verbose_name='افتراضي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إعداد العملة',
                'verbose_name_plural': 'إعدادات العملة',
            },
        ),
        migrations.CreateModel(
            name='EmailSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('smtp_host', models.CharField(max_length=100, verbose_name='مضيف SMTP')),
                ('smtp_port', models.PositiveIntegerField(default=587, verbose_name='منفذ SMTP')),
                ('smtp_username', models.CharField(max_length=100, verbose_name='اسم مستخدم SMTP')),
                ('smtp_password', models.CharField(max_length=100, verbose_name='كلمة مرور SMTP')),
                ('smtp_use_tls', models.BooleanField(default=True, verbose_name='استخدام TLS')),
                ('from_email', models.EmailField(max_length=254, verbose_name='البريد الإلكتروني المرسل')),
                ('from_name', models.CharField(max_length=100, verbose_name='اسم المرسل')),
                ('is_enabled', models.BooleanField(default=True, verbose_name='مفعل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إعداد البريد الإلكتروني',
                'verbose_name_plural': 'إعدادات البريد الإلكتروني',
            },
        ),
        migrations.CreateModel(
            name='InvoiceSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('prefix', models.CharField(default='INV-', max_length=10, verbose_name='بادئة الفاتورة')),
                ('next_number', models.PositiveIntegerField(default=1001, verbose_name='الرقم التالي')),
                ('footer_text', models.TextField(blank=True, null=True, verbose_name='نص التذييل')),
                ('terms_and_conditions', models.TextField(blank=True, null=True, verbose_name='الشروط والأحكام')),
                ('show_logo', models.BooleanField(default=True, verbose_name='إظهار الشعار')),
                ('show_tax', models.BooleanField(default=True, verbose_name='إظهار الضريبة')),
                ('show_customer_info', models.BooleanField(default=True, verbose_name='إظهار معلومات العميل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إعداد الفاتورة',
                'verbose_name_plural': 'إعدادات الفاتورة',
            },
        ),
        migrations.CreateModel(
            name='LanguageSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('language', models.CharField(choices=[('ar', 'العربية'), ('en', 'الإنجليزية'), ('fr', 'الفرنسية')], default='ar', max_length=2, verbose_name='اللغة')),
                ('direction', models.CharField(choices=[('rtl', 'من اليمين إلى اليسار'), ('ltr', 'من اليسار إلى اليمين')], default='rtl', max_length=3, verbose_name='اتجاه النص')),
                ('is_default', models.BooleanField(default=True, verbose_name='افتراضي')),
                ('is_enabled', models.BooleanField(default=True, verbose_name='مفعل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إعداد اللغة',
                'verbose_name_plural': 'إعدادات اللغة',
            },
        ),
        migrations.CreateModel(
            name='TaxSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='ضريبة القيمة المضافة', max_length=100, verbose_name='اسم الضريبة')),
                ('rate', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='نسبة الضريبة')),
                ('tax_type', models.CharField(choices=[('inclusive', 'شامل'), ('exclusive', 'إضافي')], default='exclusive', max_length=10, verbose_name='نوع الضريبة')),
                ('is_enabled', models.BooleanField(default=True, verbose_name='مفعل')),
                ('tax_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='الرقم الضريبي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إعداد الضريبة',
                'verbose_name_plural': 'إعدادات الضريبة',
            },
        ),
    ]
