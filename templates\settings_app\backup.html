{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/settings.css' %}">
{% endblock %}

{% block title %}{% trans "النسخ الاحتياطي" %} | {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "النسخ الاحتياطي" %}</h1>
        <div>
            <a href="{% url 'settings_app:backup_logs' %}" class="btn btn-info me-2">
                <i class="fas fa-history me-1"></i> {% trans "سجل العمليات" %}
            </a>
            <a href="{% url 'settings_app:restore' %}" class="btn btn-warning me-2">
                <i class="fas fa-upload me-1"></i> {% trans "استعادة النسخة الاحتياطية" %}
            </a>
            <button type="button" class="btn btn-primary" id="createBackupBtn">
                <i class="fas fa-download me-1"></i> {% trans "إنشاء نسخة احتياطية" %}
            </button>
        </div>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- لوحة الإحصائيات السريعة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">{% trans "عدد النسخ الاحتياطية" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ backup_logs|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-database fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">{% trans "آخر نسخة احتياطية" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% if backup_logs.0 %}
                                    {{ backup_logs.0.created_at|date:"Y-m-d H:i" }}
                                {% else %}
                                    -
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">{% trans "النسخ المشفرة" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% with encrypted_count=0 %}
                                    {% for log in backup_logs %}
                                        {% if log.is_encrypted %}
                                            {% with encrypted_count=encrypted_count|add:1 %}
                                            {% endwith %}
                                        {% endif %}
                                    {% endfor %}
                                    {{ encrypted_count }}
                                {% endwith %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-lock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">{% trans "الجدولة" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% if backup_setting.is_auto_backup %}
                                    <span class="badge bg-success">{% trans "مفعلة" %}</span>
                                {% else %}
                                    <span class="badge bg-secondary">{% trans "غير مفعلة" %}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "سجلات النسخ الاحتياطي" %}</h6>
            <div class="dropdown no-arrow">
                <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                    <div class="dropdown-header">{% trans "خيارات" %}:</div>
                    <a class="dropdown-item" href="#" id="configureBackupBtn">{% trans "إعدادات النسخ الاحتياطي" %}</a>
                    <div class="dropdown-divider"></div>
                    <form method="post" action="{% url 'settings_app:backup' %}" id="deleteAllBackupsForm">
                        {% csrf_token %}
                        <input type="hidden" name="action" value="delete_all">
                        <a class="dropdown-item text-danger" href="#" id="deleteAllBackupsBtn">{% trans "حذف جميع النسخ الاحتياطية" %}</a>
                    </form>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="backupsTable" width="100%" cellspacing="0">
                    <thead class="thead-light">
                        <tr>
                            <th>{% trans "اسم الملف" %}</th>
                            <th>{% trans "التاريخ" %}</th>
                            <th>{% trans "الحجم" %}</th>
                            <th>{% trans "النوع" %}</th>
                            <th>{% trans "التخزين" %}</th>
                            <th>{% trans "التشفير" %}</th>
                            <th>{% trans "الحالة" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for log in backup_logs %}
                        <tr>
                            <td>{{ log.file_name }}</td>
                            <td>{{ log.created_at|date:"Y-m-d H:i" }}</td>
                            <td>
                                {% if log.file_size %}
                                    {{ log.get_size_display }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>
                                {% if log.backup_type == 'auto' %}
                                    <span class="badge bg-info">{% trans "تلقائي" %}</span>
                                {% elif log.backup_type == 'scheduled' %}
                                    <span class="badge bg-warning">{% trans "مجدول" %}</span>
                                {% else %}
                                    <span class="badge bg-primary">{% trans "يدوي" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if log.cloud_storage_type == 'local' %}
                                    <span class="badge bg-secondary">{% trans "محلي" %}</span>
                                {% elif log.cloud_storage_type == 'google_drive' %}
                                    <span class="badge bg-primary">Google Drive</span>
                                {% elif log.cloud_storage_type == 'dropbox' %}
                                    <span class="badge bg-info">Dropbox</span>
                                {% else %}
                                    <span class="badge bg-secondary">{% trans "محلي" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if log.is_encrypted %}
                                    <span class="badge bg-success"><i class="fas fa-lock me-1"></i> {% trans "مشفر" %}</span>
                                {% else %}
                                    <span class="badge bg-secondary"><i class="fas fa-unlock me-1"></i> {% trans "غير مشفر" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if log.status == 'success' %}
                                    <span class="badge bg-success">{% trans "ناجح" %}</span>
                                {% elif log.status == 'in_progress' %}
                                    <span class="badge bg-warning">{% trans "جاري" %}</span>
                                {% else %}
                                    <span class="badge bg-danger">{% trans "فاشل" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    {% if log.status == 'success' %}
                                        {% if log.cloud_storage_link %}
                                            <a href="{{ log.cloud_storage_link }}" target="_blank" class="btn btn-sm btn-info" title="{% trans "عرض في السحابة" %}">
                                                <i class="fas fa-cloud"></i>
                                            </a>
                                        {% endif %}
                                        <a href="{% url 'settings_app:download_backup' log.id %}" class="btn btn-sm btn-primary download-backup" title="{% trans "تنزيل" %}">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        <a href="{% url 'settings_app:restore' %}?backup_id={{ log.id }}" class="btn btn-sm btn-warning" title="{% trans "استعادة" %}">
                                            <i class="fas fa-undo"></i>
                                        </a>
                                    {% endif %}
                                    <form method="post" action="{% url 'settings_app:backup' %}" style="display: inline;">
                                        {% csrf_token %}
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="backup_id" value="{{ log.id }}">
                                        <button type="submit" class="btn btn-sm btn-danger delete-backup" title="{% trans "حذف" %}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center">{% trans "لا توجد نسخ احتياطية" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Create Backup Modal -->
    <div class="modal fade" id="createBackupModal" tabindex="-1" aria-labelledby="createBackupModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createBackupModalLabel">{% trans "إنشاء نسخة احتياطية" %}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="createBackupForm" method="post" action="{% url 'settings_app:backup' %}">
                        {% csrf_token %}
                        <input type="hidden" name="action" value="create">
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="include_media" name="include_media" checked>
                                <label class="form-check-label" for="include_media">
                                    {% trans "تضمين ملفات الوسائط" %}
                                </label>
                                <div class="form-text">{% trans "تضمين الصور وملفات الوسائط الأخرى في النسخة الاحتياطية. قد يزيد هذا من حجم النسخة الاحتياطية بشكل كبير." %}</div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="encrypt" name="encrypt" {% if backup_setting.encrypt_backups %}checked{% endif %}>
                                <label class="form-check-label" for="encrypt">
                                    {% trans "تشفير النسخة الاحتياطية" %}
                                </label>
                                <div class="form-text">{% trans "تشفير النسخة الاحتياطية لحماية البيانات الحساسة." %}</div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="storage_type" class="form-label">{% trans "موقع التخزين" %}</label>
                            <select class="form-select" id="storage_type" name="storage_type">
                                <option value="local" {% if backup_setting.storage_type == 'local' %}selected{% endif %}>{% trans "محلي (على الخادم)" %}</option>
                                <option value="google_drive" {% if backup_setting.storage_type == 'google_drive' %}selected{% endif %}>Google Drive</option>
                                <option value="dropbox" {% if backup_setting.storage_type == 'dropbox' %}selected{% endif %}>Dropbox</option>
                            </select>
                            <div class="form-text">{% trans "موقع تخزين النسخة الاحتياطية. تأكد من ربط الحساب السحابي أولاً إذا اخترت خيارًا سحابيًا." %}</div>
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label">{% trans "ملاحظات" %}</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                            <div class="form-text">{% trans "ملاحظات اختيارية حول هذه النسخة الاحتياطية." %}</div>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-1"></i> {% trans "قد تستغرق عملية النسخ الاحتياطي بعض الوقت اعتمادًا على حجم البيانات." %}
                        </div>
                        <div id="backupProgress" style="display: none;">
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                            </div>
                            <p id="backupStatus">{% trans "جاري إنشاء النسخة الاحتياطية..." %}</p>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="button" class="btn btn-primary" id="startBackupBtn">
                        <i class="fas fa-download me-1"></i> {% trans "بدء النسخ الاحتياطي" %}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $('#backupsTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/ar.json",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "order": [[1, "desc"]],
            "pageLength": 10,
            "lengthMenu": [[5, 10, 15, 20, 25, 50, -1], [5, 10, 15, 20, 25, 50, "الكل"]],
            "dom": 'lfrtip',
            "columnDefs": [
                { "width": "15%", "targets": 0 },
                { "width": "10%", "targets": 1 },
                { "width": "8%", "targets": 2 },
                { "width": "8%", "targets": 3 },
                { "width": "8%", "targets": 4 },
                { "width": "8%", "targets": 5 },
                { "width": "8%", "targets": 6 },
                { "width": "15%", "targets": 7 }
            ]
        });

        // Create Backup Button
        $('#createBackupBtn').click(function() {
            $('#createBackupModal').modal('show');
        });

        // Start Backup Button
        $('#startBackupBtn').click(function(e) {
            e.preventDefault();
            // Show progress bar
            $('#backupProgress').show();
            $('#backupStatus').text('{% trans "جاري إنشاء النسخة الاحتياطية..." %}');
            $('.progress-bar').css('width', '100%');
            // Submit form manually
            $('#createBackupForm').submit();
        });

        // Delete Backup Confirmation
        $('.delete-backup').click(function(e) {
            if (!confirm('{% trans "هل أنت متأكد من رغبتك في حذف هذه النسخة الاحتياطية؟ هذا الإجراء لا يمكن التراجع عنه." %}')) {
                e.preventDefault();
            }
        });

        // Delete All Backups Button
        $('#deleteAllBackupsBtn').click(function(e) {
            e.preventDefault();
            if (confirm('{% trans "هل أنت متأكد من رغبتك في حذف جميع النسخ الاحتياطية؟ هذا الإجراء لا يمكن التراجع عنه." %}')) {
                $('#deleteAllBackupsForm').submit();
            }
        });

        // Configure Backup Button
        $('#configureBackupBtn').click(function() {
            window.location.href = '{% url "settings_app:backup_settings" %}';
        });

        // Add tooltips to buttons
        $('[title]').tooltip();
    });
</script>
{% endblock %}
