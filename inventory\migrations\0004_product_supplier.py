# Generated by Django 5.2 on 2025-04-24 14:41

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0003_product_has_promotion_product_promotion_description_and_more'),
        ('purchases', '0002_suppliercategory_supplier_city_supplier_country_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='supplier',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='products', to='purchases.supplier', verbose_name='المورد'),
        ),
    ]
