{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "إدارة مواقع التخزين" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .location-card {
        transition: all 0.3s;
        height: 100%;
    }

    .location-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .location-count {
        position: absolute;
        top: 10px;
        left: 10px;
        background-color: #0d6efd;
        color: white;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }

    .pagination-numbers {
        display: flex;
        gap: 5px;
    }

    .page-number {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 4px;
        border: 1px solid #dee2e6;
        cursor: pointer;
        transition: all 0.2s;
    }

    .page-number:hover {
        background-color: #e9ecef;
    }

    .page-number.active {
        background-color: #0d6efd;
        color: white;
        border-color: #0d6efd;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "إدارة مواقع التخزين" %}</h1>
    <div>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addLocationModal">
            <i class="fas fa-plus me-1"></i> {% trans "إضافة موقع تخزين جديد" %}
        </button>
        <a href="{% url 'inventory:index' %}" class="btn btn-secondary ms-2">
            <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى المخزون" %}
        </a>
    </div>
</div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<!-- Search and Filter Bar -->
<div class="card shadow mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-6">
                <form method="get" action="{% url 'inventory:storage_locations' %}" id="searchForm">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="{% trans 'بحث عن موقع تخزين...' %}" name="search" value="{{ search_query }}">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                        {% if search_query %}
                        <a href="{% url 'inventory:storage_locations' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                        {% endif %}
                    </div>
                </form>
            </div>
            <div class="col-md-6 text-md-end mt-3 mt-md-0">
                <div class="d-flex justify-content-md-end align-items-center">
                    <span class="me-2">{% trans "عرض" %}</span>
                    <select id="itemsPerPage" class="form-select form-select-sm" style="width: auto;">
                        <option value="4">4</option>
                        <option value="8" selected>8</option>
                        <option value="12">12</option>
                        <option value="16">16</option>
                        <option value="20">20</option>
                        <option value="all">{% trans "الكل" %}</option>
                    </select>
                    <span class="ms-2">{% trans "موقع" %}</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Locations Stats -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <div>
        <span class="text-muted">{% trans "إجمالي مواقع التخزين:" %} <strong>{{ total_locations }}</strong></span>
    </div>
    <div id="paginationInfo" class="text-muted">
        {% trans "عرض" %} <span id="showingFrom">1</span>-<span id="showingTo">8</span> {% trans "من" %} {{ total_locations }}
    </div>
</div>

<!-- Locations Grid -->
<div id="locationsContainer">
    <div class="row" id="locationsGrid">
        {% for location in locations %}
        <div class="col-xl-3 col-md-6 mb-4 location-item">
            <div class="card shadow location-card">
                <div class="card-body">
                    <div class="location-count">{{ location.products.count }}</div>
                    <h5 class="card-title text-primary">{{ location.name }}</h5>
                    <p class="card-text text-muted">
                        {% if location.description %}
                        {{ location.description|truncatechars:100 }}
                        {% else %}
                        <span class="text-muted">{% trans "لا يوجد وصف" %}</span>
                        {% endif %}
                    </p>
                    <div class="text-muted small mb-3">
                        <i class="fas fa-calendar-alt me-1"></i> {% trans "تاريخ الإنشاء:" %} {{ location.created_at|date:"Y-m-d" }}
                    </div>
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-sm btn-primary edit-btn"
                                data-id="{{ location.id }}"
                                data-name="{{ location.name }}"
                                data-description="{{ location.description|default:'' }}">
                            <i class="fas fa-edit me-1"></i> {% trans "تعديل" %}
                        </button>
                        <button type="button" class="btn btn-sm btn-danger delete-btn"
                                data-id="{{ location.id }}"
                                data-name="{{ location.name }}"
                                data-products="{{ location.products.count }}">
                            <i class="fas fa-trash me-1"></i> {% trans "حذف" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-1"></i> {% trans "لا توجد مواقع تخزين مضافة حتى الآن" %}
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination Controls -->
    <div class="d-flex justify-content-between align-items-center mt-4" id="paginationControls">
        <div>
            <button id="prevPage" class="btn btn-sm btn-outline-primary" disabled>
                <i class="fas fa-chevron-right me-1"></i> {% trans "السابق" %}
            </button>
        </div>
        <div id="pageNumbers" class="pagination-numbers">
            <!-- Page numbers will be inserted here by JavaScript -->
        </div>
        <div>
            <button id="nextPage" class="btn btn-sm btn-outline-primary">
                {% trans "التالي" %} <i class="fas fa-chevron-left ms-1"></i>
            </button>
        </div>
    </div>
</div>

<!-- Add Location Modal -->
<div class="modal fade" id="addLocationModal" tabindex="-1" aria-labelledby="addLocationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addLocationModalLabel">{% trans "إضافة موقع تخزين جديد" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'inventory:add_storage_location' %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">{% trans "اسم موقع التخزين" %} <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">{% trans "وصف موقع التخزين" %}</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "إضافة" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Location Modal -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editModalLabel">{% trans "تعديل موقع التخزين" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" id="editForm">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">{% trans "اسم موقع التخزين" %} <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">{% trans "وصف موقع التخزين" %}</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "حفظ التغييرات" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Location Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">{% trans "حذف موقع التخزين" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>{% trans "هل أنت متأكد من رغبتك في حذف موقع التخزين:" %} <strong id="deleteName"></strong>؟</p>
                <div id="deleteWarning" class="alert alert-warning d-none">
                    <i class="fas fa-exclamation-triangle me-1"></i> {% trans "هذا الموقع يحتوي على منتجات! قد لا يتم حذفه." %}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                <form method="post" id="deleteForm">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">{% trans "حذف" %}</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Edit Location
        $('.edit-btn').click(function() {
            const id = $(this).data('id');
            const name = $(this).data('name');
            const description = $(this).data('description');

            $('#edit_name').val(name);
            $('#edit_description').val(description);
            $('#editForm').attr('action', '{% url "inventory:edit_storage_location" location_id=0 %}'.replace('0', id));

            $('#editModal').modal('show');
        });

        // Delete Location
        $('.delete-btn').click(function() {
            const id = $(this).data('id');
            const name = $(this).data('name');
            const products = $(this).data('products');

            $('#deleteName').text(name);
            $('#deleteForm').attr('action', '{% url "inventory:delete_storage_location" location_id=0 %}'.replace('0', id));

            if (products > 0) {
                $('#deleteWarning').removeClass('d-none');
            } else {
                $('#deleteWarning').addClass('d-none');
            }

            $('#deleteModal').modal('show');
        });

        // Pagination and Items Per Page functionality
        const locationItems = $('.location-item');
        const totalItems = locationItems.length;
        let itemsPerPage = 8; // Default value
        let currentPage = 1;

        // Initialize pagination
        function initPagination() {
            // Hide all items initially
            locationItems.hide();

            // Show items for the current page
            updatePageDisplay();

            // Create page number buttons
            updatePaginationControls();

            // Update pagination info
            updatePaginationInfo();
        }

        // Update which items are displayed based on current page and items per page
        function updatePageDisplay() {
            locationItems.hide();

            if (itemsPerPage === 'all') {
                locationItems.show();
                $('#paginationControls').hide();
            } else {
                const startIndex = (currentPage - 1) * itemsPerPage;
                const endIndex = Math.min(startIndex + parseInt(itemsPerPage), totalItems);

                for (let i = startIndex; i < endIndex; i++) {
                    $(locationItems[i]).show();
                }

                $('#paginationControls').show();
            }
        }

        // Update pagination controls (page numbers)
        function updatePaginationControls() {
            if (itemsPerPage === 'all') return;

            const totalPages = Math.ceil(totalItems / itemsPerPage);
            $('#pageNumbers').empty();

            // Create page number buttons
            for (let i = 1; i <= totalPages; i++) {
                const pageButton = $('<div class="page-number">' + i + '</div>');

                if (i === currentPage) {
                    pageButton.addClass('active');
                }

                pageButton.click(function() {
                    currentPage = i;
                    updatePageDisplay();
                    updatePaginationControls();
                    updatePaginationInfo();
                });

                $('#pageNumbers').append(pageButton);
            }

            // Update previous/next buttons
            $('#prevPage').prop('disabled', currentPage === 1);
            $('#nextPage').prop('disabled', currentPage === totalPages);
        }

        // Update pagination info text
        function updatePaginationInfo() {
            if (itemsPerPage === 'all') {
                $('#showingFrom').text('1');
                $('#showingTo').text(totalItems);
            } else {
                const startIndex = (currentPage - 1) * itemsPerPage + 1;
                const endIndex = Math.min(startIndex + parseInt(itemsPerPage) - 1, totalItems);

                $('#showingFrom').text(startIndex);
                $('#showingTo').text(endIndex);
            }
        }

        // Items per page change handler
        $('#itemsPerPage').change(function() {
            itemsPerPage = $(this).val();
            currentPage = 1;
            initPagination();
        });

        // Previous page button handler
        $('#prevPage').click(function() {
            if (currentPage > 1) {
                currentPage--;
                updatePageDisplay();
                updatePaginationControls();
                updatePaginationInfo();
            }
        });

        // Next page button handler
        $('#nextPage').click(function() {
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            if (currentPage < totalPages) {
                currentPage++;
                updatePageDisplay();
                updatePaginationControls();
                updatePaginationInfo();
            }
        });

        // Initialize pagination on page load
        initPagination();
    });
</script>
{% endblock %}
