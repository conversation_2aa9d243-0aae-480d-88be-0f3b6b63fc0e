# Generated by Django 5.2 on 2025-04-23 13:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('settings_app', '0008_alter_invoicesetting_primary_color_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='invoicesetting',
            name='next_number',
            field=models.PositiveIntegerField(blank=True, default=1001, null=True, verbose_name='الرقم التالي'),
        ),
        migrations.AlterField(
            model_name='invoicesetting',
            name='number_format',
            field=models.CharField(blank=True, choices=[('simple', 'بسيط (مثال: INV-1001)'), ('date', 'مع التاريخ (مثال: INV-YYYY-MM-0001)'), ('year', 'مع السنة (مثال: INV-YYYY-0001)'), ('month', 'مع الشهر (مثال: INV-MM-0001)')], default='simple', max_length=10, null=True, verbose_name='تنسيق الرقم'),
        ),
        migrations.AlterField(
            model_name='invoicesetting',
            name='prefix',
            field=models.CharField(blank=True, default='INV-', max_length=10, null=True, verbose_name='بادئة الفاتورة'),
        ),
        migrations.AlterField(
            model_name='invoicesetting',
            name='reset_sequence',
            field=models.CharField(blank=True, choices=[('never', 'أبداً'), ('yearly', 'سنوياً'), ('monthly', 'شهرياً')], default='never', max_length=10, null=True, verbose_name='إعادة تعيين التسلسل'),
        ),
    ]
