{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "اختيار المنتجات لطباعة الباركود" %}{% endblock %}

{% block extra_css %}
<style>
    .product-row:hover {
        background-color: #f8f9fa;
        cursor: pointer;
    }
    .barcode-code {
        font-family: 'Courier New', monospace;
        font-size: 0.85em;
        background-color: #f8f9fa;
        padding: 2px 4px;
        border-radius: 3px;
        border: 1px solid #dee2e6;
    }
    .search-highlight {
        background-color: yellow;
        font-weight: bold;
    }
    .category-badge {
        font-size: 0.75em;
    }
    .quantity-badge {
        font-size: 0.8em;
        min-width: 50px;
    }
    .table-responsive {
        border-radius: 0.375rem;
        border: 1px solid #dee2e6;
    }
    .search-form {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.375rem;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-print me-2"></i>{% trans "اختيار المنتجات لطباعة الباركود" %}</h5>
                        <div class="text-end">
                            <small class="d-block">{% trans "إجمالي المنتجات" %}: {{ total_products }}</small>
                            {% if search_query or category_filter %}
                                <small class="d-block">{% trans "النتائج المعروضة" %}: {{ products|length }}</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form action="{% url 'inventory:barcode:bulk_print_barcodes' %}" method="post">
                        {% csrf_token %}

                        <!-- عدد النسخ -->
                        <div class="mb-3">
                            <label for="copies" class="form-label">{% trans "عدد النسخ لكل باركود" %}</label>
                            <input type="number" class="form-control" id="copies" name="copies" min="1" max="100" value="1">
                            <div class="form-text">{% trans "أقصى عدد نسخ مسموح به هو 100 نسخة" %}</div>
                        </div>

                        <!-- فلاتر البحث والتصفية -->
                        <div class="search-form">
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label for="product_search" class="form-label">{% trans "البحث عن منتج" %}</label>
                                <form method="get" action="{% url 'inventory:select_products_for_barcode' %}" id="searchForm">
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="search" id="product_search"
                                               placeholder="{% trans 'اسم المنتج أو الكود أو الوصف أو الباركود' %}"
                                               value="{{ search_query|default_if_none:'' }}">
                                        <input type="hidden" name="category" value="{{ category_filter }}">
                                        <button class="btn btn-outline-primary" type="submit" id="searchBtn">
                                            <i class="fas fa-search"></i> {% trans "بحث" %}
                                        </button>
                                        {% if search_query or category_filter %}
                                            <a href="{% url 'inventory:select_products_for_barcode' %}" class="btn btn-outline-secondary">
                                                <i class="fas fa-times"></i> {% trans "مسح" %}
                                            </a>
                                        {% endif %}
                                    </div>
                                </form>
                            </div>
                            <div class="col-md-4">
                                <label for="category_filter" class="form-label">{% trans "تصفية حسب الفئة" %}</label>
                                <select class="form-select" id="category_filter" name="category">
                                    <option value="">{% trans "جميع الفئات" %}</option>
                                    {% for category in categories %}
                                        <option value="{{ category.id }}" {% if category.id|stringformat:"s" == category_filter %}selected{% endif %}>
                                            {{ category.name }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <!-- عرض عدد النتائج -->
                        {% if search_query or category_filter %}
                        <div class="alert alert-info mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            {% blocktrans count counter=total_products %}
                                تم العثور على {{ counter }} منتج
                            {% plural %}
                                تم العثور على {{ counter }} منتج
                            {% endblocktrans %}
                            {% if search_query %}
                                للبحث: "{{ search_query }}"
                            {% endif %}
                        </div>
                        {% endif %}
                        </div>

                        <!-- قائمة المنتجات -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="products_table">
                                <thead>
                                    <tr>
                                        <th width="50px">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="select_all">
                                            </div>
                                        </th>
                                        <th>{% trans "الكود" %}</th>
                                        <th>{% trans "اسم المنتج" %}</th>
                                        <th>{% trans "الفئة" %}</th>
                                        <th>{% trans "الباركود" %}</th>
                                        <th>{% trans "الكمية" %}</th>
                                        <th>{% trans "السعر" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for product in products %}
                                    <tr class="product-row">
                                        <td>
                                            <div class="form-check">
                                                <input class="form-check-input product-checkbox" type="checkbox" name="product_ids" value="{{ product.id }}">
                                            </div>
                                        </td>
                                        <td><code>{{ product.code }}</code></td>
                                        <td>
                                            <strong>{{ product.name }}</strong>
                                            {% if product.description %}
                                                <br><small class="text-muted">{{ product.description|truncatechars:50 }}</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary category-badge">{{ product.category.name }}</span>
                                        </td>
                                        <td>
                                            {% if product.barcodes.exists %}
                                                {% for barcode in product.barcodes.all|slice:":2" %}
                                                    <code class="barcode-code d-block"
                                                          data-bs-toggle="tooltip"
                                                          data-bs-placement="top"
                                                          title="{% trans 'نوع الباركود' %}: {{ barcode.barcode_type.name|default:'غير محدد' }}">
                                                        {{ barcode.barcode_number }}
                                                    </code>
                                                {% endfor %}
                                                {% if product.barcodes.count > 2 %}
                                                    <small class="text-muted"
                                                           data-bs-toggle="tooltip"
                                                           data-bs-placement="top"
                                                           title="{% trans 'انقر لعرض جميع الباركودات' %}">
                                                        +{{ product.barcodes.count|add:"-2" }} أخرى
                                                    </small>
                                                {% endif %}
                                            {% else %}
                                                <span class="text-muted"><i class="fas fa-barcode me-1"></i>{% trans "لا يوجد باركود" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge quantity-badge {% if product.quantity > product.min_quantity %}bg-success{% elif product.quantity > 0 %}bg-warning{% else %}bg-danger{% endif %}">
                                                <i class="fas fa-boxes me-1"></i>{{ product.quantity }}
                                            </span>
                                        </td>
                                        <td>{{ product.selling_price }} {% trans "ج.م" %}</td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-box-open fa-3x mb-3"></i>
                                                <h5>{% trans "لا توجد منتجات متاحة" %}</h5>
                                                {% if search_query or category_filter %}
                                                    <p>{% trans "جرب تغيير معايير البحث أو التصفية" %}</p>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- معلومات الاختيار -->
                        <div class="alert alert-light border mt-3" id="selection-info" style="display: none;">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <i class="fas fa-info-circle text-primary me-2"></i>
                                    <span id="selection-text">{% trans "لم يتم اختيار أي منتجات بعد" %}</span>
                                </div>
                                <div class="col-md-4 text-end">
                                    <small class="text-muted">
                                        {% trans "عدد النسخ لكل باركود" %}: <span id="copies-display">1</span>
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="mt-3 d-flex justify-content-between align-items-center">
                            <div>
                                <a href="{% url 'inventory:barcode:barcode_dashboard' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>{% trans "العودة إلى لوحة التحكم" %}
                                </a>
                            </div>
                            <div>
                                <button type="button" class="btn btn-outline-primary me-2" id="select-visible-btn">
                                    <i class="fas fa-check-square me-2"></i>{% trans "اختيار المعروض" %}
                                </button>
                                <button type="button" class="btn btn-outline-warning me-2" id="clear-selection-btn">
                                    <i class="fas fa-times me-2"></i>{% trans "مسح الاختيار" %}
                                </button>
                                <button type="button" class="btn btn-secondary btn-lg disabled" id="print_button" disabled>
                                    <i class="fas fa-hand-pointer me-2"></i>{% trans "اختر منتجات للطباعة" %}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // تفعيل/تعطيل زر الطباعة بناءً على اختيار المنتجات
        function updatePrintButton() {
            var checkedProducts = $('.product-checkbox:checked').length;
            var printButton = $('#print_button');

            if (checkedProducts === 0) {
                printButton.addClass('disabled').attr('disabled', true);
                printButton.removeClass('btn-success').addClass('btn-secondary');
                printButton.html('<i class="fas fa-hand-pointer me-2"></i>اختر منتجات للطباعة');
            } else {
                printButton.removeClass('disabled').removeAttr('disabled');
                printButton.removeClass('btn-secondary').addClass('btn-success');
                printButton.html('<i class="fas fa-print me-2"></i>طباعة الباركودات (' + checkedProducts + ' منتج)');
            }
        }

        // تحديث حالة زر الطباعة عند تغيير حالة أي خانة اختيار
        $(document).on('change', '.product-checkbox', function() {
            console.log('Checkbox changed:', $(this).is(':checked'));
            updatePrintButton();
        });

        // إضافة معالج لزر الطباعة
        $('#print_button').on('click', function(e) {
            console.log('Print button clicked');
            var checkedProducts = $('.product-checkbox:checked').length;
            console.log('Checked products on click:', checkedProducts);

            // جمع معرفات المنتجات المختارة
            var selectedProductIds = [];
            $('.product-checkbox:checked').each(function() {
                selectedProductIds.push($(this).val());
            });
            console.log('Selected product IDs:', selectedProductIds);

            if (checkedProducts === 0) {
                e.preventDefault();
                alert('يرجى اختيار منتج واحد على الأقل للطباعة');
                return false;
            }

            console.log('Form will be submitted');
        });

        // تحقق عند إرسال النموذج
        $('form').on('submit', function(e) {
            var checkedProducts = $('.product-checkbox:checked').length;
            console.log('Form submitted, checked products:', checkedProducts);

            if (checkedProducts === 0) {
                e.preventDefault();
                alert('يرجى اختيار منتج واحد على الأقل للطباعة');
                return false;
            }

            // إظهار رسالة تحميل
            $('#print_button').html('<i class="fas fa-spinner fa-spin me-2"></i>جاري الطباعة...');
            $('#print_button').prop('disabled', true);
        });

        // تحديث حالة الزر عند تحميل الصفحة
        updatePrintButton();

        // معالج زر الطباعة
        $('#print_button').on('click', function() {
            console.log('Print button clicked');
            var checkedProducts = $('.product-checkbox:checked').length;
            console.log('Checked products:', checkedProducts);

            // جمع معرفات المنتجات المختارة
            var selectedProductIds = [];
            $('.product-checkbox:checked').each(function() {
                selectedProductIds.push($(this).val());
            });
            console.log('Selected product IDs:', selectedProductIds);

            if (checkedProducts === 0) {
                alert('يرجى اختيار منتج واحد على الأقل للطباعة');
                return false;
            }

            // إظهار رسالة تحميل
            $(this).html('<i class="fas fa-spinner fa-spin me-2"></i>جاري الطباعة...');
            $(this).prop('disabled', true);

            // إنشاء نموذج جديد وإرساله
            var newForm = $('<form>', {
                'method': 'POST',
                'action': '/inventory/barcode/bulk-print/'
            });

            // إضافة CSRF token
            newForm.append($('<input>', {
                'type': 'hidden',
                'name': 'csrfmiddlewaretoken',
                'value': $('[name=csrfmiddlewaretoken]').val()
            }));

            // إضافة عدد النسخ
            newForm.append($('<input>', {
                'type': 'hidden',
                'name': 'copies',
                'value': $('#copies').val()
            }));

            // إضافة معرفات المنتجات
            selectedProductIds.forEach(function(productId) {
                newForm.append($('<input>', {
                    'type': 'hidden',
                    'name': 'product_ids',
                    'value': productId
                }));
            });

            // إضافة النموذج إلى الصفحة وإرساله
            $('body').append(newForm);
            console.log('Submitting new form with data:', {
                product_ids: selectedProductIds,
                copies: $('#copies').val()
            });
            newForm.submit();
        });

        // تحديد/إلغاء تحديد كل المنتجات
        $('#select_all').on('change', function() {
            $('.product-checkbox').prop('checked', $(this).prop('checked'));
            updatePrintButton();
        });

        // البحث في المنتجات (بحث محلي فقط للعناصر المعروضة)
        $('#product_search').on('keyup', function(e) {
            // إذا ضغط Enter، قم بإرسال النموذج
            if (e.which === 13) {
                $('#searchForm').submit();
                return;
            }

            // بحث محلي في الجدول
            var value = $(this).val().toLowerCase();
            $("#products_table tbody tr").filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            });
        });

        // تصفية حسب الفئة
        $('#category_filter').on('change', function() {
            var categoryId = $(this).val();
            var currentSearch = $('#product_search').val();

            // إعادة توجيه مع الفلتر الجديد
            var url = new URL(window.location.href);
            if (categoryId) {
                url.searchParams.set('category', categoryId);
            } else {
                url.searchParams.delete('category');
            }
            if (currentSearch) {
                url.searchParams.set('search', currentSearch);
            }
            window.location.href = url.toString();
        });

        // زر مسح البحث
        $('.btn-outline-secondary').on('click', function(e) {
            e.preventDefault();
            window.location.href = "{% url 'inventory:select_products_for_barcode' %}";
        });

        // بحث فوري باستخدام AJAX (اختياري)
        let searchTimeout;
        $('#product_search').on('input', function() {
            clearTimeout(searchTimeout);
            const searchTerm = $(this).val();

            // إذا كان البحث فارغاً أو أقل من 3 أحرف، اعرض جميع العناصر
            if (searchTerm.length < 3) {
                $("#products_table tbody tr").show();
                return;
            }

            // تأخير البحث لتجنب الطلبات المتعددة
            searchTimeout = setTimeout(function() {
                // بحث محلي في الجدول الحالي
                const value = searchTerm.toLowerCase();
                $("#products_table tbody tr").filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
                });
            }, 300);
        });

        // عداد العناصر المختارة عند تغيير العرض
        function updateSelectedCount() {
            const selectedCount = $('.product-checkbox:checked').length;
            const totalVisible = $('#products_table tbody tr:visible').length;
            const copies = $('#copies').val() || 1;

            // تحديث زر الطباعة
            $('#print_button').prop('disabled', selectedCount === 0);
            if (selectedCount > 0) {
                $('#print_button').html(`<i class="fas fa-print me-2"></i>{% trans 'طباعة الباركودات' %} (${selectedCount})`);
            } else {
                $('#print_button').html('<i class="fas fa-print me-2"></i>{% trans "طباعة الباركودات" %}');
            }

            // تحديث معلومات الاختيار
            if (selectedCount > 0) {
                $('#selection-info').show();
                const totalBarcodes = selectedCount * copies;
                $('#selection-text').html(`
                    {% trans 'تم اختيار' %} <strong>${selectedCount}</strong> {% trans 'منتج' %} -
                    {% trans 'سيتم طباعة' %} <strong>${totalBarcodes}</strong> {% trans 'باركود' %}
                `);
            } else {
                $('#selection-info').hide();
            }

            // تحديث عرض عدد النسخ
            $('#copies-display').text(copies);
        }

        // تحديث العداد عند تغيير الاختيار
        $(document).on('change', '.product-checkbox', updateSelectedCount);
        $(document).on('change', '#select_all', updateSelectedCount);

        // النقر على الصف لتحديد المنتج
        $(document).on('click', '.product-row', function(e) {
            // تجنب النقر على checkbox مباشرة
            if (e.target.type !== 'checkbox') {
                const checkbox = $(this).find('.product-checkbox');
                checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
            }
        });

        // تمييز الصفوف المختارة
        $(document).on('change', '.product-checkbox', function() {
            if ($(this).is(':checked')) {
                $(this).closest('tr').addClass('table-primary');
            } else {
                $(this).closest('tr').removeClass('table-primary');
            }
        });

        // تطبيق التمييز عند تحميل الصفحة
        $('.product-checkbox:checked').each(function() {
            $(this).closest('tr').addClass('table-primary');
        });

        // زر اختيار جميع العناصر المعروضة
        $('#select-visible-btn').on('click', function() {
            $('#products_table tbody tr:visible .product-checkbox').prop('checked', true).trigger('change');
        });

        // زر مسح جميع الاختيارات
        $('#clear-selection-btn').on('click', function() {
            $('.product-checkbox').prop('checked', false).trigger('change');
            $('#select_all').prop('checked', false);
        });

        // تحديث عدد النسخ في الوقت الفعلي
        $('#copies').on('input change', function() {
            updateSelectedCount();
        });

        // تحديث أولي
        updateSelectedCount();

        // تفعيل tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}