# Generated by Django 5.2 on 2025-04-24 09:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('settings_app', '0009_alter_invoicesetting_next_number_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='invoicesetting',
            name='auto_print',
            field=models.BooleanField(default=True, verbose_name='طباعة تلقائية'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='invoice_subtitle',
            field=models.CharField(blank=True, default='شكراً لتعاملكم معنا', max_length=200, null=True, verbose_name='العنوان الفرعي'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='invoice_title',
            field=models.CharField(blank=True, default='فاتورة مبيعات', max_length=100, null=True, verbose_name='عنوان الفاتورة'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='margin_bottom',
            field=models.PositiveSmallIntegerField(default=10, verbose_name='الهامش السفلي'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='margin_left',
            field=models.PositiveSmallIntegerField(default=10, verbose_name='الهامش الأيسر'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='margin_right',
            field=models.PositiveSmallIntegerField(default=10, verbose_name='الهامش الأيمن'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='margin_top',
            field=models.PositiveSmallIntegerField(default=10, verbose_name='الهامش العلوي'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='print_background',
            field=models.BooleanField(default=True, verbose_name='طباعة الخلفيات'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='print_copies',
            field=models.PositiveSmallIntegerField(default=1, verbose_name='عدد النسخ'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='print_header_footer',
            field=models.BooleanField(default=True, verbose_name='طباعة رأس وتذييل الصفحة'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='show_created_at',
            field=models.BooleanField(default=True, verbose_name='إظهار تاريخ الإنشاء'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='show_customer',
            field=models.BooleanField(default=True, verbose_name='إظهار العميل'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='show_employee',
            field=models.BooleanField(default=True, verbose_name='إظهار الموظف'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='show_invoice_number',
            field=models.BooleanField(default=True, verbose_name='إظهار رقم الفاتورة'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='show_payment_method',
            field=models.BooleanField(default=True, verbose_name='إظهار طريقة الدفع'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='show_product_code',
            field=models.BooleanField(default=True, verbose_name='إظهار كود المنتج'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='show_product_description',
            field=models.BooleanField(default=True, verbose_name='إظهار وصف المنتج'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='show_quantity',
            field=models.BooleanField(default=True, verbose_name='إظهار الكمية'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='show_sale_date',
            field=models.BooleanField(default=True, verbose_name='إظهار تاريخ البيع'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='show_status',
            field=models.BooleanField(default=True, verbose_name='إظهار الحالة'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='show_unit_price',
            field=models.BooleanField(default=True, verbose_name='إظهار سعر الوحدة'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='show_updated_at',
            field=models.BooleanField(default=True, verbose_name='إظهار آخر تحديث'),
        ),
    ]
