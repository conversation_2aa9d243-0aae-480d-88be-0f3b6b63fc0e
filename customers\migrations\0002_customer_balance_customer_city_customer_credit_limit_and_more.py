# Generated by Django 5.2 on 2025-04-19 15:05

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('customers', '0001_initial'),
        ('employees', '0001_initial'),
        ('sales', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='customer',
            name='balance',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الرصيد الحالي'),
        ),
        migrations.AddField(
            model_name='customer',
            name='city',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='المدينة'),
        ),
        migrations.AddField(
            model_name='customer',
            name='credit_limit',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='حد الائتمان'),
        ),
        migrations.AddField(
            model_name='customer',
            name='last_purchase_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ آخر شراء'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True, unique=True, verbose_name='البريد الإلكتروني'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='phone',
            field=models.CharField(max_length=20, unique=True, verbose_name='رقم الهاتف'),
        ),
        migrations.CreateModel(
            name='CustomerImport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='customer_imports/', verbose_name='ملف الاستيراد')),
                ('status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('processing', 'قيد المعالجة'), ('completed', 'مكتمل'), ('failed', 'فشل')], default='pending', max_length=20, verbose_name='الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('processed_records', models.PositiveIntegerField(default=0, verbose_name='السجلات المعالجة')),
                ('total_records', models.PositiveIntegerField(default=0, verbose_name='إجمالي السجلات')),
                ('error_log', models.TextField(blank=True, null=True, verbose_name='سجل الأخطاء')),
                ('imported_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم الاستيراد بواسطة')),
            ],
            options={
                'verbose_name': 'استيراد العملاء',
                'verbose_name_plural': 'عمليات استيراد العملاء',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CustomerInteraction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('interaction_type', models.CharField(choices=[('call', 'مكالمة هاتفية'), ('email', 'بريد إلكتروني'), ('visit', 'زيارة'), ('purchase', 'عملية شراء'), ('return', 'عملية إرجاع'), ('complaint', 'شكوى'), ('other', 'أخرى')], max_length=20, verbose_name='نوع التفاعل')),
                ('date', models.DateTimeField(auto_now_add=True, verbose_name='التاريخ')),
                ('notes', models.TextField(verbose_name='ملاحظات')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interactions', to='customers.customer', verbose_name='العميل')),
                ('employee', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='employees.employeeprofile', verbose_name='الموظف')),
                ('related_sale', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.sale', verbose_name='المبيعات المرتبطة')),
            ],
            options={
                'verbose_name': 'تفاعل العميل',
                'verbose_name_plural': 'تفاعلات العملاء',
                'ordering': ['-date'],
            },
        ),
    ]
