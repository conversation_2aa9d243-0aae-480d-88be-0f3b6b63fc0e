{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "فئات الموردين" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .category-card {
        transition: all 0.3s;
        height: 100%;
    }
    
    .category-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .category-count {
        position: absolute;
        top: 10px;
        left: 10px;
        background-color: #4e73df;
        color: white;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }
    
    .required-field::after {
        content: " *";
        color: red;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "فئات الموردين" %}</h1>
    <div>
        <a href="{% url 'purchases:suppliers' %}" class="btn btn-primary">
            <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى الموردين" %}
        </a>
    </div>
</div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<!-- Add Category Form -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "إضافة فئة جديدة" %}</h6>
    </div>
    <div class="card-body">
        <form method="post" action="{% url 'purchases:add_supplier_category' %}">
            {% csrf_token %}
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="name" class="form-label required-field">{% trans "اسم الفئة" %}</label>
                    <input type="text" class="form-control" id="name" name="name" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="description" class="form-label">{% trans "الوصف" %}</label>
                    <textarea class="form-control" id="description" name="description" rows="1"></textarea>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i> {% trans "إضافة فئة" %}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Categories Grid -->
<div class="row">
    {% for category in categories %}
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card shadow category-card">
            <div class="card-body">
                <div class="category-count">{{ category.suppliers.count }}</div>
                <h5 class="card-title text-primary">{{ category.name }}</h5>
                <p class="card-text text-muted">
                    {% if category.description %}
                    {{ category.description|truncatechars:100 }}
                    {% else %}
                    <span class="text-muted">{% trans "لا يوجد وصف" %}</span>
                    {% endif %}
                </p>
                <div class="text-muted small mb-3">
                    <i class="fas fa-calendar-alt me-1"></i> {% trans "تاريخ الإنشاء:" %} {{ category.created_at|date:"Y-m-d" }}
                </div>
                <div class="d-flex justify-content-between">
                    <button type="button" class="btn btn-sm btn-primary edit-category" 
                            data-bs-toggle="modal" data-bs-target="#editCategoryModal"
                            data-id="{{ category.id }}"
                            data-name="{{ category.name }}"
                            data-description="{{ category.description|default:'' }}">
                        <i class="fas fa-edit me-1"></i> {% trans "تعديل" %}
                    </button>
                    <button type="button" class="btn btn-sm btn-danger delete-category" 
                            data-bs-toggle="modal" data-bs-target="#deleteCategoryModal"
                            data-id="{{ category.id }}"
                            data-name="{{ category.name }}"
                            data-count="{{ category.suppliers.count }}">
                        <i class="fas fa-trash me-1"></i> {% trans "حذف" %}
                    </button>
                </div>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-12">
        <div class="alert alert-info">
            {% trans "لا توجد فئات للموردين. قم بإضافة فئة جديدة." %}
        </div>
    </div>
    {% endfor %}
</div>

<!-- Edit Category Modal -->
<div class="modal fade" id="editCategoryModal" tabindex="-1" aria-labelledby="editCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editCategoryModalLabel">{% trans "تعديل فئة" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="" id="editCategoryForm">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label required-field">{% trans "اسم الفئة" %}</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">{% trans "الوصف" %}</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "حفظ التغييرات" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Category Modal -->
<div class="modal fade" id="deleteCategoryModal" tabindex="-1" aria-labelledby="deleteCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteCategoryModalLabel">{% trans "حذف فئة" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="delete-message">{% trans "هل أنت متأكد من حذف هذه الفئة؟" %}</p>
                <div id="delete-warning" class="alert alert-danger d-none">
                    {% trans "لا يمكن حذف هذه الفئة لأنها تحتوي على موردين. قم بنقل الموردين إلى فئة أخرى أولاً." %}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                <form method="post" action="" id="deleteCategoryForm">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger" id="confirmDeleteBtn">{% trans "حذف" %}</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Edit Category Modal
        $('.edit-category').click(function() {
            var id = $(this).data('id');
            var name = $(this).data('name');
            var description = $(this).data('description');
            
            $('#edit_name').val(name);
            $('#edit_description').val(description);
            $('#editCategoryForm').attr('action', "{% url 'purchases:edit_supplier_category' category_id=0 %}".replace('0', id));
        });
        
        // Delete Category Modal
        $('.delete-category').click(function() {
            var id = $(this).data('id');
            var name = $(this).data('name');
            var count = $(this).data('count');
            
            $('#delete-message').text("{% trans 'هل أنت متأكد من حذف فئة' %} \"" + name + "\"؟");
            $('#deleteCategoryForm').attr('action', "{% url 'purchases:delete_supplier_category' category_id=0 %}".replace('0', id));
            
            if (count > 0) {
                $('#delete-warning').removeClass('d-none');
                $('#confirmDeleteBtn').prop('disabled', true);
            } else {
                $('#delete-warning').addClass('d-none');
                $('#confirmDeleteBtn').prop('disabled', false);
            }
        });
    });
</script>
{% endblock %}
