{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "حذف البيع" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .delete-warning {
        border-right: 5px solid #e74a3b;
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
    }

    .delete-warning i {
        color: #e74a3b;
        font-size: 2rem;
        margin-bottom: 15px;
    }

    .sale-info {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .sale-info dt {
        font-weight: bold;
    }

    .sale-info dd {
        margin-bottom: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "حذف البيع" %}</h1>
    <a href="{% url 'sales:view_sale' sale.id %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى تفاصيل البيع" %}
    </a>
</div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow">
            <div class="card-body">
                <div class="delete-warning text-center">
                    <i class="fas fa-exclamation-triangle d-block"></i>
                    <h4 class="text-danger">{% trans "تحذير: أنت على وشك حذف هذا البيع" %}</h4>
                    <p class="mb-0">{% trans "هذا الإجراء لا يمكن التراجع عنه. سيتم إعادة كميات المنتجات إلى المخزون." %}</p>
                </div>

                <div class="sale-info mb-4">
                    <h5 class="mb-3">{% trans "معلومات البيع" %}</h5>
                    <dl class="row mb-0">
                        <dt class="col-sm-4">{% trans "رقم الفاتورة:" %}</dt>
                        <dd class="col-sm-8">{{ sale.invoice_number }}</dd>

                        <dt class="col-sm-4">{% trans "العميل:" %}</dt>
                        <dd class="col-sm-8">{{ sale.customer.name }}</dd>

                        <dt class="col-sm-4">{% trans "تاريخ البيع:" %}</dt>
                        <dd class="col-sm-8">{{ sale.date|date:"Y-m-d H:i" }}</dd>

                        <dt class="col-sm-4">{% trans "المبلغ الإجمالي:" %}</dt>
                        <dd class="col-sm-8">{{ sale.total_amount|floatformat:2 }} د.م</dd>

                        <dt class="col-sm-4">{% trans "الحالة:" %}</dt>
                        <dd class="col-sm-8">
                            {% if sale.status == 'completed' %}
                            <span class="badge bg-success">{% trans "مكتمل" %}</span>
                            {% elif sale.status == 'pending' %}
                            <span class="badge bg-warning">{% trans "معلق" %}</span>
                            {% elif sale.status == 'cancelled' %}
                            <span class="badge bg-danger">{% trans "ملغي" %}</span>
                            {% endif %}
                        </dd>

                        <dt class="col-sm-4">{% trans "عدد المنتجات:" %}</dt>
                        <dd class="col-sm-8">{{ sale.items.count }}</dd>
                    </dl>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-1"></i>
                    {% trans "ملاحظة: سيتم إعادة كميات المنتجات التالية إلى المخزون:" %}
                    <ul class="mb-0 mt-2">
                        {% for item in sale.items.all %}
                        <li>{{ item.product.name }} ({{ item.quantity }} {{ item.product.unit }})</li>
                        {% endfor %}
                    </ul>
                </div>

                <form method="post">
                    {% csrf_token %}
                    <div class="form-check mb-4">
                        <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                        <label class="form-check-label" for="confirmDelete">
                            {% trans "أؤكد أنني أريد حذف هذا البيع وإعادة كميات المنتجات إلى المخزون" %}
                        </label>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'sales:view_sale' sale.id %}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i> {% trans "إلغاء" %}
                        </a>
                        <button type="submit" class="btn btn-danger" id="deleteBtn" disabled>
                            <i class="fas fa-trash me-1"></i> {% trans "حذف البيع" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Enable/disable delete button based on checkbox
        $('#confirmDelete').change(function() {
            $('#deleteBtn').prop('disabled', !$(this).prop('checked'));
        });
    });
</script>
{% endblock %}
