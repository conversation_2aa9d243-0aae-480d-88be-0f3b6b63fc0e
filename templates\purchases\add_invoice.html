{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "إضافة فاتورة" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .purchase-header {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .purchase-info {
        margin-bottom: 0;
    }
    
    .purchase-info dt {
        font-weight: bold;
    }
    
    .purchase-info dd {
        margin-bottom: 0.5rem;
    }
    
    .required-field::after {
        content: " *";
        color: red;
    }
    
    .form-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .form-section-title {
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "إضافة فاتورة" %}</h1>
    <div>
        <a href="{% url 'purchases:view_purchase' purchase_id=purchase.id %}" class="btn btn-primary">
            <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى تفاصيل الطلب" %}
        </a>
    </div>
</div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<!-- Purchase Header -->
<div class="purchase-header">
    <div class="row">
        <div class="col-md-6">
            <h2>{{ purchase.reference_number }}</h2>
            <p class="text-muted">{% trans "تاريخ الطلب" %}: {{ purchase.date|date:"Y-m-d" }}</p>
        </div>
        <div class="col-md-6 text-md-end">
            <div class="mb-2">
                {% if purchase.status == 'pending' %}
                <span class="badge bg-warning text-dark">{% trans "معلق" %}</span>
                {% elif purchase.status == 'received' %}
                <span class="badge bg-success">{% trans "تم الاستلام" %}</span>
                {% elif purchase.status == 'cancelled' %}
                <span class="badge bg-danger">{% trans "ملغي" %}</span>
                {% endif %}
            </div>
            <div>
                {% if purchase.payment_status == 'unpaid' %}
                <span class="badge bg-danger">{% trans "غير مدفوع" %}</span>
                {% elif purchase.payment_status == 'partial' %}
                <span class="badge bg-warning text-dark">{% trans "مدفوع جزئياً" %}</span>
                {% elif purchase.payment_status == 'paid' %}
                <span class="badge bg-success">{% trans "مدفوع بالكامل" %}</span>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-6">
            <h5>{% trans "معلومات المورد" %}</h5>
            <dl class="row purchase-info">
                <dt class="col-sm-4">{% trans "اسم المورد" %}</dt>
                <dd class="col-sm-8">{{ purchase.supplier.name }}</dd>
                
                <dt class="col-sm-4">{% trans "رقم الهاتف" %}</dt>
                <dd class="col-sm-8">{{ purchase.supplier.phone }}</dd>
                
                {% if purchase.supplier.email %}
                <dt class="col-sm-4">{% trans "البريد الإلكتروني" %}</dt>
                <dd class="col-sm-8">{{ purchase.supplier.email }}</dd>
                {% endif %}
            </dl>
        </div>
        <div class="col-md-6">
            <h5>{% trans "معلومات الطلب" %}</h5>
            <dl class="row purchase-info">
                <dt class="col-sm-5">{% trans "المبلغ الإجمالي" %}</dt>
                <dd class="col-sm-7">{{ purchase.total_amount|floatformat:2 }} ر.س</dd>
                
                <dt class="col-sm-5">{% trans "حالة الدفع" %}</dt>
                <dd class="col-sm-7">
                    {% if purchase.payment_status == 'unpaid' %}
                    {% trans "غير مدفوع" %}
                    {% elif purchase.payment_status == 'partial' %}
                    {% trans "مدفوع جزئياً" %}
                    {% elif purchase.payment_status == 'paid' %}
                    {% trans "مدفوع بالكامل" %}
                    {% endif %}
                </dd>
            </dl>
        </div>
    </div>
</div>

<!-- Invoice Form -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "معلومات الفاتورة" %}</h6>
    </div>
    <div class="card-body">
        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            
            <div class="form-section">
                <h5 class="form-section-title">{% trans "المعلومات الأساسية" %}</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="invoice_number" class="form-label required-field">{% trans "رقم الفاتورة" %}</label>
                        <input type="text" class="form-control" id="invoice_number" name="invoice_number" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="invoice_date" class="form-label required-field">{% trans "تاريخ الفاتورة" %}</label>
                        <input type="date" class="form-control" id="invoice_date" name="invoice_date" value="{{ today }}" required>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="due_date" class="form-label">{% trans "تاريخ الاستحقاق" %}</label>
                        <input type="date" class="form-control" id="due_date" name="due_date">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="amount" class="form-label required-field">{% trans "المبلغ الإجمالي" %}</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="0" value="{{ purchase.total_amount }}" required>
                            <span class="input-group-text">ر.س</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-section">
                <h5 class="form-section-title">{% trans "معلومات إضافية" %}</h5>
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="notes" class="form-label">{% trans "ملاحظات" %}</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="invoice_file" class="form-label">{% trans "ملف الفاتورة" %}</label>
                        <input type="file" class="form-control" id="invoice_file" name="invoice_file" accept=".pdf,.jpg,.jpeg,.png">
                        <div class="form-text">{% trans "يمكنك تحميل نسخة من الفاتورة (PDF، JPG، PNG)" %}</div>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                {% trans "سيتم إضافة الفاتورة وربطها بطلب الشراء الحالي." %}
            </div>
            
            <div class="mt-4">
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-save me-1"></i> {% trans "حفظ الفاتورة" %}
                </button>
                <a href="{% url 'purchases:view_purchase' purchase_id=purchase.id %}" class="btn btn-secondary">
                    {% trans "إلغاء" %}
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Set default due date to 30 days after invoice date
        $('#invoice_date').change(function() {
            var invoiceDate = new Date($(this).val());
            if (!isNaN(invoiceDate.getTime())) {
                invoiceDate.setDate(invoiceDate.getDate() + 30);
                var dueDate = invoiceDate.toISOString().split('T')[0];
                $('#due_date').val(dueDate);
            }
        });
        
        // Trigger change to set initial due date
        $('#invoice_date').trigger('change');
        
        // Validate form before submit
        $('form').submit(function(e) {
            var isValid = true;
            
            // Check if all required fields are filled
            $(this).find('[required]').each(function() {
                if ($(this).val() === '') {
                    isValid = false;
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                alert('{% trans "يرجى ملء جميع الحقول المطلوبة" %}');
                return false;
            }
            
            return true;
        });
    });
</script>
{% endblock %}
