// تهيئة القائمة العلوية
document.addEventListener('DOMContentLoaded', function() {
    // استخدام وظائف Bootstrap الافتراضية للقوائم المنسدلة
    // لا نحتاج لكود إضافي لأن Bootstrap يتعامل مع القوائم المنسدلة تلقائياً

    // إصلاح مشكلة النوافذ المنسدلة في صفحة المبيعات
    const salesDropdowns = document.querySelectorAll('.dropdown-toggle[data-bs-toggle="dropdown"]');
    if (salesDropdowns) {
        salesDropdowns.forEach(dropdown => {
            // تأكد من أن Bootstrap يتعامل مع هذه العناصر
            if (!dropdown._bsDropdown) {
                new bootstrap.Dropdown(dropdown);
            }
        });
    }

    // إضافة مستمع لإغلاق القوائم المنسدلة عند النقر خارجها
    document.addEventListener('click', function(event) {
        const dropdownMenus = document.querySelectorAll('.dropdown-menu.show');
        if (dropdownMenus.length > 0) {
            dropdownMenus.forEach(menu => {
                if (!menu.contains(event.target) && !menu.previousElementSibling.contains(event.target)) {
                    const bsDropdown = bootstrap.Dropdown.getInstance(menu.previousElementSibling);
                    if (bsDropdown) {
                        bsDropdown.hide();
                    } else {
                        menu.classList.remove('show');
                    }
                }
            });
        }
    });
});
