from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.utils.translation import gettext as _
from django.db.models import Count

from .barcode_models import Barcode, BarcodeType, BarcodeLog, BarcodeSettings

@login_required
def barcode_dashboard(request):
    """
    عرض لوحة تحكم الباركود المتكاملة
    تعرض إحصائيات وميزات إدارة الباركود في مكان واحد
    """
    # إحصائيات الباركود
    total_barcodes = Barcode.objects.count()
    barcode_types_count = BarcodeType.objects.count()
    barcodes_printed = BarcodeLog.objects.filter(action='print').count()
    barcodes_scanned = BarcodeLog.objects.filter(action='scan').count()
    
    # الأنشطة الأخيرة
    recent_logs = BarcodeLog.objects.all().order_by('-created_at')[:10]
    
    context = {
        'title': _('لوحة تحكم الباركود'),
        'total_barcodes': total_barcodes,
        'barcode_types_count': barcode_types_count,
        'barcodes_printed': barcodes_printed,
        'barcodes_scanned': barcodes_scanned,
        'recent_logs': recent_logs,
    }
    
    return render(request, 'inventory/barcode_dashboard.html', context)