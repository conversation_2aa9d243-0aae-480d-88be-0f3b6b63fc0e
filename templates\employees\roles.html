{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/employees.css' %}">
{% endblock %}

{% block title %}{% trans "إدارة الأقسام والمناصب" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "إدارة الأقسام والمناصب" %}</h1>
        <a href="{% url 'employees:index' %}" class="btn btn-secondary">
            <i class="fas fa-users"></i> {% trans "الموظفين" %}
        </a>
    </div>

    <div class="row">
        <!-- Departments Section -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "الأقسام" %}</h6>
                    <button class="btn btn-sm btn-primary" data-toggle="modal" data-target="#addDepartmentModal">
                        <i class="fas fa-plus"></i> {% trans "إضافة قسم" %}
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="departmentsTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>{% trans "الاسم" %}</th>
                                    <th>{% trans "الوصف" %}</th>
                                    <th>{% trans "عدد المناصب" %}</th>
                                    <th>{% trans "الإجراءات" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for department in departments %}
                                <tr>
                                    <td>{{ department.name }}</td>
                                    <td>{{ department.description|default:"-"|truncatechars:50 }}</td>
                                    <td>{{ department.positions.count }}</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-sm btn-primary edit-department" data-id="{{ department.id }}" data-name="{{ department.name }}" data-description="{{ department.description|default:'' }}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger delete-department" data-id="{{ department.id }}" data-name="{{ department.name }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center">{% trans "لا توجد أقسام" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Positions Section -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "المناصب" %}</h6>
                    <button class="btn btn-sm btn-primary" data-toggle="modal" data-target="#addPositionModal">
                        <i class="fas fa-plus"></i> {% trans "إضافة منصب" %}
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="positionsTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>{% trans "الاسم" %}</th>
                                    <th>{% trans "القسم" %}</th>
                                    <th>{% trans "الوصف" %}</th>
                                    <th>{% trans "الإجراءات" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for position in positions %}
                                <tr>
                                    <td>{{ position.name }}</td>
                                    <td>{{ position.department.name }}</td>
                                    <td>{{ position.description|default:"-"|truncatechars:50 }}</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-sm btn-primary edit-position" data-id="{{ position.id }}" data-name="{{ position.name }}" data-department="{{ position.department.id }}" data-description="{{ position.description|default:'' }}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger delete-position" data-id="{{ position.id }}" data-name="{{ position.name }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center">{% trans "لا توجد مناصب" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Department Modal -->
<div class="modal fade" id="addDepartmentModal" tabindex="-1" role="dialog" aria-labelledby="addDepartmentModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addDepartmentModalLabel">{% trans "إضافة قسم جديد" %}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="addDepartmentForm">
                    {% csrf_token %}
                    <div class="form-group">
                        <label for="department_name">{% trans "اسم القسم" %} *</label>
                        <input type="text" class="form-control" id="department_name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="department_description">{% trans "الوصف" %}</label>
                        <textarea class="form-control" id="department_description" name="description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-primary" id="saveDepartment">{% trans "حفظ" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Department Modal -->
<div class="modal fade" id="editDepartmentModal" tabindex="-1" role="dialog" aria-labelledby="editDepartmentModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editDepartmentModalLabel">{% trans "تعديل القسم" %}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="editDepartmentForm">
                    {% csrf_token %}
                    <input type="hidden" id="edit_department_id" name="id">
                    <div class="form-group">
                        <label for="edit_department_name">{% trans "اسم القسم" %} *</label>
                        <input type="text" class="form-control" id="edit_department_name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="edit_department_description">{% trans "الوصف" %}</label>
                        <textarea class="form-control" id="edit_department_description" name="description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-primary" id="updateDepartment">{% trans "تحديث" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Position Modal -->
<div class="modal fade" id="addPositionModal" tabindex="-1" role="dialog" aria-labelledby="addPositionModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addPositionModalLabel">{% trans "إضافة منصب جديد" %}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="addPositionForm">
                    {% csrf_token %}
                    <div class="form-group">
                        <label for="position_name">{% trans "اسم المنصب" %} *</label>
                        <input type="text" class="form-control" id="position_name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="position_department">{% trans "القسم" %} *</label>
                        <select class="form-control" id="position_department" name="department" required>
                            <option value="">{% trans "اختر القسم" %}</option>
                            {% for department in departments %}
                            <option value="{{ department.id }}">{{ department.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="position_description">{% trans "الوصف" %}</label>
                        <textarea class="form-control" id="position_description" name="description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-primary" id="savePosition">{% trans "حفظ" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Position Modal -->
<div class="modal fade" id="editPositionModal" tabindex="-1" role="dialog" aria-labelledby="editPositionModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editPositionModalLabel">{% trans "تعديل المنصب" %}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="editPositionForm">
                    {% csrf_token %}
                    <input type="hidden" id="edit_position_id" name="id">
                    <div class="form-group">
                        <label for="edit_position_name">{% trans "اسم المنصب" %} *</label>
                        <input type="text" class="form-control" id="edit_position_name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="edit_position_department">{% trans "القسم" %} *</label>
                        <select class="form-control" id="edit_position_department" name="department" required>
                            <option value="">{% trans "اختر القسم" %}</option>
                            {% for department in departments %}
                            <option value="{{ department.id }}">{{ department.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="edit_position_description">{% trans "الوصف" %}</label>
                        <textarea class="form-control" id="edit_position_description" name="description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-primary" id="updatePosition">{% trans "تحديث" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Department Confirmation Modal -->
<div class="modal fade" id="deleteDepartmentModal" tabindex="-1" role="dialog" aria-labelledby="deleteDepartmentModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteDepartmentModalLabel">{% trans "تأكيد الحذف" %}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>{% trans "هل أنت متأكد من حذف القسم" %}: <strong id="delete_department_name"></strong>؟</p>
                <p class="text-danger">{% trans "سيتم حذف جميع المناصب المرتبطة بهذا القسم أيضًا." %}</p>
                <form id="deleteDepartmentForm">
                    {% csrf_token %}
                    <input type="hidden" id="delete_department_id" name="id">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteDepartment">{% trans "حذف" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Position Confirmation Modal -->
<div class="modal fade" id="deletePositionModal" tabindex="-1" role="dialog" aria-labelledby="deletePositionModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deletePositionModalLabel">{% trans "تأكيد الحذف" %}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>{% trans "هل أنت متأكد من حذف المنصب" %}: <strong id="delete_position_name"></strong>؟</p>
                <form id="deletePositionForm">
                    {% csrf_token %}
                    <input type="hidden" id="delete_position_id" name="id">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-danger" id="confirmDeletePosition">{% trans "حذف" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize DataTables
        $('#departmentsTable').DataTable({
            "language": {
                "url": "{% static 'js/dataTables.arabic.json' %}"
            },
            "order": [[0, "asc"]]
        });

        $('#positionsTable').DataTable({
            "language": {
                "url": "{% static 'js/dataTables.arabic.json' %}"
            },
            "order": [[0, "asc"]]
        });

        // Department CRUD operations
        $('#saveDepartment').click(function() {
            // Here you would submit the form via AJAX
            alert('سيتم حفظ القسم الجديد');
            $('#addDepartmentModal').modal('hide');
        });

        $('.edit-department').click(function() {
            var id = $(this).data('id');
            var name = $(this).data('name');
            var description = $(this).data('description');

            $('#edit_department_id').val(id);
            $('#edit_department_name').val(name);
            $('#edit_department_description').val(description);

            $('#editDepartmentModal').modal('show');
        });

        $('#updateDepartment').click(function() {
            // Here you would submit the form via AJAX
            alert('سيتم تحديث القسم');
            $('#editDepartmentModal').modal('hide');
        });

        $('.delete-department').click(function() {
            var id = $(this).data('id');
            var name = $(this).data('name');

            $('#delete_department_id').val(id);
            $('#delete_department_name').text(name);

            $('#deleteDepartmentModal').modal('show');
        });

        $('#confirmDeleteDepartment').click(function() {
            // Here you would submit the form via AJAX
            alert('سيتم حذف القسم');
            $('#deleteDepartmentModal').modal('hide');
        });

        // Position CRUD operations
        $('#savePosition').click(function() {
            // Here you would submit the form via AJAX
            alert('سيتم حفظ المنصب الجديد');
            $('#addPositionModal').modal('hide');
        });

        $('.edit-position').click(function() {
            var id = $(this).data('id');
            var name = $(this).data('name');
            var department = $(this).data('department');
            var description = $(this).data('description');

            $('#edit_position_id').val(id);
            $('#edit_position_name').val(name);
            $('#edit_position_department').val(department);
            $('#edit_position_description').val(description);

            $('#editPositionModal').modal('show');
        });

        $('#updatePosition').click(function() {
            // Here you would submit the form via AJAX
            alert('سيتم تحديث المنصب');
            $('#editPositionModal').modal('hide');
        });

        $('.delete-position').click(function() {
            var id = $(this).data('id');
            var name = $(this).data('name');

            $('#delete_position_id').val(id);
            $('#delete_position_name').text(name);

            $('#deletePositionModal').modal('show');
        });

        $('#confirmDeletePosition').click(function() {
            // Here you would submit the form via AJAX
            alert('سيتم حذف المنصب');
            $('#deletePositionModal').modal('hide');
        });
    });
</script>
{% endblock %}
