{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "فئات الإيرادات" %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card shadow">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{% trans "فئات الإيرادات" %}</h5>
            <button type="button" class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                <i class="fas fa-plus"></i> {% trans "إضافة فئة جديدة" %}
            </button>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>{% trans "الاسم" %}</th>
                            <th>{% trans "الوصف" %}</th>
                            <th>{% trans "تاريخ الإنشاء" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for category in categories %}
                        <tr>
                            <td>{{ category.name }}</td>
                            <td>{{ category.description|default:"-" }}</td>
                            <td>{{ category.created_at|date:"Y-m-d" }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editCategoryModal{{ category.id }}">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteCategoryModal{{ category.id }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="4" class="text-center">{% trans "لا توجد فئات إيرادات مضافة بعد" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal: إضافة فئة جديدة -->
<div class="modal fade" id="addCategoryModal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{% url 'finance:add_income_category' %}">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title" id="addCategoryModalLabel">{% trans "إضافة فئة إيرادات جديدة" %}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">{% trans "اسم الفئة" %} <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">{% trans "الوصف" %}</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "إضافة" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modals: تعديل وحذف الفئات -->
{% for category in categories %}
<!-- Modal: تعديل الفئة -->
<div class="modal fade" id="editCategoryModal{{ category.id }}" tabindex="-1" aria-labelledby="editCategoryModalLabel{{ category.id }}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{% url 'finance:edit_income_category' category.id %}">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title" id="editCategoryModalLabel{{ category.id }}">{% trans "تعديل فئة الإيرادات" %}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name{{ category.id }}" class="form-label">{% trans "اسم الفئة" %} <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name{{ category.id }}" name="name" value="{{ category.name }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="description{{ category.id }}" class="form-label">{% trans "الوصف" %}</label>
                        <textarea class="form-control" id="description{{ category.id }}" name="description" rows="3">{{ category.description }}</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "حفظ التغييرات" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal: حذف الفئة -->
<div class="modal fade" id="deleteCategoryModal{{ category.id }}" tabindex="-1" aria-labelledby="deleteCategoryModalLabel{{ category.id }}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{% url 'finance:delete_income_category' category.id %}">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteCategoryModalLabel{{ category.id }}">{% trans "حذف فئة الإيرادات" %}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>{% trans "هل أنت متأكد من حذف فئة الإيرادات" %} <strong>{{ category.name }}</strong>؟</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i> {% trans "لا يمكن حذف الفئة إذا كانت مرتبطة بأي إيرادات." %}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-danger">{% trans "حذف" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endfor %}
{% endblock %}
