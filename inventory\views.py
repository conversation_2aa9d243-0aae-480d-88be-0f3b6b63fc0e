from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse, HttpResponse
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.db.models import Sum, F, Q
from django.core.paginator import Paginator
from django.views.decorators.csrf import csrf_exempt
import json
import csv
import io
import base64
import barcode
from barcode.writer import ImageWriter
from datetime import datetime
from io import BytesIO

# استيراد مكتبات ReportLab لإنشاء ملفات PDF
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, landscape, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.enums import TA_RIGHT, TA_CENTER

# تأكد من تثبيت حزمة xlsxwriter باستخدام الأمر:
# pip install xlsxwriter

from .models import Product, Category, StorageLocation, ProductMovement
from .barcode_models import Barcode # إضافة استيراد الباركود
from sales.models import SaleItem
from purchases.models import PurchaseItem

# تعريف دالة معالجة النص العربي
def process_arabic_text(text):
    """
    معالجة النص العربي لضمان ظهوره بشكل صحيح في ملف PDF

    هذه الدالة تقوم بمعالجة النص العربي لضمان ظهوره بشكل صحيح في ملف PDF
    من خلال عكس ترتيب الأحرف وإضافة بعض التحسينات لضمان اتصال الحروف بشكل صحيح
    """
    if not text:
        return ""

    # تحويل النص إلى سلسلة نصية إذا لم يكن كذلك
    text = str(text)

    # إرجاع النص كما هو (ReportLab يدعم العربية مع الخطوط المناسبة)
    return text

@login_required
def index(request):
    # Get all products with related data
    products_qs = Product.objects.select_related('category', 'storage_location', 'supplier')

    # Get search query
    search_query = request.GET.get('search', '')
    if search_query:
        products_qs = products_qs.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(barcodes__barcode_number__icontains=search_query) # البحث باستخدام رقم الباركود
        ).distinct()
    else:
        products_qs = products_qs.all()

    products = products_qs

    # Calculate inventory statistics
    total_products = products.count()
    low_stock_count = products.filter(quantity__lte=F('min_quantity')).count()
    out_of_stock_count = products.filter(quantity=0).count()
    inventory_value = sum(product.quantity * product.purchase_price for product in products)

    # Get categories, storage locations and suppliers for filters
    categories = Category.objects.all()
    storage_locations = StorageLocation.objects.all()

    # Import Supplier model
    from purchases.models import Supplier
    suppliers = Supplier.objects.all()

    context = {
        'products': products,
        'search_query': search_query, # إضافة متغير البحث للسياق
        'categories': categories,
        'storage_locations': storage_locations,
        'suppliers': suppliers,
        'total_products': total_products,
        'low_stock_count': low_stock_count,
        'out_of_stock_count': out_of_stock_count,
        'inventory_value': inventory_value,
    }
    return render(request, 'inventory/index.html', context)

@login_required
def add_product(request):
    categories = Category.objects.all()
    storage_locations = StorageLocation.objects.all()

    # Import Supplier model
    from purchases.models import Supplier
    suppliers = Supplier.objects.all()

    if request.method == 'POST':
        # Get form data
        code = request.POST.get('code')
        name = request.POST.get('name')
        category_id = request.POST.get('category')
        supplier_id = request.POST.get('supplier')
        storage_location_id = request.POST.get('storage_location')
        description = request.POST.get('description')
        purchase_price = request.POST.get('purchase_price')
        selling_price = request.POST.get('selling_price')
        quantity = request.POST.get('quantity')
        min_quantity = request.POST.get('min_quantity')
        is_active = request.POST.get('is_active') == 'on'

        # Validate data
        if Product.objects.filter(code=code).exists():
            messages.error(request, _('كود المنتج موجود بالفعل'))
            return redirect('inventory:add_product')

        # Create product
        product = Product(
            code=code,
            name=name,
            category_id=category_id,
            supplier_id=supplier_id,
            storage_location_id=storage_location_id,
            description=description,
            purchase_price=purchase_price,
            selling_price=selling_price,
            quantity=quantity,
            min_quantity=min_quantity,
            is_active=is_active
        )

        # Handle image upload
        if 'image' in request.FILES:
            product.image = request.FILES['image']

        product.save()

        # Create initial stock movement if quantity > 0
        if int(quantity) > 0:
            ProductMovement.objects.create(
                product=product,
                movement_type='in',
                quantity=quantity,
                reference='Initial Stock',
                notes='Initial stock when creating product'
            )

        messages.success(request, _('تم إضافة المنتج بنجاح'))
        return redirect('inventory:index')

    context = {
        'categories': categories,
        'storage_locations': storage_locations,
        'suppliers': suppliers,
    }
    return render(request, 'inventory/add_product.html', context)

from .barcode_models import Barcode, BarcodeType # إضافة استيراد

@login_required
def edit_product(request, product_id):
    product = get_object_or_404(Product, id=product_id)
    categories = Category.objects.all()
    storage_locations = StorageLocation.objects.all()
    # جلب الباركود الأساسي للمنتج
    primary_barcode = Barcode.objects.filter(product=product, is_primary=True).first()

    # Import Supplier model
    from purchases.models import Supplier
    suppliers = Supplier.objects.all()

    if request.method == 'POST':
        # Get form data
        code = request.POST.get('code')
        barcode_number = request.POST.get('barcode') # تم تغيير الاسم ليعكس أنه رقم الباركود
        name = request.POST.get('name')
        category_id = request.POST.get('category')
        supplier_id = request.POST.get('supplier')
        storage_location_id = request.POST.get('storage_location')
        description = request.POST.get('description')
        purchase_price = request.POST.get('purchase_price')
        selling_price = request.POST.get('selling_price')
        min_quantity = request.POST.get('min_quantity')
        is_active = request.POST.get('is_active') == 'on'
        delete_image = request.POST.get('delete_image') == 'on'

        # Validate data
        if Product.objects.filter(code=code).exclude(id=product_id).exists():
            messages.error(request, _('كود المنتج موجود بالفعل'))
            return redirect('inventory:edit_product', product_id=product_id)

        # Update product
        product.code = code
        # لا نحدث product.barcode مباشرة هنا، سيتم التعامل معه أدناه
        product.name = name
        product.category_id = category_id
        product.supplier_id = supplier_id
        product.storage_location_id = storage_location_id
        product.description = description
        product.purchase_price = purchase_price
        product.selling_price = selling_price
        product.min_quantity = min_quantity
        product.is_active = is_active

        # Handle image upload or deletion
        if 'image' in request.FILES:
            product.image = request.FILES['image']
        elif delete_image and product.image:
            product.image.delete()

        product.save()

        # التعامل مع الباركود
        if barcode_number:
            # التحقق مما إذا كان الباركود موجودًا بالفعل لمنتج آخر
            existing_barcode_other_product = Barcode.objects.filter(barcode_number=barcode_number).exclude(product=product).first()
            if existing_barcode_other_product:
                messages.error(request, _('رقم الباركود هذا مستخدم بالفعل لمنتج آخر: {}').format(existing_barcode_other_product.product.name))
            else:
                barcode_type = BarcodeType.objects.filter(is_active=True).first() # استخدام أول نوع باركود نشط كافتراضي
                if not barcode_type:
                    # يمكنك إنشاء نوع باركود افتراضي هنا إذا لم يكن موجودًا أو توجيه المستخدم لإنشائه
                    default_barcode_type_name = 'EAN-13' # أو أي اسم افتراضي آخر
                    barcode_type, created = BarcodeType.objects.get_or_create(
                        code=default_barcode_type_name,
                        defaults={'name': default_barcode_type_name, 'is_active': True}
                    )
                    if created:
                        messages.info(request, _('تم إنشاء نوع باركود افتراضي: {}').format(default_barcode_type_name))

                if primary_barcode:
                    if primary_barcode.barcode_number != barcode_number:
                        primary_barcode.barcode_number = barcode_number
                        if barcode_type: # التأكد من وجود نوع باركود قبل التحديث
                           primary_barcode.barcode_type = barcode_type
                        primary_barcode.save()
                        messages.success(request, _('تم تحديث الباركود الأساسي للمنتج.'))
                elif barcode_type: # فقط إذا كان هناك نوع باركود متاح
                    # إنشاء باركود أساسي جديد إذا لم يكن موجودًا
                    Barcode.objects.create(
                        product=product,
                        barcode_type=barcode_type,
                        barcode_number=barcode_number,
                        is_primary=True,
                        created_by=request.user
                    )
                    messages.success(request, _('تم إنشاء باركود أساسي جديد للمنتج.'))
                else:
                    messages.warning(request, _('لا يمكن إنشاء أو تحديث الباركود بدون نوع باركود نشط.'))

        elif primary_barcode and not barcode_number: # إذا تم حذف الباركود من الحقل ولم يكن هناك رقم باركود جديد
            #  يمكنك اختيار حذف الباركود الأساسي أو جعله غير نشط
            # primary_barcode.delete()
            # messages.info(request, _('تم حذف الباركود الأساسي للمنتج إذا ترك الحقل فارغاً.'))
            pass # حاليًا لا يتم فعل شيء إذا تُرك الحقل فارغًا وكان هناك باركود أساسي

        messages.success(request, _('تم تعديل المنتج بنجاح'))
        return redirect('inventory:index')

    context = {
        'product': product,
        'categories': categories,
        'storage_locations': storage_locations,
        'suppliers': suppliers,
        'primary_barcode': primary_barcode, # تمرير الباركود الأساسي إلى القالب
    }
    return render(request, 'inventory/edit_product.html', context)

@login_required
def delete_product(request, product_id):
    product = get_object_or_404(Product, id=product_id)

    # Check for related records
    related_sales = SaleItem.objects.filter(product=product).count()
    related_purchases = PurchaseItem.objects.filter(product=product).count()

    if request.method == 'POST':
        # Delete product image if exists
        if product.image:
            product.image.delete()

        # Delete product
        product.delete()
        messages.success(request, _('تم حذف المنتج بنجاح'))
        return redirect('inventory:index')

    context = {
        'product': product,
        'related_sales': related_sales,
        'related_purchases': related_purchases,
    }
    return render(request, 'inventory/delete_product.html', context)

@login_required
def categories(request):
    # Get search query
    search_query = request.GET.get('search', '')

    # Filter categories based on search query
    if search_query:
        categories = Category.objects.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        ).order_by('name')
    else:
        categories = Category.objects.all().order_by('name')

    # Count total categories
    total_categories = categories.count()

    context = {
        'categories': categories,
        'search_query': search_query,
        'total_categories': total_categories,
    }
    return render(request, 'inventory/categories.html', context)

@login_required
def product_detail(request, product_id):
    product = get_object_or_404(Product, id=product_id)

    # Get product movements ordered by date
    product_movements = product.movements.all().order_by('-created_at')

    # Generate barcode
    barcode_image = generate_barcode(product.code)

    # Get all movements for chart
    all_product_movements = product.movements.all().order_by('created_at')

    # Calculate stock statistics
    stock_stats = calculate_stock_statistics(product)

    context = {
        'product': product,
        'product_movements_for_chart': all_product_movements,
        'all_product_movements': all_product_movements,
        'barcode_image': barcode_image,
        'avg_stock': stock_stats['avg_stock'],
        'min_stock': stock_stats['min_stock'],
        'max_stock': stock_stats['max_stock'],
        'stock_days_remaining': stock_stats['days_remaining'],
    }
    return render(request, 'inventory/product_detail.html', context)

@login_required
def product_details_json(request, product_id):
    """الحصول على تفاصيل المنتج بتنسيق JSON"""
    product = get_object_or_404(Product, id=product_id)

    # تحضير بيانات المنتج
    product_data = {
        'id': product.id,
        'code': product.code,
        'name': product.name,
        'category_name': product.category.name if product.category else '-',
        'supplier_name': product.supplier.name if product.supplier else '-',
        'storage_location': product.storage_location.name if product.storage_location else '-',
        'description': product.description,
        'purchase_price': float(product.purchase_price),
        'selling_price': float(product.selling_price),
        'quantity': product.quantity,
        'min_quantity': product.min_quantity,
        'is_active': product.is_active,
        'image_url': product.image.url if product.image else None,
        'created_at': product.created_at.strftime('%Y-%m-%d'),
        'updated_at': product.updated_at.strftime('%Y-%m-%d'),
    }

    return JsonResponse(product_data)

# Helper function to calculate stock statistics
def calculate_stock_statistics(product):
    # Get all movements
    movements = product.movements.all().order_by('created_at')

    if not movements.exists():
        return {
            'avg_stock': product.quantity,
            'min_stock': product.quantity,
            'max_stock': product.quantity,
            'days_remaining': 30  # Default value
        }

    # Calculate stock levels over time
    stock_levels = []
    current_stock = 0

    for movement in movements:
        if movement.movement_type == 'in':
            current_stock += movement.quantity
        elif movement.movement_type == 'out':
            current_stock -= movement.quantity

        stock_levels.append(current_stock)

    # Calculate statistics
    avg_stock = sum(stock_levels) / len(stock_levels) if stock_levels else 0
    min_stock = min(stock_levels) if stock_levels else 0
    max_stock = max(stock_levels) if stock_levels else 0

    # Calculate days until stock runs out
    days_remaining = 30  # Default value

    if len(stock_levels) >= 2 and movements.count() >= 2:
        # Get first and last movement dates
        first_date = movements.first().created_at
        last_date = movements.last().created_at

        # Calculate days difference
        days_diff = (last_date - first_date).days

        if days_diff > 0:
            # Calculate daily consumption
            stock_change = stock_levels[-1] - stock_levels[0]
            daily_consumption = stock_change / days_diff

            if daily_consumption < 0:  # Only if stock is decreasing
                # Calculate days until stock runs out
                days_remaining = int(product.quantity / abs(daily_consumption))

    return {
        'avg_stock': avg_stock,
        'min_stock': min_stock,
        'max_stock': max_stock,
        'days_remaining': days_remaining
    }

@login_required
def print_barcode(request, product_id):
    product = get_object_or_404(Product, id=product_id)

    # Get parameters
    count = int(request.GET.get('count', 1))
    size = request.GET.get('size', 'medium')
    include_price = request.GET.get('include_price', 'true') == 'true'

    # Generate barcode
    barcode_image = generate_barcode(product.code)

    context = {
        'product': product,
        'barcode_image': barcode_image,
        'count': count,
        'count_range': range(count),
        'size': size,
        'include_price': include_price,
    }
    return render(request, 'inventory/print_barcode.html', context)

@login_required
def add_category(request):
    if request.method == 'POST':
        name = request.POST.get('name')
        description = request.POST.get('description')

        if Category.objects.filter(name=name).exists():
            messages.error(request, _('اسم الفئة موجود بالفعل'))
            return redirect('inventory:categories')

        Category.objects.create(name=name, description=description)
        messages.success(request, _('تم إضافة الفئة بنجاح'))

    return redirect('inventory:categories')

@login_required
def edit_category(request, category_id):
    category = get_object_or_404(Category, id=category_id)

    if request.method == 'POST':
        name = request.POST.get('name')
        description = request.POST.get('description')

        if Category.objects.filter(name=name).exclude(id=category_id).exists():
            messages.error(request, _('اسم الفئة موجود بالفعل'))
            return redirect('inventory:categories')

        category.name = name
        category.description = description
        category.save()

        messages.success(request, _('تم تحديث الفئة بنجاح'))

    return redirect('inventory:categories')

@login_required
def delete_category(request, category_id):
    category = get_object_or_404(Category, id=category_id)

    if request.method == 'POST':
        # Check if category has products
        if category.products.exists():
            # Option: Delete all products in this category
            for product in category.products.all():
                if product.image:
                    product.image.delete()

            # Delete the category and its products
            category.delete()
            messages.warning(request, _('تم حذف الفئة وجميع المنتجات المرتبطة بها'))
        else:
            # Delete the empty category
            category.delete()
            messages.success(request, _('تم حذف الفئة بنجاح'))

    return redirect('inventory:categories')

@login_required
@csrf_exempt
def add_category_ajax(request):
    if request.method == 'POST':
        name = request.POST.get('name')
        description = request.POST.get('description')

        if Category.objects.filter(name=name).exists():
            return JsonResponse({'success': False, 'error': _('اسم الفئة موجود بالفعل')})

        category = Category.objects.create(name=name, description=description)

        return JsonResponse({
            'success': True,
            'category': {
                'id': category.id,
                'name': category.name
            }
        })

    return JsonResponse({'success': False, 'error': _('طريقة طلب غير صالحة')})

@login_required
@csrf_exempt
def add_storage_location_ajax(request):
    if request.method == 'POST':
        name = request.POST.get('name')
        description = request.POST.get('description')

        if StorageLocation.objects.filter(name=name).exists():
            return JsonResponse({'success': False, 'error': _('اسم مكان التخزين موجود بالفعل')})

        storage_location = StorageLocation.objects.create(name=name, description=description)

        return JsonResponse({
            'success': True,
            'storage_location': {
                'id': storage_location.id,
                'name': storage_location.name
            }
        })

    return JsonResponse({'success': False, 'error': _('طريقة طلب غير صالحة')})

@login_required
def storage_locations(request):
    # Get search query
    search_query = request.GET.get('search', '')

    # Filter locations based on search query
    if search_query:
        locations = StorageLocation.objects.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        ).order_by('name')
    else:
        locations = StorageLocation.objects.all().order_by('name')

    # Count total locations
    total_locations = locations.count()

    context = {
        'locations': locations,
        'search_query': search_query,
        'total_locations': total_locations,
    }
    return render(request, 'inventory/storage_locations.html', context)

@login_required
def add_storage_location(request):
    if request.method == 'POST':
        name = request.POST.get('name')
        description = request.POST.get('description')

        if StorageLocation.objects.filter(name=name).exists():
            messages.error(request, _('اسم مكان التخزين موجود بالفعل'))
            return redirect('inventory:storage_locations')

        StorageLocation.objects.create(name=name, description=description)
        messages.success(request, _('تم إضافة مكان التخزين بنجاح'))

    return redirect('inventory:storage_locations')

@login_required
def edit_storage_location(request, location_id):
    location = get_object_or_404(StorageLocation, id=location_id)

    if request.method == 'POST':
        name = request.POST.get('name')
        description = request.POST.get('description')

        if StorageLocation.objects.filter(name=name).exclude(id=location_id).exists():
            messages.error(request, _('اسم مكان التخزين موجود بالفعل'))
            return redirect('inventory:storage_locations')

        location.name = name
        location.description = description
        location.save()

        messages.success(request, _('تم تحديث مكان التخزين بنجاح'))

    return redirect('inventory:storage_locations')

@login_required
def delete_storage_location(request, location_id):
    location = get_object_or_404(StorageLocation, id=location_id)

    if request.method == 'POST':
        # Check if location has products
        if location.products.exists():
            # Option 1: Prevent deletion
            messages.error(request, _('لا يمكن حذف مكان التخزين لأنه يحتوي على منتجات'))
            # Option 2: Set products' location to None
            # location.products.update(storage_location=None)
            # location.delete()
            # messages.success(request, _('تم حذف مكان التخزين وتحديث المنتجات المرتبطة به'))
        else:
            # Delete the empty location
            location.delete()
            messages.success(request, _('تم حذف مكان التخزين بنجاح'))

    return redirect('inventory:storage_locations')

@login_required
def add_movement(request, product_id):
    product = get_object_or_404(Product, id=product_id)

    if request.method == 'POST':
        movement_type = request.POST.get('movement_type')
        quantity = int(request.POST.get('quantity'))
        reference = request.POST.get('reference')
        notes = request.POST.get('notes')

        # Validate quantity
        if movement_type == 'out' and quantity > product.quantity:
            messages.error(request, _('الكمية المطلوبة أكبر من الكمية المتوفرة في المخزون'))
            return redirect('inventory:product_detail', product_id=product_id)

        # Create movement
        movement = ProductMovement.objects.create(
            product=product,
            movement_type=movement_type,
            quantity=quantity,
            reference=reference,
            notes=notes
        )

        # Update product quantity
        if movement_type == 'in':
            product.quantity += quantity
        elif movement_type == 'out':
            product.quantity -= quantity
        # For 'adjustment', the quantity is set directly in the form

        product.save()

        messages.success(request, _('تم إضافة حركة المخزون بنجاح'))

    return redirect('inventory:product_detail', product_id=product_id)

@login_required
@csrf_exempt
def stock_movement_ajax(request):
    if request.method == 'POST':
        try:
            # Try to parse as JSON first
            try:
                data = json.loads(request.body)
            except:
                # If not JSON, use POST data
                data = request.POST

            product_id = data.get('product_id')
            movement_type = data.get('movement_type')
            quantity = int(data.get('quantity'))
            reference = data.get('reference')
            notes = data.get('notes')

            product = get_object_or_404(Product, id=product_id)

            # Validate quantity
            if movement_type == 'out' and quantity > product.quantity:
                return JsonResponse({'success': False, 'error': _('الكمية المطلوبة أكبر من الكمية المتوفرة في المخزون')})

            # Create movement
            movement = ProductMovement.objects.create(
                product=product,
                movement_type=movement_type,
                quantity=quantity,
                reference=reference,
                notes=notes
            )

            # Update product quantity
            if movement_type == 'in':
                product.quantity += quantity
            elif movement_type == 'out':
                product.quantity -= quantity
            # For 'adjustment', the quantity is set directly

            product.save()

            return JsonResponse({'success': True})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': _('طريقة طلب غير صالحة')})

@login_required
def get_product_movements(request, product_id):
    product = get_object_or_404(Product, id=product_id)
    movements = product.movements.all().order_by('-created_at')

    movement_list = []
    for movement in movements:
        movement_list.append({
            'id': movement.id,
            'movement_type': movement.movement_type,
            'movement_type_display': movement.get_movement_type_display(),
            'quantity': movement.quantity,
            'reference': movement.reference,
            'notes': movement.notes,
            'created_at': movement.created_at.strftime('%Y-%m-%d %H:%M')
        })

    return JsonResponse({'success': True, 'movements': movement_list})

@login_required
def search_products(request):
    term = request.GET.get('term', '')
    products = Product.objects.select_related('category', 'storage_location', 'supplier').filter(
        Q(name__icontains=term) | Q(code__icontains=term) | Q(description__icontains=term),
        is_active=True
    )[:10]

    results = []
    for product in products:
        results.append({
            'id': product.id,
            'name': product.name,
            'code': product.code,
            'price': float(product.selling_price),
            'quantity': product.quantity,
            'category': product.category.name,
            'supplier': product.supplier.name if product.supplier else '',
            'storage_location': product.storage_location.name if product.storage_location else '',
            'description': product.description[:50] + '...' if product.description and len(product.description) > 50 else product.description
        })

    return JsonResponse(results, safe=False)

@login_required
def export_products(request):
    # تحديد نوع التصدير (Excel أو PDF أو CSV)
    export_type = request.GET.get('type', 'excel')

    if export_type == 'pdf':
        return export_products_pdf(request)
    elif export_type == 'csv':
        return export_products_csv(request)
    else:  # excel هو الخيار الافتراضي
        # استخدام مكتبة xlsxwriter لإنشاء ملف Excel
        try:
            import xlsxwriter
        except ImportError:
            # إذا لم تكن المكتبة مثبتة، استخدم CSV كحل بديل
            return export_products_csv(request)

        # إنشاء استجابة بايت لملف Excel
        output = BytesIO()

        # إنشاء مصنف عمل Excel مع إعدادات لدعم اللغة العربية
        workbook = xlsxwriter.Workbook(output, {
            'in_memory': True,
            'strings_to_unicode': True,  # تحويل السلاسل النصية إلى يونيكود
            'encoding': 'utf-8'  # استخدام ترميز UTF-8
        })

        # إضافة ورقة عمل باسم عربي
        worksheet = workbook.add_worksheet('المنتجات')

        # تعيين اتجاه الورقة من اليمين إلى اليسار لدعم اللغة العربية
        worksheet.right_to_left()

        # تنسيق العناوين
        header_format = workbook.add_format({
            'bold': True,
            'align': 'center',
            'valign': 'vcenter',
            'fg_color': '#D9EAD3',
            'border': 1,
            'font_name': 'Arial',  # استخدام خط يدعم اللغة العربية
            'font_size': 12
        })

        # كتابة صف العناوين
        headers = [
            'الكود', 'الاسم', 'الفئة', 'المورد', 'موقع التخزين', 'سعر الشراء',
            'سعر البيع', 'الكمية', 'الحد الأدنى للكمية', 'الوصف', 'نشط'
        ]

        for col_num, header in enumerate(headers):
            worksheet.write(0, col_num, header, header_format)
            # تعيين عرض العمود مع مراعاة النص العربي (زيادة العرض)
            worksheet.set_column(col_num, col_num, 20)

        # تنسيق البيانات
        data_format = workbook.add_format({
            'align': 'right',  # محاذاة النص العربي إلى اليمين
            'border': 1,
            'font_name': 'Arial',  # استخدام خط يدعم اللغة العربية
            'font_size': 11
        })

        # تنسيق للأرقام
        number_format = workbook.add_format({
            'align': 'center',
            'border': 1,
            'font_name': 'Arial',
            'font_size': 11,
            'num_format': '#,##0.00'
        })

        # تنسيق للأعداد الصحيحة
        integer_format = workbook.add_format({
            'align': 'center',
            'border': 1,
            'font_name': 'Arial',
            'font_size': 11
        })

        # كتابة صفوف البيانات
        products = Product.objects.select_related('category', 'storage_location', 'supplier').all()
        for row_num, product in enumerate(products, 1):
            worksheet.write(row_num, 0, product.code, data_format)
            worksheet.write(row_num, 1, product.name, data_format)
            worksheet.write(row_num, 2, product.category.name if product.category else '', data_format)
            worksheet.write(row_num, 3, product.supplier.name if product.supplier else '', data_format)
            worksheet.write(row_num, 4, product.storage_location.name if product.storage_location else '', data_format)
            worksheet.write_number(row_num, 5, float(product.purchase_price), number_format)
            worksheet.write_number(row_num, 6, float(product.selling_price), number_format)
            worksheet.write_number(row_num, 7, product.quantity, integer_format)
            worksheet.write_number(row_num, 8, product.min_quantity, integer_format)
            worksheet.write(row_num, 9, product.description, data_format)
            worksheet.write(row_num, 10, 'نعم' if product.is_active else 'لا', data_format)

        # إغلاق المصنف
        workbook.close()

        # إعداد الاستجابة
        output.seek(0)
        response = HttpResponse(
            output.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="products_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        return response

@login_required
def export_products_pdf(request):
    """
    تصدير المنتجات إلى ملف PDF مع دعم كامل للغة العربية

    هذه الوظيفة تقوم بإنشاء ملف PDF يحتوي على قائمة المنتجات في المخزون
    مع دعم كامل للغة العربية من خلال:
    - تسجيل الخطوط العربية المناسبة
    - معالجة النص العربي لضمان ظهوره بشكل صحيح
    - ضبط اتجاه النص من اليمين إلى اليسار
    - تحسين اتصال الحروف العربية
    - تنسيق الجدول بشكل مناسب للغة العربية

    يمكن تطبيق فلاتر على التقرير من خلال المعلمات التالية في الطلب:
    - category: معرف الفئة لتصفية المنتجات حسب الفئة
    - supplier: معرف المورد لتصفية المنتجات حسب المورد
    - status: حالة المخزون (low للمنخفض، out للنافد)
    """
    # تم نقل تعريف دالة process_arabic_text إلى بداية الملف
    # إنشاء استجابة HTTP مع نوع محتوى مناسب
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="products_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf"'

    # تسجيل الخط العربي
    try:
        import os
        # تحديد مسار مجلد الخطوط
        fonts_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'static', 'fonts')

        # إنشاء مجلد الخطوط إذا لم يكن موجودًا
        if not os.path.exists(fonts_dir):
            os.makedirs(fonts_dir)

        # قائمة بالخطوط العربية المفضلة للاستخدام
        arabic_font_files = [
            ('Arabic', 'NotoSansArabic-Regular.ttf', 'NotoSansArabic-Bold.ttf'),
            ('Amiri', 'Amiri-Regular.ttf', 'Amiri-Bold.ttf'),
            ('Scheherazade', 'ScheherazadeNew-Regular.ttf', 'ScheherazadeNew-Bold.ttf'),
            ('Dubai', 'Dubai-Regular.ttf', 'Dubai-Bold.ttf')
        ]

        # البحث عن خطوط عربية في مجلد النظام
        system_fonts_dir = os.path.join(os.environ.get('SystemRoot', 'C:\\Windows'), 'Fonts')
        system_arabic_fonts = [
            ('Tahoma', 'tahoma.ttf', 'tahomabd.ttf'),
            ('Arial', 'arial.ttf', 'arialbd.ttf'),
            ('Times', 'times.ttf', 'timesbd.ttf'),
            ('Segoe', 'segoeui.ttf', 'segoeuib.ttf'),
            ('Simplified', 'simpo.ttf', 'simpbdo.ttf')
        ]

        # محاولة تسجيل خط عربي من مجلد المشروع
        font_loaded = False
        arabic_font_name = 'Arabic'
        arabic_bold_font_name = 'Arabic-Bold'

        # البحث في مجلد المشروع أولاً
        for font_name, regular_file, bold_file in arabic_font_files:
            regular_path = os.path.join(fonts_dir, regular_file)
            bold_path = os.path.join(fonts_dir, bold_file)

            if os.path.exists(regular_path):
                # تسجيل الخط العربي العادي
                pdfmetrics.registerFont(TTFont(arabic_font_name, regular_path))

                # تسجيل الخط العربي الغامق
                if os.path.exists(bold_path):
                    pdfmetrics.registerFont(TTFont(arabic_bold_font_name, bold_path))
                else:
                    pdfmetrics.registerFont(TTFont(arabic_bold_font_name, regular_path))

                print(f"تم تحميل الخط: {font_name}")
                font_loaded = True
                break

        # إذا لم يتم العثور على خط في مجلد المشروع، ابحث في مجلد النظام
        if not font_loaded:
            for font_name, regular_file, bold_file in system_arabic_fonts:
                regular_path = os.path.join(system_fonts_dir, regular_file)
                bold_path = os.path.join(system_fonts_dir, bold_file)

                if os.path.exists(regular_path):
                    # تسجيل الخط العربي العادي
                    pdfmetrics.registerFont(TTFont(arabic_font_name, regular_path))

                    # تسجيل الخط العربي الغامق
                    if os.path.exists(bold_path):
                        pdfmetrics.registerFont(TTFont(arabic_bold_font_name, bold_path))
                    else:
                        pdfmetrics.registerFont(TTFont(arabic_bold_font_name, regular_path))

                    print(f"تم تحميل الخط من النظام: {font_name}")
                    font_loaded = True
                    break

        if not font_loaded:
            # محاولة استخدام خط Tahoma كخيار أخير لأنه يدعم العربية بشكل جيد
            tahoma_path = os.path.join(system_fonts_dir, 'tahoma.ttf')
            tahoma_bold_path = os.path.join(system_fonts_dir, 'tahomabd.ttf')

            if os.path.exists(tahoma_path):
                pdfmetrics.registerFont(TTFont(arabic_font_name, tahoma_path))
                if os.path.exists(tahoma_bold_path):
                    pdfmetrics.registerFont(TTFont(arabic_bold_font_name, tahoma_bold_path))
                else:
                    pdfmetrics.registerFont(TTFont(arabic_bold_font_name, tahoma_path))
                print("تم تحميل خط Tahoma كخيار بديل")
                font_loaded = True
            else:
                print("لم يتم العثور على أي خط عربي، سيتم استخدام الخط الافتراضي")
    except Exception as e:
        print(f"خطأ في تحميل الخط العربي: {e}")

    # تم نقل تعريف دالة process_arabic_text إلى بداية الملف

    # إنشاء أنماط النصوص
    styles = getSampleStyleSheet()

    # تعريف الخط العربي المستخدم
    arabic_font = 'Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'
    arabic_bold_font = 'Arabic-Bold' if 'Arabic-Bold' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'

    # إنشاء نمط للنصوص العربية
    arabic_style = ParagraphStyle(
        'ArabicStyle',
        parent=styles['Normal'],
        alignment=TA_RIGHT,
        fontName=arabic_font,
        fontSize=12,
        leading=14,
    )

    # إنشاء ملف PDF مع ضبط الهوامش لدعم النص العربي بشكل أفضل
    buffer = BytesIO()
    # استخدام اتجاه أفقي للصفحة لإتاحة مساحة أكبر للجدول
    doc = SimpleDocTemplate(buffer, pagesize=landscape(A4), rightMargin=50, leftMargin=50, topMargin=30, bottomMargin=30)

    # قائمة العناصر التي سيتم إضافتها إلى المستند
    elements = []

    # إضافة معلومات المستند (ملاحظة: SimpleDocTemplate لا يدعم setAuthor وما شابه)
    # يمكن إضافة معلومات المستند في نص الترويسة بدلاً من ذلك
    date_text = Paragraph(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d')}\nنظام إدارة المخزون", arabic_style)
    elements.append(date_text)
    elements.append(Spacer(1, 10))

    # إنشاء نمط للخلايا
    cell_style = ParagraphStyle(
        'CellStyle',
        parent=arabic_style,
        fontSize=10,
        leading=12,
        alignment=TA_RIGHT,
        fontName=arabic_font
    )

    # إنشاء أنماط مختلفة لحالات المخزون
    status_low_style = ParagraphStyle(
        'StatusLowStyle',
        parent=cell_style,
        textColor=colors.orange,
        fontName=arabic_bold_font
    )

    status_out_style = ParagraphStyle(
        'StatusOutStyle',
        parent=cell_style,
        textColor=colors.red,
        fontName=arabic_bold_font
    )

    status_ok_style = ParagraphStyle(
        'StatusOkStyle',
        parent=cell_style,
        textColor=colors.green,
        fontName=arabic_bold_font
    )

    # تم نقل تعريف دالة process_arabic_text إلى بداية الملف

    # إنشاء نمط للعنوان
    title_style = ParagraphStyle(
        'TitleStyle',
        parent=styles['Title'],
        alignment=TA_RIGHT,
        fontName=arabic_bold_font,
        fontSize=18,
        leading=22,
        textColor=colors.black,
        spaceAfter=10
    )

    # إضافة عنوان للتقرير
    title = Paragraph(process_arabic_text(_("تقرير المنتجات في المخزون")), title_style)
    elements.append(title)
    elements.append(Spacer(1, 20))

    # إضافة تاريخ التقرير
    date_text = Paragraph(process_arabic_text(_('تاريخ التقرير: ') + datetime.now().strftime('%Y-%m-%d %H:%M')), arabic_style)
    elements.append(date_text)
    elements.append(Spacer(1, 20))

    # إضافة تاريخ التقرير
    date_text = Paragraph(_('تاريخ التقرير: ') + datetime.now().strftime('%Y-%m-%d %H:%M'), arabic_style)
    elements.append(date_text)
    elements.append(Spacer(1, 20))

    # الحصول على بيانات المنتجات
    products = Product.objects.select_related('category', 'storage_location', 'supplier').all()

    # تطبيق الفلاتر إذا كانت موجودة في الطلب
    category_id = request.GET.get('category')
    supplier_id = request.GET.get('supplier')
    stock_status = request.GET.get('status')

    if category_id:
        products = products.filter(category_id=category_id)

    if supplier_id:
        products = products.filter(supplier_id=supplier_id)

    if stock_status == 'low':
        products = products.filter(quantity__lte=F('min_quantity'), quantity__gt=0)
    elif stock_status == 'out':
        products = products.filter(quantity=0)

    # إنشاء نمط للعناوين مع تحسين المظهر
    header_style = ParagraphStyle(
        'HeaderStyle',
        parent=arabic_style,
        fontSize=12,
        leading=14,
        alignment=TA_CENTER,  # تغيير المحاذاة إلى المركز لتحسين المظهر
        fontName='Arabic-Bold' if 'Arabic-Bold' in pdfmetrics.getRegisteredFontNames() else 'Arabic',
        textColor=colors.white,  # لون النص أبيض ليظهر بشكل واضح على الخلفية
        wordWrap='RTL',  # تأكيد اتجاه النص من اليمين إلى اليسار
        direction='rtl',  # تأكيد اتجاه النص من اليمين إلى اليسار
    )

    # إنشاء نمط للخلايا مع تحسين المظهر
    cell_style = ParagraphStyle(
        'CellStyle',
        parent=arabic_style,
        fontSize=10,
        leading=12,
        alignment=TA_RIGHT,
        wordWrap='RTL',
        direction='rtl',
    )

    headers = [
        Paragraph(process_arabic_text(_('الكود')), header_style),
        Paragraph(process_arabic_text(_('اسم المنتج')), header_style),
        Paragraph(process_arabic_text(_('الفئة')), header_style),
        Paragraph(process_arabic_text(_('المورد')), header_style),
        Paragraph(process_arabic_text(_('موقع التخزين')), header_style),
        Paragraph(process_arabic_text(_('سعر الشراء')), header_style),
        Paragraph(process_arabic_text(_('سعر البيع')), header_style),
        Paragraph(process_arabic_text(_('الكمية')), header_style),
        Paragraph(process_arabic_text(_('الحد الأدنى')), header_style),
        Paragraph(process_arabic_text(_('الحالة')), header_style)
    ]

    data = [headers]

    # إنشاء أنماط مختلفة لحالات المخزون
    status_low_style = ParagraphStyle(
        'StatusLowStyle',
        parent=cell_style,
        textColor=colors.orange,
        fontName='Arabic-Bold' if 'Arabic-Bold' in pdfmetrics.getRegisteredFontNames() else 'Arabic',
    )

    status_out_style = ParagraphStyle(
        'StatusOutStyle',
        parent=cell_style,
        textColor=colors.red,
        fontName='Arabic-Bold' if 'Arabic-Bold' in pdfmetrics.getRegisteredFontNames() else 'Arabic',
    )

    status_ok_style = ParagraphStyle(
        'StatusOkStyle',
        parent=cell_style,
        textColor=colors.green,
        fontName='Arabic-Bold' if 'Arabic-Bold' in pdfmetrics.getRegisteredFontNames() else 'Arabic',
    )

    for product in products:
        # تحديد حالة المنتج ونمط العرض المناسب
        if product.quantity == 0:
            status = _('نفد')
            status_style = status_out_style
        elif product.quantity <= product.min_quantity:
            status = _('منخفض')
            status_style = status_low_style
        else:
            status = _('متوفر')
            status_style = status_ok_style

        # تحويل البيانات إلى كائنات Paragraph لدعم النصوص العربية
        # تطبيق معالجة النص العربي على جميع البيانات
        row = [
            Paragraph(process_arabic_text(str(product.code)), cell_style),
            Paragraph(process_arabic_text(str(product.name)), cell_style),
            Paragraph(process_arabic_text(str(product.category.name) if product.category else '-'), cell_style),
            Paragraph(process_arabic_text(str(product.supplier.name) if product.supplier else '-'), cell_style),
            Paragraph(process_arabic_text(str(product.storage_location.name) if product.storage_location else '-'), cell_style),
            # تنسيق الأرقام مع إضافة رمز العملة
            Paragraph(f"{float(product.purchase_price):.2f} {process_arabic_text('د.م')}", cell_style),
            Paragraph(f"{float(product.selling_price):.2f} {process_arabic_text('د.م')}", cell_style),
            # تنسيق الأرقام بشكل مباشر بدون معالجة إضافية
            Paragraph(str(product.quantity), cell_style),
            Paragraph(str(product.min_quantity), cell_style),
            Paragraph(process_arabic_text(status), status_style)
        ]
        data.append(row)

    # تعديل عرض الأعمدة لتناسب المحتوى العربي بشكل أفضل
    col_widths = [60, 40, 40, 70, 70, 80, 80, 80, 120, 60]

    # عكس ترتيب الأعمدة لدعم العرض من اليمين إلى اليسار
    for row in data:
        row.reverse()

    # إنشاء الجدول مع تكرار صف العناوين وتحديد عرض الأعمدة
    table = Table(data, repeatRows=1, colWidths=col_widths, hAlign='RIGHT')

    # تنسيق الجدول
    table_style = TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
        ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
        ('FONTNAME', (0, 0), (-1, 0), arabic_bold_font),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
        ('TOPPADDING', (0, 0), (-1, -1), 10),
        ('BACKGROUND', (0, 1), (-1, -1), colors.white),
        ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('RIGHTPADDING', (0, 0), (-1, -1), 15),
        ('LEFTPADDING', (0, 0), (-1, -1), 15),
        ('BOX', (0, 0), (-1, -1), 1, colors.black),  # إضافة إطار خارجي للجدول
        ('LINEBELOW', (0, 0), (-1, 0), 2, colors.black),  # تسميك الخط أسفل العناوين
    ])

    # إضافة تلوين الصفوف البديلة بلون فاتح لتحسين القراءة
    for i in range(1, len(data)):
        if i % 2 == 0:
            table_style.add('BACKGROUND', (0, i), (-1, i), colors.whitesmoke)

    # تأكيد تطبيق اللون الأزرق على صف العنوان
    table_style.add('BACKGROUND', (0, 0), (-1, 0), colors.blue)
    table_style.add('ALIGN', (0, 0), (-1, 0), 'CENTER')

    # لا حاجة لتلوين حالة المخزون هنا لأننا استخدمنا أنماط مخصصة للحالات المختلفة

    table.setStyle(table_style)
    elements.append(table)

    # إضافة معلومات إضافية
    elements.append(Spacer(1, 30))

    # حساب إحصائيات المخزون
    total_products = products.count()
    inventory_value = sum(product.quantity * product.purchase_price for product in products)
    low_stock_count = products.filter(quantity__lte=F('min_quantity'), quantity__gt=0).count()
    out_of_stock_count = products.filter(quantity=0).count()

    # إنشاء نمط للعناوين في جدول الملخص
    summary_title_style = ParagraphStyle(
        'SummaryTitleStyle',
        parent=arabic_style,
        fontSize=12,
        leading=14,
        alignment=TA_RIGHT,
        fontName='Arabic-Bold' if 'Arabic-Bold' in pdfmetrics.getRegisteredFontNames() else 'Arabic',
        textColor=colors.black,
    )

    # إنشاء نمط للقيم في جدول الملخص
    summary_value_style = ParagraphStyle(
        'SummaryValueStyle',
        parent=cell_style,
        fontSize=12,
        alignment=TA_CENTER,
        fontName='Arabic-Bold' if 'Arabic-Bold' in pdfmetrics.getRegisteredFontNames() else 'Arabic',
    )

    # إنشاء نمط للعناوين في جدول الملخص
    summary_title_style = ParagraphStyle(
        'SummaryTitleStyle',
        parent=arabic_style,
        fontSize=12,
        leading=14,
        alignment=TA_RIGHT,
        fontName=arabic_bold_font,
        textColor=colors.black
    )

    # إنشاء نمط للقيم في جدول الملخص
    summary_value_style = ParagraphStyle(
        'SummaryValueStyle',
        parent=cell_style,
        fontSize=12,
        alignment=TA_CENTER,
        fontName=arabic_bold_font
    )

    # إنشاء ملخص البيانات باستخدام كائنات Paragraph لدعم النصوص العربية
    summary_data = [
        [Paragraph(process_arabic_text(_('إجمالي المنتجات:')), summary_title_style), Paragraph(process_arabic_text(str(total_products)), summary_value_style)],
        [Paragraph(process_arabic_text(_('قيمة المخزون:')), summary_title_style), Paragraph(process_arabic_text(f"{inventory_value:.2f} د.م"), summary_value_style)],
        [Paragraph(process_arabic_text(_('منتجات منخفضة المخزون:')), summary_title_style), Paragraph(process_arabic_text(str(low_stock_count)), summary_value_style)],
        [Paragraph(process_arabic_text(_('منتجات نفدت من المخزون:')), summary_title_style), Paragraph(process_arabic_text(str(out_of_stock_count)), summary_value_style)],
    ]

    # عكس ترتيب الأعمدة في جدول الملخص لدعم العرض من اليمين إلى اليسار
    for row in summary_data:
        row.reverse()

    summary_table = Table(summary_data, colWidths=[100, 200])
    summary_style = TableStyle([
        ('ALIGN', (0, 0), (0, -1), 'CENTER'),
        ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
        ('BACKGROUND', (0, 0), (-1, -1), colors.whitesmoke),
        ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
        ('LEFTPADDING', (0, 0), (-1, -1), 10),
        ('RIGHTPADDING', (0, 0), (-1, -1), 10),
        ('TOPPADDING', (0, 0), (-1, -1), 8),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ('TOPPADDING', (0, 0), (-1, -1), 8),
        ('RIGHTPADDING', (0, 0), (-1, -1), 12),
        ('LEFTPADDING', (0, 0), (-1, -1), 12),
    ])
    summary_table.setStyle(summary_style)
    elements.append(summary_table)

    # إضافة معلومات التذييل
    elements.append(Spacer(1, 40))

    # إنشاء نمط للتذييل
    footer_style = ParagraphStyle(
        'FooterStyle',
        parent=arabic_style,
        fontSize=10,
        leading=12,
        alignment=TA_CENTER,
        textColor=colors.black,
    )

    # إضافة تذييل للتقرير
    current_date = datetime.now().strftime('%Y-%m-%d %H:%M')
    footer_text = Paragraph(f"2025 {_('جميع الحقوق محفوظة - تم توليد التقرير في')} {current_date}", footer_style)
    elements.append(footer_text)

    # إضافة معلومات إضافية للمستند
    def add_page_number(canvas, doc):
        # إضافة رقم الصفحة في أسفل كل صفحة
        page_num = canvas.getPageNumber()
        text = "%s %d" % (_('صفحة'), page_num)
        canvas.setFont('Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica', 9)
        canvas.drawRightString(800, 20, text)
        # إضافة التاريخ في أعلى كل صفحة
        canvas.drawString(30, 580, datetime.now().strftime('%Y-%m-%d %H:%M'))

    # بناء المستند مع إضافة أرقام الصفحات
    doc.build(elements, onFirstPage=add_page_number, onLaterPages=add_page_number)

    # الحصول على قيمة البافر وكتابتها في الاستجابة
    pdf = buffer.getvalue()
    buffer.close()
    response.write(pdf)

    return response

def export_products_csv(request):
    # إنشاء استجابة CSV
    response = HttpResponse(content_type='text/csv; charset=utf-8-sig')
    response['Content-Disposition'] = f'attachment; filename="products_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv"'

    # إضافة BOM لدعم الأحرف العربية في Excel
    response.write('\ufeff')

    # إنشاء كاتب CSV مع إعدادات مخصصة لدعم اللغة العربية
    writer = csv.writer(response, delimiter=',', quotechar='"', quoting=csv.QUOTE_MINIMAL)

    # كتابة صف العناوين
    writer.writerow([
        'الكود', 'الاسم', 'الفئة', 'المورد', 'موقع التخزين', 'سعر الشراء',
        'سعر البيع', 'الكمية', 'الحد الأدنى للكمية', 'الوصف', 'نشط'
    ])

    # كتابة صفوف البيانات
    products = Product.objects.select_related('category', 'storage_location', 'supplier').all()
    for product in products:
        writer.writerow([
            product.code,
            product.name,
            product.category.name if product.category else '',
            product.supplier.name if product.supplier else '',
            product.storage_location.name if product.storage_location else '',
            float(product.purchase_price),
            float(product.selling_price),
            product.quantity,
            product.min_quantity,
            product.description,
            'نعم' if product.is_active else 'لا'
        ])

    return response

@login_required
def export_template(request):
    # Create CSV response with UTF-8 encoding
    response = HttpResponse(content_type='text/csv; charset=utf-8-sig')
    response['Content-Disposition'] = 'attachment; filename="products_import_template.csv"'

    # إضافة BOM لدعم الأحرف العربية في Excel
    response.write('\ufeff')

    # Create CSV writer with settings for Arabic support
    writer = csv.writer(response, delimiter=',', quotechar='"', quoting=csv.QUOTE_MINIMAL)

    # Write header row
    writer.writerow([
        'الكود', 'الاسم', 'الفئة', 'المورد', 'موقع التخزين', 'سعر الشراء',
        'سعر البيع', 'الكمية', 'الحد الأدنى للكمية', 'الوصف', 'نشط'
    ])

    # Write example row
    writer.writerow([
        'ABC123',
        'Example Product',
        'Category Name',
        'Supplier Name',
        'Storage Location',
        '100.00',
        '150.00',
        '10',
        '5',
        'Product description',
        'Yes'
    ])

    return response

@login_required
@csrf_exempt
def import_products(request):
    if request.method == 'POST':
        csv_file = request.FILES.get('import_file')
        update_existing = request.POST.get('update_existing') == 'on'

        if not csv_file or not csv_file.name.endswith(('.csv', '.xlsx', '.xls')):
            return JsonResponse({'success': False, 'error': _('الرجاء تحميل ملف CSV أو Excel صالح')})

        # Process CSV file
        try:
            # Read CSV file
            decoded_file = csv_file.read().decode('utf-8')
            io_string = io.StringIO(decoded_file)
            reader = csv.reader(io_string)

            # Skip header row
            next(reader)

            imported_count = 0
            errors = []

            for row in reader:
                if len(row) < 8:  # Ensure row has minimum required fields
                    continue

                code = row[0].strip()
                name = row[1].strip()
                category_name = row[2].strip()
                supplier_name = row[3].strip()
                storage_location_name = row[4].strip()
                purchase_price = float(row[5].strip())
                selling_price = float(row[6].strip())
                quantity = int(row[7].strip())
                min_quantity = int(row[8].strip())
                description = row[9].strip() if len(row) > 9 else ''
                is_active = row[10].strip().lower() in ('yes', 'true', '1') if len(row) > 10 else True

                # Get or create category
                try:
                    category, _ = Category.objects.get_or_create(name=category_name)
                except Exception as e:
                    errors.append(f"Error with category '{category_name}': {str(e)}")
                    continue

                # Get supplier if provided
                supplier = None
                if supplier_name:
                    try:
                        from purchases.models import Supplier
                        supplier = Supplier.objects.filter(name=supplier_name).first()
                        if not supplier:
                            # Create new supplier
                            supplier = Supplier.objects.create(name=supplier_name)
                    except Exception as e:
                        errors.append(f"Error with supplier '{supplier_name}': {str(e)}")
                        continue

                # Get storage location if provided
                storage_location = None
                if storage_location_name:
                    try:
                        storage_location = StorageLocation.objects.filter(name=storage_location_name).first()
                        if not storage_location:
                            # Create new storage location
                            storage_location = StorageLocation.objects.create(name=storage_location_name)
                    except Exception as e:
                        errors.append(f"Error with storage location '{storage_location_name}': {str(e)}")
                        continue

                # Check if product exists
                product_exists = Product.objects.filter(code=code).exists()

                if product_exists and update_existing:
                    # Update existing product
                    product = Product.objects.get(code=code)
                    product.name = name
                    product.category = category
                    product.supplier = supplier
                    product.storage_location = storage_location
                    product.purchase_price = purchase_price
                    product.selling_price = selling_price
                    product.min_quantity = min_quantity
                    product.description = description
                    product.is_active = is_active
                    product.save()

                    imported_count += 1
                elif not product_exists:
                    # Create new product
                    product = Product.objects.create(
                        code=code,
                        name=name,
                        category=category,
                        supplier=supplier,
                        storage_location=storage_location,
                        purchase_price=purchase_price,
                        selling_price=selling_price,
                        quantity=quantity,
                        min_quantity=min_quantity,
                        description=description,
                        is_active=is_active
                    )

                    # Create initial stock movement if quantity > 0
                    if quantity > 0:
                        ProductMovement.objects.create(
                            product=product,
                            movement_type='in',
                            quantity=quantity,
                            reference='Import',
                            notes='Initial stock from import'
                        )

                    imported_count += 1

            if errors:
                return JsonResponse({
                    'success': True,
                    'imported_count': imported_count,
                    'warnings': errors
                })

            return JsonResponse({'success': True, 'imported_count': imported_count})

        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': _('طريقة طلب غير صالحة')})

@login_required
@csrf_exempt
def bulk_delete(request):
    if request.method == 'POST':
        product_ids = request.POST.get('product_ids', '').split(',')

        if not product_ids:
            return JsonResponse({'success': False, 'error': _('لم يتم تحديد أي منتجات')})

        try:
            # Delete products
            products = Product.objects.filter(id__in=product_ids)

            # Delete images first
            for product in products:
                if product.image:
                    product.image.delete()

            # Delete products
            count = products.count()
            products.delete()

            return JsonResponse({'success': True, 'count': count})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': _('طريقة طلب غير صالحة')})

@login_required
def stock_alerts(request):
    # Get products with low stock or out of stock
    products = Product.objects.select_related('category', 'storage_location', 'supplier').filter(
        Q(quantity__lte=F('min_quantity')) | Q(quantity=0)
    ).order_by('quantity')

    # Calculate inventory statistics
    low_stock_count = products.filter(quantity__gt=0, quantity__lte=F('min_quantity')).count()
    out_of_stock_count = products.filter(quantity=0).count()

    # Get categories and storage locations for filters
    categories = Category.objects.all()
    storage_locations = StorageLocation.objects.all()

    # Get suppliers for purchase order creation
    suppliers = []
    try:
        from purchases.models import Supplier
        suppliers = Supplier.objects.all()
    except ImportError:
        pass

    context = {
        'products': products,
        'search_query': search_query, # إضافة متغير البحث للسياق
        'categories': categories,
        'storage_locations': storage_locations,
        'suppliers': suppliers,
        'low_stock_count': low_stock_count,
        'out_of_stock_count': out_of_stock_count,
    }
    return render(request, 'inventory/stock_alerts.html', context)

@login_required
@csrf_exempt
def create_purchase_order(request):
    if request.method == 'POST':
        product_ids = request.POST.getlist('product_ids[]')
        quantities = request.POST.getlist('quantities[]')

        if not product_ids or len(product_ids) != len(quantities):
            messages.error(request, _('بيانات غير صالحة. يرجى التحقق من المنتجات والكميات'))
            return redirect('inventory:stock_alerts')

        # Redirect to purchases app with product IDs and quantities as query parameters
        query_params = ''
        for i in range(len(product_ids)):
            query_params += f'&product_ids={product_ids[i]}&quantities={quantities[i]}'

        return redirect(f'/purchases/new/?{query_params[1:]}')

    return redirect('inventory:stock_alerts')

# Helper function to generate barcode
def generate_barcode(code):
    try:
        # Generate barcode
        code128 = barcode.get_barcode_class('code128')

        # Make sure code is not empty
        if not code or len(code.strip()) == 0:
            code = 'NOCODE'

        # Create barcode instance with options for better quality
        options = {
            'module_width': 0.2,
            'module_height': 15.0,
            'quiet_zone': 6.0,
            'font_size': 10,
            'text_distance': 1.0,
            'background': 'white',
            'foreground': 'black',
        }

        barcode_instance = code128(code, writer=ImageWriter())

        # Save barcode to BytesIO
        buffer = io.BytesIO()
        barcode_instance.write(buffer)

        # Convert to base64
        buffer.seek(0)
        barcode_image = base64.b64encode(buffer.getvalue()).decode('utf-8')

        return barcode_image
    except Exception as e:
        # Log the error
        print(f"Error generating barcode: {e}")
        # Return a placeholder or empty string
        return ""

@login_required
def select_products_for_barcode(request):
    """
    صفحة وسيطة تسمح للمستخدم باختيار المنتجات قبل طباعة الباركودات
    """
    # الحصول على استعلام البحث
    search_query = request.GET.get('search', '')
    category_filter = request.GET.get('category', '')

    # بدء بجميع المنتجات النشطة
    products_qs = Product.objects.select_related('category', 'storage_location', 'supplier').filter(is_active=True)

    # تطبيق فلتر البحث
    if search_query:
        products_qs = products_qs.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(barcodes__barcode_number__icontains=search_query)
        ).distinct()

    # تطبيق فلتر الفئة
    if category_filter:
        products_qs = products_qs.filter(category_id=category_filter)

    # ترتيب النتائج
    products = products_qs.order_by('name')

    # الحصول على جميع الفئات للفلتر
    categories = Category.objects.all().order_by('name')

    # عد النتائج
    total_products = products.count()

    context = {
        'products': products,
        'categories': categories,
        'search_query': search_query,
        'category_filter': category_filter,
        'total_products': total_products,
        'title': _('اختيار المنتجات لطباعة الباركود'),
    }

    return render(request, 'inventory/select_products_for_barcode.html', context)
