# Generated by Django 5.2 on 2025-04-22 13:46

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('settings_app', '0003_deletelog_backuplog_checksum_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='OperationLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation_type', models.CharField(choices=[('backup', 'نسخ احتياطي'), ('restore', 'استعادة'), ('delete', 'حذف')], max_length=20, verbose_name='نوع العملية')),
                ('status', models.CharField(choices=[('success', 'ناجح'), ('failed', 'فاشل'), ('in_progress', 'جاري')], max_length=15, verbose_name='الحالة')),
                ('file_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='اسم الملف')),
                ('details', models.TextField(blank=True, null=True, verbose_name='التفاصيل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')),
                ('backup_log', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='settings_app.backuplog', verbose_name='سجل النسخ الاحتياطي')),
                ('restore_log', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='settings_app.restorelog', verbose_name='سجل الاستعادة')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'سجل العمليات',
                'verbose_name_plural': 'سجلات العمليات',
                'ordering': ['-created_at'],
            },
        ),
    ]
