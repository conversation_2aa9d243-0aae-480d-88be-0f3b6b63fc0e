{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "إضافة تحويل بين الحسابات" %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">{% trans "إضافة تحويل بين الحسابات" %}</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'finance:add_transfer' %}">
                        {% csrf_token %}
                        
                        <div class="form-group mb-3">
                            <label for="date">{% trans "تاريخ التحويل" %} <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="date" name="date" value="{{ today|date:'Y-m-d' }}" required>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="from_account">{% trans "من حساب" %} <span class="text-danger">*</span></label>
                                    <select class="form-control" id="from_account" name="from_account" required>
                                        <option value="">{% trans "اختر الحساب المصدر" %}</option>
                                        {% for account in accounts %}
                                        <option value="{{ account.id }}">{{ account.name }} ({{ account.current_balance }} {% trans "د.م" %})</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="to_account">{% trans "إلى حساب" %} <span class="text-danger">*</span></label>
                                    <select class="form-control" id="to_account" name="to_account" required>
                                        <option value="">{% trans "اختر الحساب الهدف" %}</option>
                                        {% for account in accounts %}
                                        <option value="{{ account.id }}">{{ account.name }} ({{ account.current_balance }} {% trans "د.م" %})</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="amount">{% trans "المبلغ" %} <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" step="0.01" min="0.01" class="form-control" id="amount" name="amount" required>
                                <div class="input-group-append">
                                    <span class="input-group-text">{% trans "د.م" %}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="reference">{% trans "المرجع" %}</label>
                            <input type="text" class="form-control" id="reference" name="reference" placeholder="{% trans 'رقم التحويل أو أي مرجع آخر' %}">
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="description">{% trans "الوصف" %}</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                        
                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-primary">{% trans "إنشاء التحويل" %}</button>
                            <a href="{% url 'finance:transfers' %}" class="btn btn-secondary">{% trans "إلغاء" %}</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // التحقق من أن الحسابين مختلفين
        $('#to_account').change(function() {
            var fromAccount = $('#from_account').val();
            var toAccount = $(this).val();
            
            if (fromAccount && toAccount && fromAccount === toAccount) {
                alert('لا يمكن التحويل إلى نفس الحساب');
                $(this).val('');
            }
        });
        
        $('#from_account').change(function() {
            var fromAccount = $(this).val();
            var toAccount = $('#to_account').val();
            
            if (fromAccount && toAccount && fromAccount === toAccount) {
                alert('لا يمكن التحويل إلى نفس الحساب');
                $('#to_account').val('');
            }
        });
    });
</script>
{% endblock %}
