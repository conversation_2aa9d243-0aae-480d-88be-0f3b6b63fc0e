#!/usr/bin/env python
"""
Script لإنشاء أنواع الباركود الأساسية في النظام
"""

import os
import sys
import django

# إعداد Django
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'auto_parts_pos.settings')
django.setup()

from inventory.barcode_models import BarcodeType

def create_barcode_types():
    """إنشاء أنواع الباركود الأساسية"""
    
    barcode_types = [
        {
            'name': 'Code 128',
            'code': 'code128',
            'description': 'باركود Code 128 - الأكثر شيوعاً واستخداماً، يدعم جميع الأحرف والأرقام'
        },
        {
            'name': 'Code 39',
            'code': 'code39',
            'description': 'باركود Code 39 - يدعم الأحرف الكبيرة والأرقام وبعض الرموز الخاصة'
        },
        {
            'name': 'EAN-13',
            'code': 'ean13',
            'description': 'باركود EAN-13 - المعيار الأوروبي للمنتجات التجارية (13 رقم)'
        },
        {
            'name': 'EAN-8',
            'code': 'ean8',
            'description': 'باركود EAN-8 - نسخة مختصرة من EAN-13 للمنتجات الصغيرة (8 أرقام)'
        },
        {
            'name': 'UPC-A',
            'code': 'upca',
            'description': 'باركود UPC-A - المعيار الأمريكي للمنتجات التجارية (12 رقم)'
        }
    ]
    
    created_count = 0
    updated_count = 0
    
    for barcode_type_data in barcode_types:
        barcode_type, created = BarcodeType.objects.get_or_create(
            code=barcode_type_data['code'],
            defaults={
                'name': barcode_type_data['name'],
                'description': barcode_type_data['description'],
                'is_active': True
            }
        )
        
        if created:
            created_count += 1
            print(f"✅ تم إنشاء نوع باركود جديد: {barcode_type.name} ({barcode_type.code})")
        else:
            # تحديث البيانات إذا كان موجود
            barcode_type.name = barcode_type_data['name']
            barcode_type.description = barcode_type_data['description']
            barcode_type.is_active = True
            barcode_type.save()
            updated_count += 1
            print(f"🔄 تم تحديث نوع باركود موجود: {barcode_type.name} ({barcode_type.code})")
    
    print(f"\n📊 النتائج:")
    print(f"   - تم إنشاء {created_count} نوع باركود جديد")
    print(f"   - تم تحديث {updated_count} نوع باركود موجود")
    print(f"   - إجمالي أنواع الباركود: {BarcodeType.objects.count()}")

if __name__ == '__main__':
    print("🚀 بدء إنشاء أنواع الباركود الأساسية...")
    create_barcode_types()
    print("✨ تم الانتهاء بنجاح!")
