{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "تفاصيل المعاملة" %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <!-- تفاصيل المعاملة -->
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{% trans "تفاصيل المعاملة" %} #{{ transaction.transaction_number }}</h5>
                    <div>
                        <a href="{% url 'finance:transactions' %}" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-right"></i> {% trans "العودة إلى المعاملات" %}
                        </a>
                        {% if transaction.payment_status != 'paid' %}
                        <a href="{% url 'finance:add_payment_to_transaction' transaction.id %}" class="btn btn-success btn-sm">
                            <i class="fas fa-money-bill"></i> {% trans "إضافة دفعة" %}
                        </a>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <th>{% trans "رقم المعاملة" %}</th>
                                    <td>{{ transaction.transaction_number }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "التاريخ" %}</th>
                                    <td>{{ transaction.date }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "النوع" %}</th>
                                    <td>{{ transaction.get_transaction_type_display }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "الحساب" %}</th>
                                    <td>{{ transaction.account.name }}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "الوصف" %}</th>
                                    <td>{{ transaction.description|default:"-" }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <th>{% trans "المبلغ الإجمالي" %}</th>
                                    <td>{{ transaction.amount }} {% trans "د.م" %}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "المبلغ المدفوع" %}</th>
                                    <td class="text-success">{{ transaction.paid_amount }} {% trans "د.م" %}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "المبلغ المتبقي" %}</th>
                                    <td class="text-danger">{{ transaction.remaining_amount }} {% trans "د.م" %}</td>
                                </tr>
                                <tr>
                                    <th>{% trans "حالة الدفع" %}</th>
                                    <td>
                                        {% if transaction.payment_status == 'paid' %}
                                        <span class="badge bg-success">{% trans "مدفوعة" %}</span>
                                        {% elif transaction.payment_status == 'partial' %}
                                        <span class="badge bg-warning">{% trans "مدفوعة جزئياً" %}</span>
                                        {% else %}
                                        <span class="badge bg-danger">{% trans "غير مدفوعة" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>{% trans "تاريخ الإنشاء" %}</th>
                                    <td>{{ transaction.created_at }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    {% if transaction.related_object %}
                    <div class="mt-4">
                        <h6>{% trans "مرتبطة بـ" %}</h6>
                        <div class="alert alert-info">
                            {{ transaction.related_object }}
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if transaction.source_object %}
                    <div class="mt-4">
                        <h6>{% trans "المصدر" %}</h6>
                        <div class="alert alert-info">
                            {{ transaction.source_object }}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- سجل الدفعات -->
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">{% trans "سجل الدفعات" %}</h5>
                </div>
                <div class="card-body">
                    {% if payments %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>{% trans "التاريخ" %}</th>
                                    <th>{% trans "المبلغ" %}</th>
                                    <th>{% trans "طريقة الدفع" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in payments %}
                                <tr>
                                    <td>{{ payment.date }}</td>
                                    <td>{{ payment.amount }} {% trans "د.م" %}</td>
                                    <td>{{ payment.get_payment_method_display }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr class="table-success">
                                    <th>{% trans "الإجمالي" %}</th>
                                    <th colspan="2">{{ transaction.paid_amount }} {% trans "د.م" %}</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-warning">
                        {% trans "لا توجد دفعات مسجلة لهذه المعاملة" %}
                    </div>
                    {% endif %}
                    
                    {% if transaction.payment_status != 'paid' %}
                    <div class="mt-3">
                        <a href="{% url 'finance:add_payment_to_transaction' transaction.id %}" class="btn btn-success w-100">
                            <i class="fas fa-plus"></i> {% trans "إضافة دفعة جديدة" %}
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
