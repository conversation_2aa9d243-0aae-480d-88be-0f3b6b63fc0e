import os
import json
import requests
from django.conf import settings
from .models import BackupSetting

class CloudStorageBase:
    """
    الفئة الأساسية للتخزين السحابي
    """
    def __init__(self):
        self.backup_setting = BackupSetting.objects.first()
    
    def upload_file(self, file_path, remote_path=None):
        """
        رفع ملف إلى التخزين السحابي
        
        Args:
            file_path (str): مسار الملف المحلي
            remote_path (str, optional): المسار في التخزين السحابي
            
        Returns:
            dict: معلومات الملف المرفوع (مثل الرابط، المعرف)
        """
        raise NotImplementedError("يجب تنفيذ هذه الدالة في الفئات الفرعية")
    
    def download_file(self, remote_id, local_path):
        """
        تنزيل ملف من التخزين السحابي
        
        Args:
            remote_id (str): معرف الملف في التخزين السحابي
            local_path (str): المسار المحلي لحفظ الملف
            
        Returns:
            str: مسار الملف المحلي بعد التنزيل
        """
        raise NotImplementedError("يجب تنفيذ هذه الدالة في الفئات الفرعية")
    
    def delete_file(self, remote_id):
        """
        حذف ملف من التخزين السحابي
        
        Args:
            remote_id (str): معرف الملف في التخزين السحابي
            
        Returns:
            bool: True إذا تم الحذف بنجاح، False إذا فشل
        """
        raise NotImplementedError("يجب تنفيذ هذه الدالة في الفئات الفرعية")
    
    def list_files(self, folder_path=None):
        """
        عرض قائمة الملفات في التخزين السحابي
        
        Args:
            folder_path (str, optional): مسار المجلد في التخزين السحابي
            
        Returns:
            list: قائمة بمعلومات الملفات
        """
        raise NotImplementedError("يجب تنفيذ هذه الدالة في الفئات الفرعية")

class GoogleDriveStorage(CloudStorageBase):
    """
    فئة للتعامل مع Google Drive
    """
    def __init__(self):
        super().__init__()
        self.credentials_path = os.path.join(settings.BASE_DIR, 'credentials', 'google_drive_credentials.json')
        self.token_path = os.path.join(settings.BASE_DIR, 'credentials', 'google_drive_token.json')
        
        # التحقق من وجود مجلد الاعتمادات
        os.makedirs(os.path.dirname(self.credentials_path), exist_ok=True)
        
        # تهيئة الاتصال بـ Google Drive
        self._init_drive()
    
    def _init_drive(self):
        """
        تهيئة الاتصال بـ Google Drive
        """
        try:
            from googleapiclient.discovery import build
            from google_auth_oauthlib.flow import InstalledAppFlow
            from google.auth.transport.requests import Request
            from google.oauth2.credentials import Credentials
            
            # نطاقات الوصول المطلوبة
            SCOPES = ['https://www.googleapis.com/auth/drive.file']
            
            creds = None
            # التحقق من وجود ملف الرمز المميز
            if os.path.exists(self.token_path):
                creds = Credentials.from_authorized_user_info(json.load(open(self.token_path)), SCOPES)
            
            # إذا لم تكن هناك اعتمادات صالحة، فقم بطلب المصادقة
            if not creds or not creds.valid:
                if creds and creds.expired and creds.refresh_token:
                    creds.refresh(Request())
                else:
                    if not os.path.exists(self.credentials_path):
                        raise FileNotFoundError(f"ملف اعتمادات Google Drive غير موجود: {self.credentials_path}")
                    
                    flow = InstalledAppFlow.from_client_secrets_file(self.credentials_path, SCOPES)
                    creds = flow.run_local_server(port=0)
                
                # حفظ الاعتمادات للاستخدام لاحقًا
                with open(self.token_path, 'w') as token:
                    token.write(creds.to_json())
            
            # إنشاء خدمة Google Drive
            self.drive_service = build('drive', 'v3', credentials=creds)
            
        except Exception as e:
            print(f"خطأ في تهيئة Google Drive: {str(e)}")
            self.drive_service = None
    
    def upload_file(self, file_path, remote_path=None):
        """
        رفع ملف إلى Google Drive
        """
        try:
            from googleapiclient.http import MediaFileUpload
            
            if not self.drive_service:
                raise Exception("لم يتم تهيئة خدمة Google Drive")
            
            file_metadata = {
                'name': os.path.basename(file_path),
                'mimeType': 'application/octet-stream'
            }
            
            # إذا تم تحديد مجلد، ابحث عنه أو أنشئه
            if remote_path:
                folder_id = self._get_or_create_folder(remote_path)
                file_metadata['parents'] = [folder_id]
            
            media = MediaFileUpload(file_path, resumable=True)
            
            file = self.drive_service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id,name,webViewLink'
            ).execute()
            
            return {
                'id': file.get('id'),
                'name': file.get('name'),
                'link': file.get('webViewLink')
            }
            
        except Exception as e:
            print(f"خطأ في رفع الملف إلى Google Drive: {str(e)}")
            return None
    
    def download_file(self, remote_id, local_path):
        """
        تنزيل ملف من Google Drive
        """
        try:
            from googleapiclient.http import MediaIoBaseDownload
            import io
            
            if not self.drive_service:
                raise Exception("لم يتم تهيئة خدمة Google Drive")
            
            request = self.drive_service.files().get_media(fileId=remote_id)
            
            with open(local_path, 'wb') as f:
                downloader = MediaIoBaseDownload(f, request)
                done = False
                while not done:
                    status, done = downloader.next_chunk()
            
            return local_path
            
        except Exception as e:
            print(f"خطأ في تنزيل الملف من Google Drive: {str(e)}")
            return None
    
    def delete_file(self, remote_id):
        """
        حذف ملف من Google Drive
        """
        try:
            if not self.drive_service:
                raise Exception("لم يتم تهيئة خدمة Google Drive")
            
            self.drive_service.files().delete(fileId=remote_id).execute()
            return True
            
        except Exception as e:
            print(f"خطأ في حذف الملف من Google Drive: {str(e)}")
            return False
    
    def list_files(self, folder_path=None):
        """
        عرض قائمة الملفات في Google Drive
        """
        try:
            if not self.drive_service:
                raise Exception("لم يتم تهيئة خدمة Google Drive")
            
            query = "'me' in owners and trashed = false"
            
            # إذا تم تحديد مجلد، ابحث عن الملفات داخله
            if folder_path:
                folder_id = self._get_folder_id(folder_path)
                if folder_id:
                    query += f" and '{folder_id}' in parents"
            
            results = self.drive_service.files().list(
                q=query,
                pageSize=100,
                fields="files(id, name, mimeType, size, createdTime, webViewLink)"
            ).execute()
            
            return results.get('files', [])
            
        except Exception as e:
            print(f"خطأ في عرض قائمة الملفات من Google Drive: {str(e)}")
            return []
    
    def _get_folder_id(self, folder_path):
        """
        الحصول على معرف المجلد من المسار
        """
        try:
            if not self.drive_service:
                raise Exception("لم يتم تهيئة خدمة Google Drive")
            
            # تقسيم المسار إلى أجزاء
            parts = folder_path.strip('/').split('/')
            
            parent_id = 'root'
            current_id = None
            
            for part in parts:
                query = f"name = '{part}' and mimeType = 'application/vnd.google-apps.folder' and '{parent_id}' in parents and trashed = false"
                
                results = self.drive_service.files().list(
                    q=query,
                    pageSize=1,
                    fields="files(id)"
                ).execute()
                
                items = results.get('files', [])
                
                if not items:
                    return None
                
                current_id = items[0].get('id')
                parent_id = current_id
            
            return current_id
            
        except Exception as e:
            print(f"خطأ في الحصول على معرف المجلد: {str(e)}")
            return None
    
    def _get_or_create_folder(self, folder_path):
        """
        الحصول على معرف المجلد أو إنشائه إذا لم يكن موجودًا
        """
        folder_id = self._get_folder_id(folder_path)
        
        if folder_id:
            return folder_id
        
        # إنشاء المجلد إذا لم يكن موجودًا
        parts = folder_path.strip('/').split('/')
        parent_id = 'root'
        
        for part in parts:
            query = f"name = '{part}' and mimeType = 'application/vnd.google-apps.folder' and '{parent_id}' in parents and trashed = false"
            
            results = self.drive_service.files().list(
                q=query,
                pageSize=1,
                fields="files(id)"
            ).execute()
            
            items = results.get('files', [])
            
            if not items:
                # إنشاء المجلد
                folder_metadata = {
                    'name': part,
                    'mimeType': 'application/vnd.google-apps.folder',
                    'parents': [parent_id]
                }
                
                folder = self.drive_service.files().create(
                    body=folder_metadata,
                    fields='id'
                ).execute()
                
                parent_id = folder.get('id')
            else:
                parent_id = items[0].get('id')
        
        return parent_id

class DropboxStorage(CloudStorageBase):
    """
    فئة للتعامل مع Dropbox
    """
    def __init__(self):
        super().__init__()
        self.token_path = os.path.join(settings.BASE_DIR, 'credentials', 'dropbox_token.txt')
        
        # التحقق من وجود مجلد الاعتمادات
        os.makedirs(os.path.dirname(self.token_path), exist_ok=True)
        
        # تهيئة الاتصال بـ Dropbox
        self._init_dropbox()
    
    def _init_dropbox(self):
        """
        تهيئة الاتصال بـ Dropbox
        """
        try:
            import dropbox
            
            # التحقق من وجود ملف الرمز المميز
            if os.path.exists(self.token_path):
                with open(self.token_path, 'r') as f:
                    access_token = f.read().strip()
                
                self.dbx = dropbox.Dropbox(access_token)
                
                # التحقق من صلاحية الرمز المميز
                try:
                    self.dbx.users_get_current_account()
                except Exception:
                    self.dbx = None
            else:
                self.dbx = None
            
        except Exception as e:
            print(f"خطأ في تهيئة Dropbox: {str(e)}")
            self.dbx = None
    
    def upload_file(self, file_path, remote_path=None):
        """
        رفع ملف إلى Dropbox
        """
        try:
            if not self.dbx:
                raise Exception("لم يتم تهيئة Dropbox")
            
            # إذا لم يتم تحديد المسار البعيد، استخدم اسم الملف فقط
            if not remote_path:
                remote_path = f"/{os.path.basename(file_path)}"
            elif not remote_path.startswith('/'):
                remote_path = f"/{remote_path}"
            
            # التأكد من وجود المجلد
            self._create_folder_if_not_exists(os.path.dirname(remote_path))
            
            # رفع الملف
            with open(file_path, 'rb') as f:
                self.dbx.files_upload(f.read(), remote_path, mode=dropbox.files.WriteMode.overwrite)
            
            # الحصول على رابط مشاركة
            shared_link = self.dbx.sharing_create_shared_link_with_settings(remote_path)
            
            return {
                'id': remote_path,
                'name': os.path.basename(file_path),
                'link': shared_link.url
            }
            
        except Exception as e:
            print(f"خطأ في رفع الملف إلى Dropbox: {str(e)}")
            return None
    
    def download_file(self, remote_id, local_path):
        """
        تنزيل ملف من Dropbox
        """
        try:
            if not self.dbx:
                raise Exception("لم يتم تهيئة Dropbox")
            
            # تنزيل الملف
            with open(local_path, 'wb') as f:
                metadata, res = self.dbx.files_download(remote_id)
                f.write(res.content)
            
            return local_path
            
        except Exception as e:
            print(f"خطأ في تنزيل الملف من Dropbox: {str(e)}")
            return None
    
    def delete_file(self, remote_id):
        """
        حذف ملف من Dropbox
        """
        try:
            if not self.dbx:
                raise Exception("لم يتم تهيئة Dropbox")
            
            self.dbx.files_delete_v2(remote_id)
            return True
            
        except Exception as e:
            print(f"خطأ في حذف الملف من Dropbox: {str(e)}")
            return False
    
    def list_files(self, folder_path=None):
        """
        عرض قائمة الملفات في Dropbox
        """
        try:
            if not self.dbx:
                raise Exception("لم يتم تهيئة Dropbox")
            
            if not folder_path:
                folder_path = ''
            if not folder_path.startswith('/'):
                folder_path = f"/{folder_path}"
            
            result = self.dbx.files_list_folder(folder_path)
            
            files = []
            for entry in result.entries:
                if isinstance(entry, dropbox.files.FileMetadata):
                    # الحصول على رابط مشاركة
                    try:
                        shared_link = self.dbx.sharing_create_shared_link_with_settings(entry.path_display)
                        link = shared_link.url
                    except dropbox.exceptions.ApiError:
                        # قد يكون الملف مشاركًا بالفعل
                        shared_links = self.dbx.sharing_list_shared_links(entry.path_display).links
                        link = shared_links[0].url if shared_links else None
                    
                    files.append({
                        'id': entry.path_display,
                        'name': entry.name,
                        'size': entry.size,
                        'created_time': entry.client_modified,
                        'link': link
                    })
            
            return files
            
        except Exception as e:
            print(f"خطأ في عرض قائمة الملفات من Dropbox: {str(e)}")
            return []
    
    def _create_folder_if_not_exists(self, folder_path):
        """
        إنشاء مجلد إذا لم يكن موجودًا
        """
        try:
            if not folder_path or folder_path == '/':
                return
            
            try:
                self.dbx.files_get_metadata(folder_path)
            except dropbox.exceptions.ApiError:
                # المجلد غير موجود، قم بإنشائه
                self._create_folder_if_not_exists(os.path.dirname(folder_path))
                self.dbx.files_create_folder_v2(folder_path)
                
        except Exception as e:
            print(f"خطأ في إنشاء المجلد: {str(e)}")

def get_storage_provider():
    """
    الحصول على مزود التخزين المناسب بناءً على الإعدادات
    
    Returns:
        CloudStorageBase: كائن مزود التخزين
    """
    backup_setting = BackupSetting.objects.first()
    
    if not backup_setting:
        return None
    
    storage_type = backup_setting.storage_type
    
    if storage_type == 'google_drive':
        return GoogleDriveStorage()
    elif storage_type == 'dropbox':
        return DropboxStorage()
    else:
        return None
