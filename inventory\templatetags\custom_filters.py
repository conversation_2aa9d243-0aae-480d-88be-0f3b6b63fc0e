from django import template

register = template.Library()

@register.filter
def sub(value, arg):
    """
    مرشح مخصص لطرح قيمة من أخرى
    مثال: {{ 10|sub:5 }} ستعطي 5
    """
    try:
        return value - arg
    except (ValueError, TypeError):
        return value
        
@register.filter
def mul(value, arg):
    """
    مرشح مخصص لضرب قيمة بأخرى
    مثال: {{ 10|mul:5 }} ستعطي 50
    """
    try:
        return value * arg
    except (ValueError, TypeError):
        return value
