/* تنسيقات خاصة بصفحات الإعدادات */

/* تنسيق بطاقات الإعدادات */
.dashboard-card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    margin-bottom: 20px;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

.dashboard-card .card-body {
    padding: 20px;
}

.dashboard-card .stretched-link {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    pointer-events: auto;
    content: "";
    background-color: rgba(0, 0, 0, 0);
}

.border-left-primary {
    border-right: 5px solid #4e73df !important;
}

.border-left-success {
    border-right: 5px solid #1cc88a !important;
}

.border-left-info {
    border-right: 5px solid #36b9cc !important;
}

.border-left-warning {
    border-right: 5px solid #f6c23e !important;
}

.border-left-danger {
    border-right: 5px solid #e74a3b !important;
}

.border-left-secondary {
    border-right: 5px solid #858796 !important;
}

/* تنسيق النماذج */
.settings-form .form-group {
    margin-bottom: 20px;
}

.settings-form label {
    font-weight: bold;
}

.settings-form .form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
}

/* ضمان ظهور زر الحفظ بشكل صحيح */
.card-body form {
    margin-bottom: 40px; /* زيادة المسافة أسفل النموذج لضمان ظهور زر الحفظ */
}

.d-grid.gap-2.d-md-flex.justify-content-md-end, .text-end {
    padding-bottom: 30px; /* زيادة المسافة أسفل زر الحفظ */
    margin-bottom: 20px;
}

/* إضافة مسافة أسفل البطاقات في صفحات الإعدادات */
.card.shadow.mb-4 {
    margin-bottom: 30px !important;
}

/* إضافة مسافة أسفل الصف الأخير من البطاقات */
.row:last-child {
    margin-bottom: 40px;
}

/* تنسيق الجداول */
.settings-table th {
    background-color: #f8f9fc;
    font-weight: bold;
}

.settings-table .action-buttons {
    display: flex;
    justify-content: center;
    gap: 5px;
}

.settings-table .action-buttons .btn {
    width: 35px;
    height: 35px;
    padding: 6px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* تنسيق صفحة النسخ الاحتياطي */
.backup-card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.backup-card .card-header {
    background-color: #f8f9fc;
    font-weight: bold;
}

/* تنسيق صفحة معلومات النظام */
.system-info-item {
    padding: 15px;
    border-bottom: 1px solid #e3e6f0;
}

.system-info-item:last-child {
    border-bottom: none;
}

.system-info-label {
    font-weight: bold;
    color: #4e73df;
}
