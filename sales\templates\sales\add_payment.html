{% extends 'base.html' %}
{% load i18n %}
{% load static %}
{% load sales_extras %}

{% block title %}{% trans "إضافة دفعة جديدة" %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card shadow">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">{% trans "إضافة دفعة جديدة للبيع رقم" %} #{{ sale.invoice_number }}</h5>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <h6>{% trans "معلومات البيع" %}</h6>
                    <table class="table table-sm">
                        <tr>
                            <th>{% trans "رقم الفاتورة" %}</th>
                            <td>{{ sale.invoice_number }}</td>
                        </tr>
                        <tr>
                            <th>{% trans "التاريخ" %}</th>
                            <td>{{ sale.date }}</td>
                        </tr>
                        <tr>
                            <th>{% trans "العميل" %}</th>
                            <td>{{ sale.customer.name|default:"عميل نقدي" }}</td>
                        </tr>
                        <tr>
                            <th>{% trans "المبلغ الإجمالي" %}</th>
                            <td>{{ sale.total_amount }} {% trans "د.م" %}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>{% trans "الدفعات السابقة" %}</h6>
                    {% if sale.payments.all %}
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>{% trans "التاريخ" %}</th>
                                <th>{% trans "المبلغ" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in sale.payments.all %}
                            <tr>
                                <td>{{ payment.created_at }}</td>
                                <td>{{ payment.amount }} {% trans "د.م" %}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr>
                                <th>{% trans "المجموع" %}</th>
                                <th>{{ sale.payments.all|sum_payments }} {% trans "د.م" %}</th>
                            </tr>
                        </tfoot>
                    </table>
                    {% else %}
                    <p>{% trans "لا توجد دفعات سابقة" %}</p>
                    {% endif %}
                </div>
            </div>

            <form method="post" action="{% url 'sales:add_payment' sale.id %}">
                {% csrf_token %}
                <div class="form-group">
                    <label for="amount">{% trans "المبلغ" %}</label>
                    <div class="input-group">
                        <input type="number" step="0.01" min="0.01" class="form-control" id="amount" name="amount" required>
                        <div class="input-group-append">
                            <span class="input-group-text">{% trans "د.م" %}</span>
                        </div>
                    </div>
                </div>
                <div class="form-group mt-3">
                    <button type="submit" class="btn btn-primary">{% trans "إضافة الدفعة" %}</button>
                    <a href="{% url 'sales:view_sale' sale.id %}" class="btn btn-secondary">{% trans "إلغاء" %}</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // تركيز تلقائي على حقل المبلغ
        $('#amount').focus();
    });
</script>
{% endblock %}
