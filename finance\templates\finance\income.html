{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "الإيرادات" %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card shadow">
        <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{% trans "الإيرادات" %}</h5>
            <a href="{% url 'finance:add_income' %}" class="btn btn-light btn-sm">
                <i class="fas fa-plus"></i> {% trans "إضافة إيراد جديد" %}
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>{% trans "التاريخ" %}</th>
                            <th>{% trans "الفئة" %}</th>
                            <th>{% trans "الوصف" %}</th>
                            <th>{% trans "المرجع" %}</th>
                            <th>{% trans "الحساب" %}</th>
                            <th>{% trans "المبلغ" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for income in incomes %}
                        <tr>
                            <td>{{ income.date }}</td>
                            <td>{{ income.category.name }}</td>
                            <td>{{ income.description|default:"-" }}</td>
                            <td>{{ income.reference|default:"-" }}</td>
                            <td>{{ income.account.name }}</td>
                            <td class="text-success">{{ income.amount }} {% trans "د.م" %}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'finance:edit_income' income.id %}" class="btn btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'finance:delete_income' income.id %}" class="btn btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center">{% trans "لا توجد إيرادات مسجلة بعد" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
