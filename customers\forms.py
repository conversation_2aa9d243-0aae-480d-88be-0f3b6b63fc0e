from django import forms
from django.utils.translation import gettext_lazy as _
from .models import Customer, CustomerCategory, CustomerVehicle, CustomerInteraction, CustomerImport

class CustomerForm(forms.ModelForm):
    """نموذج إضافة وتعديل العميل"""
    
    class Meta:
        model = Customer
        fields = [
            'name', 'phone', 'email', 'address', 'city', 'category', 
            'credit_limit', 'notes', 'is_active'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': _('أدخل اسم العميل')}),
            'phone': forms.TextInput(attrs={'class': 'form-control', 'placeholder': _('أدخل رقم الهاتف'), 'dir': 'ltr'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': _('أدخل البريد الإلكتروني'), 'dir': 'ltr'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'placeholder': _('أدخل العنوان'), 'rows': 3}),
            'city': forms.TextInput(attrs={'class': 'form-control', 'placeholder': _('أدخل المدينة')}),
            'category': forms.Select(attrs={'class': 'form-select'}),
            'credit_limit': forms.NumberInput(attrs={'class': 'form-control', 'min': 0, 'step': 0.01}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'placeholder': _('أدخل ملاحظات إضافية'), 'rows': 3}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def clean_phone(self):
        """التحقق من رقم الهاتف"""
        phone = self.cleaned_data.get('phone')
        
        # التحقق من أن رقم الهاتف يحتوي على أرقام فقط
        if not phone.isdigit():
            raise forms.ValidationError(_('يجب أن يحتوي رقم الهاتف على أرقام فقط'))
        
        # التحقق من عدم تكرار رقم الهاتف
        if Customer.objects.filter(phone=phone).exclude(id=self.instance.id).exists():
            raise forms.ValidationError(_('رقم الهاتف مستخدم بالفعل'))
        
        return phone
    
    def clean_email(self):
        """التحقق من البريد الإلكتروني"""
        email = self.cleaned_data.get('email')
        
        if email:
            # التحقق من عدم تكرار البريد الإلكتروني
            if Customer.objects.filter(email=email).exclude(id=self.instance.id).exists():
                raise forms.ValidationError(_('البريد الإلكتروني مستخدم بالفعل'))
        
        return email


class CustomerVehicleForm(forms.ModelForm):
    """نموذج إضافة وتعديل مركبة العميل"""
    
    class Meta:
        model = CustomerVehicle
        fields = ['make', 'model', 'year', 'license_plate', 'vin', 'notes']
        widgets = {
            'make': forms.TextInput(attrs={'class': 'form-control', 'placeholder': _('أدخل الشركة المصنعة')}),
            'model': forms.TextInput(attrs={'class': 'form-control', 'placeholder': _('أدخل الموديل')}),
            'year': forms.NumberInput(attrs={'class': 'form-control', 'min': 1900, 'max': 2100}),
            'license_plate': forms.TextInput(attrs={'class': 'form-control', 'placeholder': _('أدخل رقم اللوحة')}),
            'vin': forms.TextInput(attrs={'class': 'form-control', 'placeholder': _('أدخل رقم الهيكل (VIN)')}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'placeholder': _('أدخل ملاحظات إضافية'), 'rows': 3}),
        }


class CustomerInteractionForm(forms.ModelForm):
    """نموذج إضافة تفاعل مع العميل"""
    
    class Meta:
        model = CustomerInteraction
        fields = ['interaction_type', 'notes', 'related_sale']
        widgets = {
            'interaction_type': forms.Select(attrs={'class': 'form-select'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'placeholder': _('أدخل تفاصيل التفاعل'), 'rows': 3}),
            'related_sale': forms.Select(attrs={'class': 'form-select'}),
        }


class CustomerImportForm(forms.ModelForm):
    """نموذج استيراد العملاء من ملف"""
    
    class Meta:
        model = CustomerImport
        fields = ['file']
        widgets = {
            'file': forms.FileInput(attrs={'class': 'form-control', 'accept': '.csv,.xlsx,.xls'}),
        }
    
    def clean_file(self):
        """التحقق من نوع الملف"""
        file = self.cleaned_data.get('file')
        
        if file:
            # التحقق من امتداد الملف
            ext = file.name.split('.')[-1].lower()
            if ext not in ['csv', 'xlsx', 'xls']:
                raise forms.ValidationError(_('يجب أن يكون الملف بتنسيق CSV أو Excel'))
        
        return file


class CustomerSearchForm(forms.Form):
    """نموذج البحث عن العملاء"""
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('بحث بالاسم، رقم الهاتف، أو البريد الإلكتروني')
        })
    )
    
    category = forms.ModelChoiceField(
        queryset=CustomerCategory.objects.all(),
        required=False,
        empty_label=_('جميع الفئات'),
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    is_active = forms.ChoiceField(
        choices=[
            ('', _('الكل')),
            ('1', _('نشط')),
            ('0', _('غير نشط')),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    has_purchases = forms.ChoiceField(
        choices=[
            ('', _('الكل')),
            ('1', _('لديه مشتريات')),
            ('0', _('لا توجد مشتريات')),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    city = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('المدينة')
        })
    )


class CustomerExportForm(forms.Form):
    """نموذج تصدير العملاء"""
    
    format = forms.ChoiceField(
        choices=[
            ('csv', _('CSV')),
            ('excel', _('Excel')),
            ('pdf', _('PDF')),
        ],
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    category = forms.ModelChoiceField(
        queryset=CustomerCategory.objects.all(),
        required=False,
        empty_label=_('جميع الفئات'),
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    is_active = forms.ChoiceField(
        choices=[
            ('', _('الكل')),
            ('1', _('نشط')),
            ('0', _('غير نشط')),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
