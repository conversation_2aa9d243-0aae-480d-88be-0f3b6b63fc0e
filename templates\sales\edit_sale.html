{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "تعديل البيع" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .required-field::after {
        content: " *";
        color: red;
    }

    .form-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .form-section-title {
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }

    .product-row {
        transition: all 0.3s;
    }

    .product-row:hover {
        background-color: #f8f9fa;
    }

    .product-image-small {
        width: 40px;
        height: 40px;
        object-fit: contain;
        border-radius: 4px;
    }

    .summary-card {
        position: sticky;
        top: 20px;
    }
    
    .barcode-scanner {
        position: relative;
    }
    
    .barcode-scanner .scan-icon {
        position: absolute;
        top: 50%;
        left: 10px;
        transform: translateY(-50%);
        cursor: pointer;
    }
    
    .barcode-scanner .form-control {
        padding-left: 40px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "تعديل البيع" %}</h1>
    <div>
        <a href="{% url 'sales:view_sale' sale.id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى تفاصيل البيع" %}
        </a>
    </div>
</div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<div class="alert alert-warning mb-4">
    <i class="fas fa-exclamation-triangle me-1"></i>
    {% trans "تنبيه: تعديل البيع سيؤثر على المخزون. سيتم تعديل كميات المنتجات في المخزون وفقاً للتغييرات." %}
</div>

<form method="post" id="saleForm">
    {% csrf_token %}

    <div class="row">
        <div class="col-md-8">
            <!-- Basic Information Section -->
            <div class="form-section">
                <h5 class="form-section-title">{% trans "المعلومات الأساسية" %}</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="customer" class="form-label required-field">{% trans "العميل" %}</label>
                        <select class="form-select" id="customer" name="customer" required>
                            <option value="">{% trans "اختر العميل" %}</option>
                            {% for customer in customers %}
                            <option value="{{ customer.id }}" {% if customer.id == sale.customer.id %}selected{% endif %}>{{ customer.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="date" class="form-label required-field">{% trans "تاريخ البيع" %}</label>
                        <input type="date" class="form-control" id="date" name="date" value="{{ sale.date|date:'Y-m-d' }}" required>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="payment_method" class="form-label required-field">{% trans "طريقة الدفع" %}</label>
                        <select class="form-select" id="payment_method" name="payment_method" required>
                            <option value="cash" {% if sale.payment_method == 'cash' %}selected{% endif %}>{% trans "نقدي" %}</option>
                            <option value="card" {% if sale.payment_method == 'card' %}selected{% endif %}>{% trans "بطاقة ائتمان" %}</option>
                            <option value="transfer" {% if sale.payment_method == 'transfer' %}selected{% endif %}>{% trans "تحويل بنكي" %}</option>
                            <option value="check" {% if sale.payment_method == 'check' %}selected{% endif %}>{% trans "شيك" %}</option>
                            <option value="credit" {% if sale.payment_method == 'credit' %}selected{% endif %}>{% trans "آجل" %}</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="status" class="form-label required-field">{% trans "الحالة" %}</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="completed" {% if sale.status == 'completed' %}selected{% endif %}>{% trans "مكتمل" %}</option>
                            <option value="pending" {% if sale.status == 'pending' %}selected{% endif %}>{% trans "معلق" %}</option>
                            <option value="cancelled" {% if sale.status == 'cancelled' %}selected{% endif %}>{% trans "ملغي" %}</option>
                        </select>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="notes" class="form-label">{% trans "ملاحظات" %}</label>
                    <textarea class="form-control" id="notes" name="notes" rows="2">{{ sale.notes }}</textarea>
                </div>
            </div>

            <!-- Products Section -->
            <div class="form-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="form-section-title mb-0">{% trans "المنتجات" %}</h5>
                    <div>
                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                            <i class="fas fa-plus me-1"></i> {% trans "إضافة منتج" %}
                        </button>
                        <button type="button" class="btn btn-sm btn-info ms-2" data-bs-toggle="modal" data-bs-target="#scannerModal">
                            <i class="fas fa-barcode me-1"></i> {% trans "مسح الباركود" %}
                        </button>
                    </div>
                </div>

                <div class="barcode-scanner mb-3">
                    <input type="text" class="form-control" id="barcodeInput" placeholder="{% trans 'أدخل الباركود أو اضغط على أيقونة المسح...' %}">
                    <span class="scan-icon" data-bs-toggle="modal" data-bs-target="#scannerModal">
                        <i class="fas fa-barcode"></i>
                    </span>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered" id="productsTable">
                        <thead>
                            <tr>
                                <th width="50px">{% trans "صورة" %}</th>
                                <th>{% trans "المنتج" %}</th>
                                <th width="100px">{% trans "الكمية" %}</th>
                                <th width="150px">{% trans "سعر الوحدة" %}</th>
                                <th width="150px">{% trans "المجموع" %}</th>
                                <th width="50px">{% trans "الإجراءات" %}</th>
                            </tr>
                        </thead>
                        <tbody id="productsTableBody">
                            {% for item in items %}
                            <tr class="product-row">
                                <td>
                                    {% if item.product.image %}
                                    <img src="{{ item.product.image.url }}" alt="{{ item.product.name }}" class="product-image-small">
                                    {% else %}
                                    <div class="text-center">
                                        <i class="fas fa-box text-muted"></i>
                                    </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <input type="hidden" name="product_ids[]" value="{{ item.product.id }}">
                                    <input type="hidden" name="item_ids[]" value="{{ item.id }}">
                                    <strong>{{ item.product.code }}</strong> - {{ item.product.name }}
                                    <div class="small text-muted">{% trans "المخزون المتاح:" %} {{ item.product.quantity|add:item.quantity }}</div>
                                </td>
                                <td>
                                    <input type="number" class="form-control form-control-sm quantity-input" name="quantities[]" value="{{ item.quantity }}" min="1" max="{{ item.product.quantity|add:item.quantity }}" required data-stock="{{ item.product.quantity|add:item.quantity }}" data-original="{{ item.quantity }}">
                                </td>
                                <td>
                                    <div class="input-group input-group-sm">
                                        <input type="number" class="form-control form-control-sm unit-price-input" name="unit_prices[]" value="{{ item.unit_price }}" step="0.01" min="0" required>
                                        <span class="input-group-text">ر.س</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="subtotal">{{ item.subtotal }}</span> ر.س
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-danger remove-product">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% empty %}
                            <tr id="noProductsRow">
                                <td colspan="6" class="text-center">{% trans "لم يتم إضافة منتجات بعد" %}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-1"></i>
                    {% trans "يمكنك إضافة المنتجات باستخدام زر 'إضافة منتج' أعلاه أو عن طريق مسح الباركود. تأكد من إضافة منتج واحد على الأقل قبل حفظ الفاتورة." %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Summary Section -->
            <div class="card shadow summary-card">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "ملخص الفاتورة" %}</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "المجموع الفرعي:" %}</span>
                            <span id="subtotalSummary">{{ sale.subtotal }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "ضريبة القيمة المضافة (15%):" %}</span>
                            <span id="taxSummary">{{ sale.tax_amount }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="discount" class="form-label">{% trans "الخصم:" %}</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="discount" name="discount" step="0.01" min="0" value="{{ sale.discount }}">
                            <span class="input-group-text">ر.س</span>
                        </div>
                    </div>
                    <hr>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <strong>{% trans "المجموع الكلي:" %}</strong>
                            <strong id="totalSummary">{{ sale.total_amount }}</strong>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="amount_paid" class="form-label">{% trans "المبلغ المدفوع:" %}</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="amount_paid" name="amount_paid" step="0.01" min="0" value="{{ total_paid }}">
                            <span class="input-group-text">ر.س</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "المبلغ المتبقي:" %}</span>
                            <span id="remainingAmount">{{ remaining_amount }}</span>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" id="saveBtn">
                            <i class="fas fa-save me-1"></i> {% trans "حفظ التغييرات" %}
                        </button>
                        <a href="{% url 'sales:view_sale' sale.id %}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i> {% trans "إلغاء" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Add Product Modal -->
<div class="modal fade" id="addProductModal" tabindex="-1" aria-labelledby="addProductModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addProductModalLabel">{% trans "إضافة منتج" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="productSearch" class="form-label">{% trans "بحث عن منتج" %}</label>
                    <input type="text" class="form-control" id="productSearch" placeholder="{% trans 'اكتب اسم المنتج أو الكود...' %}">
                </div>

                <div class="table-responsive mt-3">
                    <table class="table table-bordered table-hover" id="productsSearchTable">
                        <thead>
                            <tr>
                                <th>{% trans "الكود" %}</th>
                                <th>{% trans "اسم المنتج" %}</th>
                                <th>{% trans "الفئة" %}</th>
                                <th>{% trans "الكمية المتوفرة" %}</th>
                                <th>{% trans "سعر البيع" %}</th>
                                <th>{% trans "الإجراءات" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr>
                                <td>{{ product.code }}</td>
                                <td>{{ product.name }}</td>
                                <td>{{ product.category.name }}</td>
                                <td>{{ product.quantity }}</td>
                                <td>{{ product.sale_price }} ر.س</td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-primary select-product"
                                            data-id="{{ product.id }}"
                                            data-code="{{ product.code }}"
                                            data-name="{{ product.name }}"
                                            data-price="{{ product.sale_price }}"
                                            data-stock="{{ product.quantity }}"
                                            data-image="{% if product.image %}{{ product.image.url }}{% endif %}">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إغلاق" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Barcode Scanner Modal -->
<div class="modal fade" id="scannerModal" tabindex="-1" aria-labelledby="scannerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="scannerModalLabel">{% trans "مسح الباركود" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="scanner-container">
                    <video id="scanner-video"></video>
                </div>
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-1"></i>
                    {% trans "قم بتوجيه الكاميرا نحو الباركود للمسح التلقائي." %}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إغلاق" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/quagga@0.12.1/dist/quagga.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize DataTable for products search
        var searchTable = $('#productsSearchTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
            },
            "pageLength": 5
        });

        // Product search
        $('#productSearch').keyup(function() {
            searchTable.search($(this).val()).draw();
        });

        // Select Product
        $(document).on('click', '.select-product', function() {
            var productId = $(this).data('id');
            var productCode = $(this).data('code');
            var productName = $(this).data('name');
            var productPrice = $(this).data('price');
            var productStock = $(this).data('stock');
            var productImage = $(this).data('image');

            // Check if product already exists in the table
            var existingRow = $('input[name="product_ids[]"][value="' + productId + '"]').closest('tr');
            
            if (existingRow.length > 0) {
                // Product already exists, increment quantity
                var quantityInput = existingRow.find('.quantity-input');
                var currentQuantity = parseInt(quantityInput.val());
                var maxStock = parseInt(quantityInput.data('stock'));
                
                if (currentQuantity < maxStock) {
                    quantityInput.val(currentQuantity + 1).trigger('input');
                    // Show notification
                    alert('{% trans "تم زيادة كمية المنتج" %}');
                } else {
                    alert('{% trans "لا يمكن زيادة الكمية. الكمية المتاحة في المخزون:" %}' + maxStock);
                }
            } else {
                // Remove "no products" row if exists
                $('#noProductsRow').remove();

                // Add product to table
                var newRow = `
                    <tr class="product-row">
                        <td>
                            ${productImage ?
                                `<img src="${productImage}" alt="${productName}" class="product-image-small">` :
                                `<div class="text-center"><i class="fas fa-box text-muted"></i></div>`
                            }
                        </td>
                        <td>
                            <input type="hidden" name="product_ids[]" value="${productId}">
                            <input type="hidden" name="item_ids[]" value="">
                            <strong>${productCode}</strong> - ${productName}
                            <div class="small text-muted">{% trans "المخزون المتاح:" %} ${productStock}</div>
                        </td>
                        <td>
                            <input type="number" class="form-control form-control-sm quantity-input" name="quantities[]" value="1" min="1" max="${productStock}" required data-stock="${productStock}" data-original="0">
                        </td>
                        <td>
                            <div class="input-group input-group-sm">
                                <input type="number" class="form-control form-control-sm unit-price-input" name="unit_prices[]" value="${productPrice}" step="0.01" min="0" required>
                                <span class="input-group-text">ر.س</span>
                            </div>
                        </td>
                        <td>
                            <span class="subtotal">${productPrice}</span> ر.س
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm btn-danger remove-product">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;

                $('#productsTableBody').append(newRow);
            }

            // Close modal
            $('#addProductModal').modal('hide');

            // Update summary
            updateSummary();
        });

        // Remove Product
        $(document).on('click', '.remove-product', function() {
            $(this).closest('tr').remove();

            // If no products left, add "no products" row
            if ($('#productsTableBody tr').length === 0) {
                $('#productsTableBody').html('<tr id="noProductsRow"><td colspan="6" class="text-center">{% trans "لم يتم إضافة منتجات بعد" %}</td></tr>');
            }

            // Update summary
            updateSummary();
        });

        // Update subtotal when quantity or unit price changes
        $(document).on('input', '.quantity-input, .unit-price-input', function() {
            var row = $(this).closest('tr');
            var quantity = parseFloat(row.find('.quantity-input').val()) || 0;
            var unitPrice = parseFloat(row.find('.unit-price-input').val()) || 0;
            var stock = parseInt(row.find('.quantity-input').data('stock'));
            
            // Check if quantity exceeds stock
            if (quantity > stock) {
                alert('{% trans "الكمية المطلوبة تتجاوز المخزون المتاح!" %}');
                row.find('.quantity-input').val(stock);
                quantity = stock;
            }
            
            var subtotal = quantity * unitPrice;

            row.find('.subtotal').text(subtotal.toFixed(2));

            // Update summary
            updateSummary();
        });

        // Update discount
        $('#discount').on('input', function() {
            updateSummary();
        });

        // Update amount paid
        $('#amount_paid').on('input', function() {
            updateSummary();
        });

        // Update summary
        function updateSummary() {
            var subtotal = 0;

            // Calculate subtotal
            $('.subtotal').each(function() {
                subtotal += parseFloat($(this).text()) || 0;
            });

            // Calculate tax
            var taxRate = 15; // 15% VAT
            var tax = subtotal * (taxRate / 100);

            // Get discount
            var discount = parseFloat($('#discount').val()) || 0;

            // Calculate total
            var total = subtotal + tax - discount;
            if (total < 0) total = 0;

            // Get amount paid
            var amountPaid = parseFloat($('#amount_paid').val()) || 0;

            // Calculate remaining amount
            var remaining = total - amountPaid;

            // Update summary
            $('#subtotalSummary').text(subtotal.toFixed(2));
            $('#taxSummary').text(tax.toFixed(2));
            $('#totalSummary').text(total.toFixed(2));
            $('#remainingAmount').text(remaining.toFixed(2));
        }

        // Change payment method
        $('#payment_method').change(function() {
            var method = $(this).val();
            var total = parseFloat($('#totalSummary').text()) || 0;
            
            if (method === 'cash') {
                $('#amount_paid').val(total.toFixed(2));
                $('#remainingAmount').text('0.00');
            } else if (method === 'credit') {
                $('#amount_paid').val('0.00');
                $('#remainingAmount').text(total.toFixed(2));
            }
        });

        // Barcode input handling
        $('#barcodeInput').keypress(function(e) {
            if (e.which === 13) { // Enter key
                e.preventDefault();
                var barcode = $(this).val().trim();
                if (barcode) {
                    findProductByBarcode(barcode);
                    $(this).val('');
                }
            }
        });

        // Find product by barcode
        function findProductByBarcode(barcode) {
            // Here you would normally make an AJAX call to your backend
            // For demo purposes, we'll just search the table
            var found = false;
            
            $('#productsSearchTable tbody tr').each(function() {
                var code = $(this).find('td:first').text();
                if (code === barcode) {
                    $(this).find('.select-product').click();
                    found = true;
                    return false; // Break the loop
                }
            });
            
            if (!found) {
                alert('{% trans "لم يتم العثور على منتج بهذا الباركود" %}');
            }
        }

        // Initialize barcode scanner
        var scanner = null;
        
        $('#scannerModal').on('shown.bs.modal', function() {
            // إضافة رسالة تحميل
            $('#scanner-container').html('<div class="text-center p-3"><i class="fas fa-spinner fa-spin fa-2x mb-2"></i><p>{% trans "جاري تهيئة الكاميرا..." %}</p></div>');
            
            // التحقق من وجود مكتبة Quagga
            if (typeof Quagga === 'undefined') {
                $('#scanner-container').html('<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>{% trans "لم يتم تحميل مكتبة ماسح الباركود بشكل صحيح" %}</div>');
                return;
            }
            
            // Initialize Quagga
            Quagga.init({
                inputStream: {
                    name: "Live",
                    type: "LiveStream",
                    target: document.querySelector('#scanner-video'),
                    constraints: {
                        width: 480,
                        height: 320,
                        facingMode: "environment"
                    },
                },
                locator: {
                    patchSize: "medium",
                    halfSample: true
                },
                numOfWorkers: 2,
                frequency: 10,
                decoder: {
                    readers: [
                        "code_128_reader",
                        "ean_reader",
                        "ean_8_reader",
                        "code_39_reader",
                        "code_39_vin_reader",
                        "codabar_reader",
                        "upc_reader",
                        "upc_e_reader",
                        "i2of5_reader"
                    ],
                    debug: {
                        showCanvas: true,
                        showPatches: false,
                        showFoundPatches: false,
                        showSkeleton: false,
                        showLabels: false,
                        showPatchLabels: false,
                        showRemainingPatchLabels: false,
                        boxFromPatches: {
                            showTransformed: false,
                            showTransformedBox: false,
                            showBB: true
                        }
                    }
                },
            }, function(err) {
                if (err) {
                    console.error("خطأ في تهيئة ماسح الباركود:", err);
                    
                    // عرض رسالة خطأ مناسبة للمستخدم
                    let errorMessage = '{% trans "حدث خطأ أثناء تهيئة ماسح الباركود" %}';
                    
                    // تحديد نوع الخطأ وعرض رسالة مناسبة
                    if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') {
                        errorMessage = '{% trans "لم يتم السماح بالوصول إلى الكاميرا. يرجى السماح بالوصول للكاميرا في إعدادات المتصفح." %}';
                    } else if (err.name === 'NotFoundError' || err.name === 'DevicesNotFoundError') {
                        errorMessage = '{% trans "لم يتم العثور على كاميرا متصلة بجهازك." %}';
                    } else if (err.name === 'NotReadableError' || err.name === 'TrackStartError') {
                        errorMessage = '{% trans "لا يمكن الوصول إلى الكاميرا. قد تكون مستخدمة من قبل تطبيق آخر." %}';
                    } else if (err.name === 'OverconstrainedError' || err.name === 'ConstraintNotSatisfiedError') {
                        errorMessage = '{% trans "لا يمكن استخدام الكاميرا بالإعدادات المطلوبة." %}';
                    }
                    
                    // عرض رسالة الخطأ في نافذة الماسح
                    $('#scanner-container').html(`<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>${errorMessage}</div>`);
                    return;
                }
                
                try {
                    Quagga.start();
                    // إعادة إنشاء عنصر الفيديو بعد التهيئة الناجحة
                    $('#scanner-container').html('<video id="scanner-video"></video>');
                } catch (error) {
                    console.error("خطأ في بدء ماسح الباركود:", error);
                    $('#scanner-container').html(`<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>{% trans "حدث خطأ أثناء تشغيل ماسح الباركود" %}</div>`);
                }
            });
            
            // When a barcode is detected
            Quagga.onDetected(function(result) {
                var code = result.codeResult.code;
                $('#barcodeInput').val(code);
                findProductByBarcode(code);
                $('#scannerModal').modal('hide');
            });
        });
        
        $('#scannerModal').on('hidden.bs.modal', function() {
            if (Quagga) {
                Quagga.stop();
            }
        });

        // Form validation
        $('#saleForm').submit(function(e) {
            if ($('#productsTableBody tr').not('#noProductsRow').length === 0) {
                e.preventDefault();
                alert('{% trans "يرجى إضافة منتج واحد على الأقل" %}');
                return false;
            }
            
            var total = parseFloat($('#totalSummary').text()) || 0;
            var amountPaid = parseFloat($('#amount_paid').val()) || 0;
            var paymentMethod = $('#payment_method').val();
            
            if (paymentMethod !== 'credit' && amountPaid < total) {
                if (!confirm('{% trans "المبلغ المدفوع أقل من المبلغ الإجمالي. هل تريد المتابعة؟" %}')) {
                    e.preventDefault();
                    return false;
                }
            }

            return true;
        });

        // Initialize summary
        updateSummary();
    });
</script>
{% endblock %}
