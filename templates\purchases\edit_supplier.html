{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "تعديل المورد" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .required-field::after {
        content: " *";
        color: red;
    }
    
    .form-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .form-section-title {
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "تعديل المورد" %}</h1>
    <div>
        <a href="{% url 'purchases:suppliers' %}" class="btn btn-primary">
            <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى الموردين" %}
        </a>
    </div>
</div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<!-- Supplier Form -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "معلومات المورد" %}</h6>
    </div>
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            
            <div class="form-section">
                <h5 class="form-section-title">{% trans "المعلومات الأساسية" %}</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="name" class="form-label required-field">{% trans "اسم المورد" %}</label>
                        <input type="text" class="form-control" id="name" name="name" value="{{ supplier.name }}" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="category" class="form-label">{% trans "الفئة" %}</label>
                        <select class="form-select" id="category" name="category">
                            <option value="">{% trans "اختر الفئة (اختياري)" %}</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}" {% if supplier.category and supplier.category.id == category.id %}selected{% endif %}>{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="contact_person" class="form-label">{% trans "الشخص المسؤول" %}</label>
                        <input type="text" class="form-control" id="contact_person" name="contact_person" value="{{ supplier.contact_person|default:'' }}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="phone" class="form-label required-field">{% trans "رقم الهاتف" %}</label>
                        <input type="tel" class="form-control" id="phone" name="phone" value="{{ supplier.phone }}" required>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="email" class="form-label">{% trans "البريد الإلكتروني" %}</label>
                        <input type="email" class="form-control" id="email" name="email" value="{{ supplier.email|default:'' }}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="website" class="form-label">{% trans "الموقع الإلكتروني" %}</label>
                        <input type="url" class="form-control" id="website" name="website" placeholder="https://" value="{{ supplier.website|default:'' }}">
                    </div>
                </div>
            </div>
            
            <div class="form-section">
                <h5 class="form-section-title">{% trans "معلومات العنوان" %}</h5>
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="address" class="form-label">{% trans "العنوان" %}</label>
                        <textarea class="form-control" id="address" name="address" rows="2">{{ supplier.address|default:'' }}</textarea>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="city" class="form-label">{% trans "المدينة" %}</label>
                        <input type="text" class="form-control" id="city" name="city" value="{{ supplier.city|default:'' }}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="country" class="form-label">{% trans "الدولة" %}</label>
                        <input type="text" class="form-control" id="country" name="country" value="{{ supplier.country|default:'' }}">
                    </div>
                </div>
            </div>
            
            <div class="form-section">
                <h5 class="form-section-title">{% trans "معلومات إضافية" %}</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="tax_number" class="form-label">{% trans "الرقم الضريبي" %}</label>
                        <input type="text" class="form-control" id="tax_number" name="tax_number" value="{{ supplier.tax_number|default:'' }}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="form-check mt-4">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {% if supplier.is_active %}checked{% endif %}>
                            <label class="form-check-label" for="is_active">
                                {% trans "نشط" %}
                            </label>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="notes" class="form-label">{% trans "ملاحظات" %}</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3">{{ supplier.notes|default:'' }}</textarea>
                    </div>
                </div>
            </div>
            
            <div class="mt-4">
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-save me-1"></i> {% trans "حفظ التغييرات" %}
                </button>
                <a href="{% url 'purchases:suppliers' %}" class="btn btn-secondary">
                    {% trans "إلغاء" %}
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Form validation
        $('form').submit(function(e) {
            var isValid = true;
            
            // Check if all required fields are filled
            $(this).find('[required]').each(function() {
                if ($(this).val() === '') {
                    isValid = false;
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });
            
            // Validate email format if provided
            var email = $('#email').val();
            if (email && !isValidEmail(email)) {
                isValid = false;
                $('#email').addClass('is-invalid');
                alert('{% trans "يرجى إدخال بريد إلكتروني صحيح" %}');
            }
            
            // Validate phone format
            var phone = $('#phone').val();
            if (phone && !isValidPhone(phone)) {
                isValid = false;
                $('#phone').addClass('is-invalid');
                alert('{% trans "يرجى إدخال رقم هاتف صحيح" %}');
            }
            
            if (!isValid) {
                e.preventDefault();
                return false;
            }
            
            return true;
        });
        
        // Validate email format
        function isValidEmail(email) {
            var re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        }
        
        // Validate phone format (simple validation)
        function isValidPhone(phone) {
            var re = /^[0-9+\-\s()]{8,20}$/;
            return re.test(phone);
        }
    });
</script>
{% endblock %}
