from django.core.management.base import BaseCommand
from django.utils.translation import gettext_lazy as _
from finance.models import IncomeCategory, ExpenseCategory, Account

class Command(BaseCommand):
    help = 'Setup initial finance data (categories, accounts, etc.)'

    def handle(self, *args, **kwargs):
        self.stdout.write(self.style.SUCCESS('Setting up finance data...'))
        
        # إضافة فئات الإيرادات الافتراضية
        self.setup_income_categories()
        
        # إضافة فئات المصروفات الافتراضية
        self.setup_expense_categories()
        
        # إضافة حساب افتراضي إذا لم يكن هناك حسابات
        self.setup_default_account()
        
        self.stdout.write(self.style.SUCCESS('Finance data setup completed successfully!'))
    
    def setup_income_categories(self):
        # قائمة بفئات الإيرادات الافتراضية
        default_categories = [
            {'name': _('مبيعات'), 'description': _('إيرادات من مبيعات قطع غيار السيارات')},
            {'name': _('خدمات'), 'description': _('إيرادات من تقديم خدمات الصيانة والإصلاح')},
            {'name': _('استشارات'), 'description': _('إيرادات من تقديم استشارات فنية')},
            {'name': _('عمولات'), 'description': _('عمولات من شركاء أو موردين')},
            {'name': _('أخرى'), 'description': _('إيرادات أخرى متنوعة')},
        ]
        
        # إضافة الفئات إذا لم تكن موجودة
        categories_created = 0
        for category_data in default_categories:
            category, created = IncomeCategory.objects.get_or_create(
                name=category_data['name'],
                defaults={'description': category_data['description']}
            )
            if created:
                categories_created += 1
        
        self.stdout.write(f'Income categories: {categories_created} created, {len(default_categories) - categories_created} already existed')
    
    def setup_expense_categories(self):
        # قائمة بفئات المصروفات الافتراضية
        default_categories = [
            {'name': _('مشتريات'), 'description': _('مشتريات قطع غيار وبضائع')},
            {'name': _('رواتب'), 'description': _('رواتب وأجور الموظفين')},
            {'name': _('إيجار'), 'description': _('إيجار المحل أو المستودع')},
            {'name': _('مرافق'), 'description': _('فواتير الكهرباء والماء والهاتف والإنترنت')},
            {'name': _('صيانة'), 'description': _('صيانة المعدات والمباني')},
            {'name': _('تسويق'), 'description': _('مصاريف الإعلان والتسويق')},
            {'name': _('ضرائب'), 'description': _('ضرائب ورسوم حكومية')},
            {'name': _('نقل'), 'description': _('مصاريف النقل والشحن')},
            {'name': _('أخرى'), 'description': _('مصاريف أخرى متنوعة')},
        ]
        
        # إضافة الفئات إذا لم تكن موجودة
        categories_created = 0
        for category_data in default_categories:
            category, created = ExpenseCategory.objects.get_or_create(
                name=category_data['name'],
                defaults={'description': category_data['description']}
            )
            if created:
                categories_created += 1
        
        self.stdout.write(f'Expense categories: {categories_created} created, {len(default_categories) - categories_created} already existed')
    
    def setup_default_account(self):
        # إضافة حساب نقدي افتراضي إذا لم يكن هناك حسابات
        if Account.objects.count() == 0:
            Account.objects.create(
                name=_('الصندوق'),
                account_type='cash',
                initial_balance=0,
                current_balance=0,
                notes=_('الحساب النقدي الافتراضي')
            )
            self.stdout.write('Default cash account created')
        else:
            self.stdout.write('Accounts already exist, skipping default account creation')
