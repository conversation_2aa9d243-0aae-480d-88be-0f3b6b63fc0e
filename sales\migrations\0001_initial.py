# Generated by Django 5.2 on 2025-04-18 16:07

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customers', '0001_initial'),
        ('inventory', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Sale',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(default=uuid.uuid4, max_length=50, unique=True, verbose_name='رقم الفاتورة')),
                ('date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ البيع')),
                ('subtotal', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المجموع الفرعي')),
                ('tax_rate', models.DecimalField(decimal_places=2, default=15.0, max_digits=5, verbose_name='نسبة الضريبة')),
                ('tax_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='مبلغ الضريبة')),
                ('discount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الخصم')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المجموع الكلي')),
                ('payment_method', models.CharField(choices=[('cash', 'نقدي'), ('card', 'بطاقة ائتمان'), ('transfer', 'تحويل بنكي'), ('check', 'شيك'), ('credit', 'آجل')], default='cash', max_length=20, verbose_name='طريقة الدفع')),
                ('status', models.CharField(choices=[('pending', 'معلق'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='completed', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sales', to='customers.customer', verbose_name='العميل')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sales', to=settings.AUTH_USER_MODEL, verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'بيع',
                'verbose_name_plural': 'المبيعات',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ')),
                ('payment_method', models.CharField(choices=[('cash', 'نقدي'), ('card', 'بطاقة ائتمان'), ('transfer', 'تحويل بنكي'), ('check', 'شيك'), ('credit', 'آجل')], max_length=20, verbose_name='طريقة الدفع')),
                ('payment_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الدفع')),
                ('reference', models.CharField(blank=True, max_length=100, null=True, verbose_name='المرجع')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('sale', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='sales.sale', verbose_name='البيع')),
            ],
            options={
                'verbose_name': 'دفعة',
                'verbose_name_plural': 'الدفعات',
                'ordering': ['-payment_date'],
            },
        ),
        migrations.CreateModel(
            name='SaleItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة')),
                ('subtotal', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المجموع الفرعي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sale_items', to='inventory.product', verbose_name='المنتج')),
                ('sale', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='sales.sale', verbose_name='البيع')),
            ],
            options={
                'verbose_name': 'عنصر البيع',
                'verbose_name_plural': 'عناصر البيع',
            },
        ),
    ]
