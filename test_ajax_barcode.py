#!/usr/bin/env python
import requests
import json

def test_ajax_barcode():
    session = requests.Session()
    
    # تسجيل الدخول أولاً
    login_url = "http://127.0.0.1:8000/login/"
    
    # الحصول على CSRF token من صفحة تسجيل الدخول
    login_page = session.get(login_url)
    csrf_token = None
    for line in login_page.text.split('\n'):
        if 'csrfmiddlewaretoken' in line and 'value=' in line:
            start = line.find('value="') + 7
            end = line.find('"', start)
            csrf_token = line[start:end]
            break
    
    if not csrf_token:
        print("Could not find CSRF token")
        return
    
    # تسجيل الدخول
    login_data = {
        'username': 'admin',
        'password': 'admin',
        'csrfmiddlewaretoken': csrf_token
    }
    
    login_response = session.post(login_url, data=login_data)
    print(f"Login status: {login_response.status_code}")
    
    if login_response.status_code != 200:
        print("Login failed")
        return
    
    # الآن اختبار الباركود API
    barcode_url = "http://127.0.0.1:8000/inventory/barcode/scan/"
    
    # الحصول على CSRF token جديد من الكوكيز
    csrf_token = session.cookies.get('csrftoken')
    
    barcode_data = {
        'barcode_number': '1282235856942741'
    }
    
    headers = {
        'X-CSRFToken': csrf_token,
        'Referer': 'http://127.0.0.1:8000/sales/new/',
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    print(f"Testing barcode API: {barcode_url}")
    print(f"Data: {barcode_data}")
    print(f"CSRF Token: {csrf_token}")
    
    try:
        response = session.post(barcode_url, data=barcode_data, headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Text: {response.text}")
        
        if response.headers.get('content-type', '').startswith('application/json'):
            try:
                json_data = response.json()
                print(f"JSON Response: {json.dumps(json_data, indent=2, ensure_ascii=False)}")
            except Exception as e:
                print(f"Could not parse JSON: {e}")
                
    except Exception as e:
        print(f"Error: {e}")

if __name__ == '__main__':
    test_ajax_barcode()
