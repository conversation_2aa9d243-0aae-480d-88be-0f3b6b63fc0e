from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Sum, F
from django.utils import timezone
from datetime import timedelta

from sales.models import Sale, SaleItem
from inventory.models import Product, ProductMovement
from customers.models import Customer
from finance.models import Expense, Income

@login_required
def index(request):
    # Get current date and date ranges
    today = timezone.now().date()
    start_of_week = today - timedelta(days=today.weekday())
    start_of_month = today.replace(day=1)

    # Sales statistics
    total_sales_today = Sale.objects.filter(date__date=today, status='completed').aggregate(Sum('total_amount'))['total_amount__sum'] or 0
    total_sales_week = Sale.objects.filter(date__date__gte=start_of_week, status='completed').aggregate(Sum('total_amount'))['total_amount__sum'] or 0
    total_sales_month = Sale.objects.filter(date__date__gte=start_of_month, status='completed').aggregate(Sum('total_amount'))['total_amount__sum'] or 0

    # Inventory statistics
    total_products = Product.objects.count()
    low_stock_products = Product.objects.filter(quantity__lte=F('min_quantity')).count()
    out_of_stock_products = Product.objects.filter(quantity=0).count()

    # Customer statistics
    total_customers = Customer.objects.count()
    new_customers_month = Customer.objects.filter(created_at__date__gte=start_of_month).count()

    # Financial statistics
    total_expenses_month = Expense.objects.filter(date__gte=start_of_month).aggregate(Sum('amount'))['amount__sum'] or 0
    total_income_month = Income.objects.filter(date__gte=start_of_month).aggregate(Sum('amount'))['amount__sum'] or 0
    profit_month = total_sales_month - total_expenses_month

    # Recent sales
    recent_sales = Sale.objects.order_by('-date')[:10]

    # Top selling products
    top_products = SaleItem.objects.values('product__name').annotate(
        total_quantity=Sum('quantity'),
        total_sales=Sum('subtotal')
    ).order_by('-total_quantity')[:5]

    # Low stock products
    low_stock_items = Product.objects.filter(quantity__lte=F('min_quantity')).order_by('quantity')[:10]

    # Recent product movements
    recent_movements = ProductMovement.objects.order_by('-created_at')[:10]

    context = {
        'total_sales_today': total_sales_today,
        'total_sales_week': total_sales_week,
        'total_sales_month': total_sales_month,
        'total_products': total_products,
        'low_stock_products': low_stock_products,
        'out_of_stock_products': out_of_stock_products,
        'total_customers': total_customers,
        'new_customers_month': new_customers_month,
        'total_expenses_month': total_expenses_month,
        'total_income_month': total_income_month,
        'profit_month': profit_month,
        'recent_sales': recent_sales,
        'top_products': top_products,
        'low_stock_items': low_stock_items,
        'recent_movements': recent_movements,
    }

    return render(request, 'dashboard/index.html', context)
