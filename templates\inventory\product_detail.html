{% extends 'base.html' %}
{% load i18n %}

{% block title %}{{ product.name }} | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .product-image {
        max-width: 100%;
        max-height: 300px;
        object-fit: contain;
        border-radius: 8px;
    }

    .product-info-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .product-info-item {
        margin-bottom: 10px;
        display: flex;
    }

    .product-info-label {
        font-weight: bold;
        min-width: 150px;
    }

    .product-info-value {
        flex: 1;
    }

    .status-badge {
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .status-available {
        background-color: #d1e7dd;
        color: #0f5132;
    }

    .status-low {
        background-color: #fff3cd;
        color: #664d03;
    }

    .status-out {
        background-color: #f8d7da;
        color: #842029;
    }

    .movement-in {
        color: #198754;
    }

    .movement-out {
        color: #dc3545;
    }

    .movement-adjustment {
        color: #0d6efd;
    }

    .barcode-container {
        text-align: center;
        margin-top: 20px;
    }

    .barcode-image {
        max-width: 100%;
        height: 80px;
    }

    .print-barcode-btn {
        margin-top: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{{ product.name }}</h1>
    <div>
        <a href="#" class="btn btn-info me-2" id="printBtn">
            <i class="fas fa-print me-1"></i> {% trans "طباعة" %}
        </a>
        <a href="{% url 'inventory:edit_product' product.id %}" class="btn btn-primary me-2">
            <i class="fas fa-edit me-1"></i> {% trans "تعديل" %}
        </a>
        <a href="{% url 'inventory:index' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى المخزون" %}
        </a>
    </div>
</div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<div class="row">
    <!-- Product Image and Basic Info -->
    <div class="col-md-4 mb-4">
        <div class="card shadow h-100">
            <div class="card-body text-center">
                {% if product.image %}
                <img src="{{ product.image.url }}" alt="{{ product.name }}" class="product-image mb-3">
                {% else %}
                <div class="text-center mb-3">
                    <i class="fas fa-image fa-5x text-muted"></i>
                    <p class="text-muted">{% trans "لا توجد صورة" %}</p>
                </div>
                {% endif %}

                <h4 class="card-title">{{ product.name }}</h4>
                <h6 class="card-subtitle mb-3 text-muted">{{ product.code }}</h6>

                <div class="d-flex justify-content-center mb-3">
                    {% if product.quantity == 0 %}
                    <span class="status-badge status-out">{% trans "نفد من المخزون" %}</span>
                    {% elif product.is_low_stock %}
                    <span class="status-badge status-low">{% trans "منخفض المخزون" %}</span>
                    {% else %}
                    <span class="status-badge status-available">{% trans "متوفر" %}</span>
                    {% endif %}
                </div>

                <div class="barcode-container">
                    <img src="data:image/png;base64,{{ barcode_image }}" alt="Barcode" class="barcode-image">
                    <div class="mt-2">{{ product.code }}</div>
                    <button class="btn btn-sm btn-outline-primary print-barcode-btn" id="printBarcodeBtn">
                        <i class="fas fa-print me-1"></i> {% trans "طباعة الباركود" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Details -->
    <div class="col-md-8 mb-4">
        <div class="card shadow h-100">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "تفاصيل المنتج" %}</h6>
                <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#stockMovementModal">
                    <i class="fas fa-exchange-alt me-1"></i> {% trans "إضافة حركة مخزون" %}
                </button>
            </div>
            <div class="card-body">
                <div class="product-info-section">
                    <h5 class="mb-3">{% trans "المعلومات الأساسية" %}</h5>
                    <div class="product-info-item">
                        <div class="product-info-label">{% trans "كود المنتج:" %}</div>
                        <div class="product-info-value">{{ product.code }}</div>
                    </div>
                    <div class="product-info-item">
                        <div class="product-info-label">{% trans "الفئة:" %}</div>
                        <div class="product-info-value">{{ product.category.name }}</div>
                    </div>
                    <div class="product-info-item">
                        <div class="product-info-label">{% trans "نوع السيارة:" %}</div>
                        <div class="product-info-value">{{ product.car_type.name }} ({{ product.car_type.get_origin_display }})</div>
                    </div>
                    <div class="product-info-item">
                        <div class="product-info-label">{% trans "الوصف:" %}</div>
                        <div class="product-info-value">
                            {% if product.description %}
                            {{ product.description }}
                            {% else %}
                            <span class="text-muted">{% trans "لا يوجد وصف" %}</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="product-info-item">
                        <div class="product-info-label">{% trans "الحالة:" %}</div>
                        <div class="product-info-value">
                            {% if product.is_active %}
                            <span class="badge bg-success">{% trans "نشط" %}</span>
                            {% else %}
                            <span class="badge bg-danger">{% trans "غير نشط" %}</span>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="product-info-section">
                    <h5 class="mb-3">{% trans "معلومات المخزون والتسعير" %}</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="product-info-item">
                                <div class="product-info-label">{% trans "الكمية الحالية:" %}</div>
                                <div class="product-info-value">{{ product.quantity }}</div>
                            </div>
                            <div class="product-info-item">
                                <div class="product-info-label">{% trans "الحد الأدنى للكمية:" %}</div>
                                <div class="product-info-value">{{ product.min_quantity }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="product-info-item">
                                <div class="product-info-label">{% trans "سعر الشراء:" %}</div>
                                <div class="product-info-value">{{ product.purchase_price }} د.م</div>
                            </div>
                            <div class="product-info-item">
                                <div class="product-info-label">{% trans "سعر البيع:" %}</div>
                                <div class="product-info-value">{{ product.selling_price }} د.م</div>
                            </div>
                            <div class="product-info-item">
                                <div class="product-info-label">{% trans "هامش الربح:" %}</div>
                                <div class="product-info-value">{{ product.profit_margin|floatformat:2 }}%</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="product-info-section">
                    <h5 class="mb-3">{% trans "معلومات إضافية" %}</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="product-info-item">
                                <div class="product-info-label">{% trans "تاريخ الإنشاء:" %}</div>
                                <div class="product-info-value">{{ product.created_at|date:"Y-m-d H:i" }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="product-info-item">
                                <div class="product-info-label">{% trans "آخر تحديث:" %}</div>
                                <div class="product-info-value">{{ product.updated_at|date:"Y-m-d H:i" }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stock Movement History -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "سجل حركات المخزون" %}</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover" id="movementsTable">
                <thead>
                    <tr>
                        <th>{% trans "التاريخ" %}</th>
                        <th>{% trans "نوع الحركة" %}</th>
                        <th>{% trans "الكمية" %}</th>
                        <th>{% trans "المرجع" %}</th>
                        <th>{% trans "ملاحظات" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for movement in product.movements.all %}
                    <tr>
                        <td>{{ movement.created_at|date:"Y-m-d H:i" }}</td>
                        <td>
                            {% if movement.movement_type == 'in' %}
                            <span class="movement-in"><i class="fas fa-arrow-up me-1"></i> {% trans "وارد" %}</span>
                            {% elif movement.movement_type == 'out' %}
                            <span class="movement-out"><i class="fas fa-arrow-down me-1"></i> {% trans "صادر" %}</span>
                            {% else %}
                            <span class="movement-adjustment"><i class="fas fa-sync-alt me-1"></i> {% trans "تعديل" %}</span>
                            {% endif %}
                        </td>
                        <td>{{ movement.quantity }}</td>
                        <td>{{ movement.reference|default:"-" }}</td>
                        <td>{{ movement.notes|default:"-" }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="text-center">{% trans "لا توجد حركات مخزون لهذا المنتج" %}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Stock Movement Chart -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "مخطط حركة المخزون" %}</h6>
        <div class="btn-group">
            <button type="button" class="btn btn-sm btn-outline-primary active" id="lastMonthBtn">{% trans "آخر شهر" %}</button>
            <button type="button" class="btn btn-sm btn-outline-primary" id="last3MonthsBtn">{% trans "آخر 3 أشهر" %}</button>
            <button type="button" class="btn btn-sm btn-outline-primary" id="lastYearBtn">{% trans "آخر سنة" %}</button>
        </div>
    </div>
    <div class="card-body">
        <div class="row mb-3">
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h6 class="card-title text-muted">{% trans "متوسط المخزون" %}</h6>
                        <h3 class="mb-0" id="avgStock">{{ avg_stock|default:"0"|floatformat:0 }}</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h6 class="card-title text-muted">{% trans "أعلى مستوى" %}</h6>
                        <h3 class="mb-0 text-success" id="maxStock">{{ max_stock|default:"0" }}</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h6 class="card-title text-muted">{% trans "أدنى مستوى" %}</h6>
                        <h3 class="mb-0 text-danger" id="minStock">{{ min_stock|default:"0" }}</h3>
                    </div>
                </div>
            </div>
        </div>
        <div class="chart-container" style="position: relative; height:300px;">
            <canvas id="stockChart"></canvas>
        </div>
        <div class="mt-3">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-1"></i>
                <span id="stockPrediction">{% trans "بناءً على الاتجاه الحالي، من المتوقع أن ينفد المخزون في غضون" %} <strong>{{ stock_days_remaining|default:"30" }} {% trans "يوم" %}</strong>.</span>
            </div>
        </div>
    </div>
</div>

<!-- Stock Movement Modal -->
<div class="modal fade" id="stockMovementModal" tabindex="-1" aria-labelledby="stockMovementModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="stockMovementModalLabel">{% trans "إضافة حركة مخزون" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'inventory:add_movement' product.id %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="movement_type" class="form-label">{% trans "نوع الحركة" %}</label>
                        <select class="form-select" id="movement_type" name="movement_type" required>
                            <option value="in">{% trans "وارد (إضافة)" %}</option>
                            <option value="out">{% trans "صادر (سحب)" %}</option>
                            <option value="adjustment">{% trans "تعديل" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="quantity" class="form-label">{% trans "الكمية" %}</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" min="1" required>
                    </div>
                    <div class="mb-3">
                        <label for="reference" class="form-label">{% trans "المرجع" %}</label>
                        <input type="text" class="form-control" id="reference" name="reference" placeholder="{% trans 'مثال: فاتورة شراء رقم 123' %}">
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">{% trans "ملاحظات" %}</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "حفظ" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Print Barcode Modal -->
<div class="modal fade" id="printBarcodeModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "طباعة الباركود" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="barcode_count" class="form-label">{% trans "عدد النسخ" %}</label>
                    <input type="number" class="form-control" id="barcode_count" min="1" value="1">
                </div>
                <div class="mb-3">
                    <label for="barcode_size" class="form-label">{% trans "الحجم" %}</label>
                    <select class="form-select" id="barcode_size">
                        <option value="small">{% trans "صغير" %}</option>
                        <option value="medium" selected>{% trans "متوسط" %}</option>
                        <option value="large">{% trans "كبير" %}</option>
                    </select>
                </div>
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="include_price" checked>
                        <label class="form-check-label" for="include_price">
                            {% trans "إظهار السعر" %}
                        </label>
                    </div>
                </div>
                <div class="barcode-preview text-center">
                    <img src="data:image/png;base64,{{ barcode_image }}" alt="Barcode" class="barcode-image">
                    <div class="mt-2">{{ product.code }}</div>
                    <div class="mt-1">{{ product.selling_price }} د.م</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-primary" id="confirmPrintBarcode">{% trans "طباعة" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    $(document).ready(function() {
        // Initialize DataTable for movements
        $('#movementsTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
            },
            "order": [[0, "desc"]],
            "pageLength": 10
        });

        // Stock Movement Chart
        const ctx = document.getElementById('stockChart').getContext('2d');

        // Prepare data for chart
        const allLabels = [];
        const allData = [];
        const allDates = [];
        let currentStock = {{ product.quantity }};

        {% for movement in all_product_movements %}
            allLabels.unshift("{{ movement.created_at|date:'Y-m-d' }}");
            allDates.unshift(new Date("{{ movement.created_at|date:'Y-m-d' }}"));

            {% if movement.movement_type == 'in' %}
                currentStock -= {{ movement.quantity }};
            {% elif movement.movement_type == 'out' %}
                currentStock += {{ movement.quantity }};
            {% endif %}

            allData.unshift(currentStock);
        {% endfor %}

        // Calculate statistics
        function calculateStats(data) {
            if (data.length === 0) return { avg: 0, min: 0, max: 0 };

            const sum = data.reduce((a, b) => a + b, 0);
            const avg = sum / data.length;
            const min = Math.min(...data);
            const max = Math.max(...data);

            return { avg, min, max };
        }

        // Filter data by date range
        function filterDataByDateRange(days) {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - days);

            const filteredLabels = [];
            const filteredData = [];

            for (let i = 0; i < allDates.length; i++) {
                if (allDates[i] >= cutoffDate) {
                    filteredLabels.push(allLabels[i]);
                    filteredData.push(allData[i]);
                }
            }

            // If no data in range, use the most recent data point
            if (filteredData.length === 0 && allData.length > 0) {
                filteredLabels.push(allLabels[0]);
                filteredData.push(allData[0]);
            }

            return { labels: filteredLabels, data: filteredData };
        }

        // Predict days until stock runs out
        function predictDaysUntilStockOut(data, dates) {
            if (data.length < 2) return 30; // Default to 30 days if not enough data

            // Calculate average daily consumption
            const firstDate = new Date(dates[0]);
            const lastDate = new Date(dates[dates.length - 1]);
            const daysDiff = (lastDate - firstDate) / (1000 * 60 * 60 * 24);

            if (daysDiff <= 0) return 30;

            const totalConsumption = data[0] - data[data.length - 1];
            const dailyConsumption = totalConsumption / daysDiff;

            if (dailyConsumption <= 0) return 999; // Stock is not decreasing

            // Calculate days until stock runs out
            const currentStock = data[data.length - 1];
            const daysRemaining = Math.ceil(currentStock / dailyConsumption);

            return daysRemaining > 0 ? daysRemaining : 0;
        }

        // Initialize chart with last month data
        const initialData = filterDataByDateRange(30);
        const stats = calculateStats(initialData.data);

        // Update stats display
        $('#avgStock').text(Math.round(stats.avg));
        $('#maxStock').text(stats.max);
        $('#minStock').text(stats.min);

        // Calculate days until stock runs out
        const daysRemaining = predictDaysUntilStockOut(initialData.data, initialData.labels);
        $('#stockPrediction').html(`{% trans "بناءً على الاتجاه الحالي، من المتوقع أن ينفد المخزون في غضون" %} <strong>${daysRemaining} {% trans "يوم" %}</strong>.`);

        // Create chart
        let stockChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: initialData.labels,
                datasets: [{
                    label: '{% trans "مستوى المخزون" %}',
                    data: initialData.data,
                    backgroundColor: 'rgba(78, 115, 223, 0.05)',
                    borderColor: 'rgba(78, 115, 223, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(78, 115, 223, 1)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 1,
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    fill: true,
                    tension: 0.1
                }, {
                    label: '{% trans "الحد الأدنى" %}',
                    data: Array(initialData.labels.length).fill({{ product.min_quantity }}),
                    borderColor: 'rgba(255, 193, 7, 1)',
                    borderWidth: 2,
                    borderDash: [5, 5],
                    pointRadius: 0,
                    fill: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            drawBorder: false,
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + context.raw;
                            }
                        }
                    }
                }
            }
        });

        // Date range buttons
        $('#lastMonthBtn').click(function() {
            updateChart(30);
            $('.btn-outline-primary').removeClass('active');
            $(this).addClass('active');
        });

        $('#last3MonthsBtn').click(function() {
            updateChart(90);
            $('.btn-outline-primary').removeClass('active');
            $(this).addClass('active');
        });

        $('#lastYearBtn').click(function() {
            updateChart(365);
            $('.btn-outline-primary').removeClass('active');
            $(this).addClass('active');
        });

        // Update chart with new date range
        function updateChart(days) {
            const filteredData = filterDataByDateRange(days);
            const stats = calculateStats(filteredData.data);

            // Update chart data
            stockChart.data.labels = filteredData.labels;
            stockChart.data.datasets[0].data = filteredData.data;
            stockChart.data.datasets[1].data = Array(filteredData.labels.length).fill({{ product.min_quantity }});
            stockChart.update();

            // Update stats display
            $('#avgStock').text(Math.round(stats.avg));
            $('#maxStock').text(stats.max);
            $('#minStock').text(stats.min);

            // Update prediction
            const daysRemaining = predictDaysUntilStockOut(filteredData.data, filteredData.labels);
            $('#stockPrediction').html(`{% trans "بناءً على الاتجاه الحالي، من المتوقع أن ينفد المخزون في غضون" %} <strong>${daysRemaining} {% trans "يوم" %}</strong>.`);
        }

        // Print button
        $('#printBtn').click(function() {
            window.print();
        });

        // Print Barcode button
        $('#printBarcodeBtn').click(function() {
            $('#printBarcodeModal').modal('show');
        });

        // Confirm Print Barcode
        $('#confirmPrintBarcode').click(function() {
            const count = $('#barcode_count').val();
            const size = $('#barcode_size').val();
            const includePrice = $('#include_price').is(':checked');

            // Redirect to barcode print page
            const printUrl = `{% url 'inventory:print_barcode' product.id %}?count=${count}&size=${size}&include_price=${includePrice}`;
            console.log('Opening print URL:', printUrl);

            // Open in a new window with specific features for printing
            const printWindow = window.open(
                printUrl,
                '_blank',
                'width=800,height=600,menubar=no,toolbar=no,location=no,status=no'
            );

            if (printWindow) {
                // Focus on the new window
                printWindow.focus();
            } else {
                // If popup was blocked, alert the user
                alert('{% trans "يرجى السماح بالنوافذ المنبثقة لطباعة الباركود" %}');
            }

            $('#printBarcodeModal').modal('hide');
        });

        // Form validation for stock movement
        $('#stockMovementModal form').submit(function(e) {
            const movementType = $('#movement_type').val();
            const quantity = parseInt($('#quantity').val());
            const currentStock = {{ product.quantity }};

            if (movementType === 'out' && quantity > currentStock) {
                e.preventDefault();
                alert('{% trans "الكمية المطلوبة أكبر من الكمية المتوفرة في المخزون" %}');
                return false;
            }

            return true;
        });
    });
</script>
{% endblock %}
