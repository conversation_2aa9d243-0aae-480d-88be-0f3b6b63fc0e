import os
import json
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator

class SystemSetting(models.Model):
    key = models.CharField(_('المفتاح'), max_length=100, unique=True)
    value = models.TextField(_('القيمة'))
    description = models.TextField(_('الوصف'), blank=True, null=True)
    is_public = models.BooleanField(_('عام'), default=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('إعداد النظام')
        verbose_name_plural = _('إعدادات النظام')
        ordering = ['key']

    def __str__(self):
        return self.key

class CompanyInfo(models.Model):
    name = models.CharField(_('اسم الشركة'), max_length=200)
    logo = models.ImageField(_('الشعار'), upload_to='company/', blank=True, null=True)
    address = models.TextField(_('العنوان'))
    phone = models.CharField(_('رقم الهاتف'), max_length=20)
    email = models.EmailField(_('البريد الإلكتروني'))
    website = models.URLField(_('الموقع الإلكتروني'), blank=True, null=True)
    tax_number = models.CharField(_('الرقم الضريبي'), max_length=50, blank=True, null=True)
    commercial_register = models.CharField(_('رقم السجل التجاري'), max_length=50, blank=True, null=True)
    footer_text = models.TextField(_('نص التذييل'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('معلومات الشركة')
        verbose_name_plural = _('معلومات الشركة')

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        # Ensure only one instance exists
        if not self.pk and CompanyInfo.objects.exists():
            # Update existing instance instead of creating a new one
            existing = CompanyInfo.objects.first()
            self.pk = existing.pk
        super().save(*args, **kwargs)

class BackupLog(models.Model):
    BACKUP_TYPE_CHOICES = (
        ('auto', _('تلقائي')),
        ('manual', _('يدوي')),
        ('scheduled', _('مجدول')),
    )

    STATUS_CHOICES = (
        ('success', _('ناجح')),
        ('failed', _('فاشل')),
        ('in_progress', _('جاري')),
    )

    STORAGE_TYPE_CHOICES = (
        ('local', _('محلي')),
        ('google_drive', _('Google Drive')),
        ('dropbox', _('Dropbox')),
    )

    file_name = models.CharField(_('اسم الملف'), max_length=255)
    file_path = models.CharField(_('مسار الملف'), max_length=255)
    file_size = models.PositiveIntegerField(_('حجم الملف'), blank=True, null=True)
    backup_type = models.CharField(_('نوع النسخ الاحتياطي'), max_length=10, choices=BACKUP_TYPE_CHOICES)
    status = models.CharField(_('الحالة'), max_length=15, choices=STATUS_CHOICES)
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)

    # حقول جديدة لدعم التشفير والتخزين السحابي
    checksum = models.CharField(_('التحقق من السلامة'), max_length=64, blank=True, null=True)
    is_encrypted = models.BooleanField(_('مشفر'), default=False)
    encryption_key = models.TextField(_('مفتاح التشفير'), blank=True, null=True)
    cloud_storage_type = models.CharField(_('نوع التخزين السحابي'), max_length=20, choices=STORAGE_TYPE_CHOICES, default='local')
    cloud_storage_id = models.CharField(_('معرف التخزين السحابي'), max_length=255, blank=True, null=True)
    cloud_storage_link = models.URLField(_('رابط التخزين السحابي'), blank=True, null=True)
    include_media = models.BooleanField(_('تضمين الوسائط'), default=True)

    class Meta:
        verbose_name = _('سجل النسخ الاحتياطي')
        verbose_name_plural = _('سجلات النسخ الاحتياطي')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.file_name} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"

    def get_size_display(self):
        """
        عرض حجم الملف بشكل مقروء
        """
        if not self.file_size:
            return "-"

        # تحويل الحجم إلى وحدات مناسبة
        size_bytes = self.file_size
        if size_bytes < 1024:
            return f"{size_bytes} بايت"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.2f} كيلوبايت"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.2f} ميجابايت"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.2f} جيجابايت"

    def is_available(self):
        """
        التحقق من توفر النسخة الاحتياطية (محليًا أو في السحابة)
        """
        if os.path.exists(self.file_path):
            return True
        elif self.cloud_storage_id and self.cloud_storage_type != 'local':
            return True
        return False

class NotificationSetting(models.Model):
    EVENT_CHOICES = (
        ('low_stock', _('مخزون منخفض')),
        ('new_order', _('طلب جديد')),
        ('payment_received', _('تم استلام الدفع')),
        ('order_status_change', _('تغيير حالة الطلب')),
    )

    event = models.CharField(_('الحدث'), max_length=50, choices=EVENT_CHOICES, unique=True)
    email_notification = models.BooleanField(_('إشعار بريد إلكتروني'), default=True)
    system_notification = models.BooleanField(_('إشعار نظام'), default=True)
    email_template = models.TextField(_('قالب البريد الإلكتروني'), blank=True, null=True)
    recipients = models.TextField(_('المستلمون'), blank=True, null=True, help_text=_('قائمة بريد إلكتروني مفصولة بفواصل'))
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('إعداد الإشعارات')
        verbose_name_plural = _('إعدادات الإشعارات')
        ordering = ['event']

    def __str__(self):
        return self.get_event_display()

class CurrencySetting(models.Model):
    CURRENCY_CHOICES = (
        ('MAD', _('درهم مغربي')),
        ('USD', _('دولار أمريكي')),
        ('EUR', _('يورو')),
        ('SAR', _('ريال سعودي')),
        ('AED', _('درهم إماراتي')),
        ('EGP', _('جنيه مصري')),
    )

    POSITION_CHOICES = (
        ('before', _('قبل المبلغ')),
        ('after', _('بعد المبلغ')),
    )

    currency = models.CharField(_('العملة'), max_length=3, choices=CURRENCY_CHOICES, default='MAD')
    symbol = models.CharField(_('الرمز'), max_length=5, default='د.م.')
    position = models.CharField(_('موضع الرمز'), max_length=10, choices=POSITION_CHOICES, default='after')
    decimal_places = models.PositiveSmallIntegerField(_('منازل عشرية'), default=2)
    thousands_separator = models.CharField(_('فاصل الآلاف'), max_length=1, default=',')
    decimal_separator = models.CharField(_('فاصل العشري'), max_length=1, default='.')
    is_default = models.BooleanField(_('افتراضي'), default=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('إعداد العملة')
        verbose_name_plural = _('إعدادات العملة')

    def __str__(self):
        return f"{self.get_currency_display()} ({self.symbol})"

    def save(self, *args, **kwargs):
        # Ensure only one default currency
        if self.is_default:
            CurrencySetting.objects.exclude(pk=self.pk).update(is_default=False)
        super().save(*args, **kwargs)

class TaxSetting(models.Model):
    TAX_TYPE_CHOICES = (
        ('inclusive', _('شامل')),
        ('exclusive', _('إضافي')),
    )

    name = models.CharField(_('اسم الضريبة'), max_length=100, default='ضريبة القيمة المضافة')
    rate = models.DecimalField(_('نسبة الضريبة'), max_digits=5, decimal_places=2, validators=[MinValueValidator(0), MaxValueValidator(100)])
    tax_type = models.CharField(_('نوع الضريبة'), max_length=10, choices=TAX_TYPE_CHOICES, default='exclusive')
    is_enabled = models.BooleanField(_('مفعل'), default=True)
    tax_number = models.CharField(_('الرقم الضريبي'), max_length=50, blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('إعداد الضريبة')
        verbose_name_plural = _('إعدادات الضريبة')

    def __str__(self):
        return f"{self.name} ({self.rate}%)"

class InvoiceSetting(models.Model):
    # خيارات تنسيق رقم الفاتورة
    NUMBER_FORMAT_CHOICES = (
        ('simple', _('بسيط (مثال: INV-1001)')),
        ('date', _('مع التاريخ (مثال: INV-YYYY-MM-0001)')),
        ('year', _('مع السنة (مثال: INV-YYYY-0001)')),
        ('month', _('مع الشهر (مثال: INV-MM-0001)')),
    )

    # خيارات حجم الورق
    PAPER_SIZE_CHOICES = (
        ('a4', _('A4')),
        ('a5', _('A5')),
        ('letter', _('Letter')),
        ('thermal', _('إيصال حراري (80مم)')),
    )

    # خيارات الخط
    FONT_CHOICES = (
        ('tajawal', _('Tajawal')),
        ('cairo', _('Cairo')),
        ('noto', _('Noto Sans Arabic')),
        ('droid', _('Droid Arabic Naskh')),
    )

    # خيارات إعادة تعيين التسلسل
    RESET_SEQUENCE_CHOICES = (
        ('never', _('أبداً')),
        ('yearly', _('سنوياً')),
        ('monthly', _('شهرياً')),
    )

    # إعدادات أساسية
    prefix = models.CharField(_('بادئة الفاتورة'), max_length=10, default='INV-', blank=True, null=True)
    next_number = models.PositiveIntegerField(_('الرقم التالي'), default=1001, blank=True, null=True)
    number_format = models.CharField(_('تنسيق الرقم'), max_length=10, choices=NUMBER_FORMAT_CHOICES, default='simple', blank=True, null=True)
    reset_sequence = models.CharField(_('إعادة تعيين التسلسل'), max_length=10, choices=RESET_SEQUENCE_CHOICES, default='never', blank=True, null=True)
    last_reset_date = models.DateField(_('تاريخ آخر إعادة تعيين'), null=True, blank=True)

    # إعدادات المحتوى
    footer_text = models.TextField(_('نص التذييل'), blank=True, null=True)
    terms_and_conditions = models.TextField(_('الشروط والأحكام'), blank=True, null=True)
    default_notes = models.TextField(_('ملاحظات افتراضية'), blank=True, null=True)
    show_logo = models.BooleanField(_('إظهار الشعار'), default=True)
    show_tax = models.BooleanField(_('إظهار الضريبة'), default=True)
    show_tax_summary = models.BooleanField(_('إظهار ملخص الضريبة'), default=True)
    show_customer_info = models.BooleanField(_('إظهار معلومات العميل'), default=True)
    show_signature = models.BooleanField(_('إظهار التوقيع'), default=True)
    show_qr_code = models.BooleanField(_('إظهار رمز QR'), default=True)
    show_terms = models.BooleanField(_('إظهار الشروط والأحكام'), default=True)

    # إعدادات محتوى الفاتورة
    show_invoice_number = models.BooleanField(_('إظهار رقم الفاتورة'), default=True)
    show_customer = models.BooleanField(_('إظهار العميل'), default=True)
    show_sale_date = models.BooleanField(_('إظهار تاريخ البيع'), default=True)
    show_employee = models.BooleanField(_('إظهار الموظف'), default=True)
    show_status = models.BooleanField(_('إظهار الحالة'), default=True)
    show_payment_method = models.BooleanField(_('إظهار طريقة الدفع'), default=True)
    show_created_at = models.BooleanField(_('إظهار تاريخ الإنشاء'), default=True)
    show_updated_at = models.BooleanField(_('إظهار آخر تحديث'), default=True)

    # إعدادات محتوى المنتجات
    show_product_code = models.BooleanField(_('إظهار كود المنتج'), default=True)
    show_product_description = models.BooleanField(_('إظهار وصف المنتج'), default=True)
    show_unit_price = models.BooleanField(_('إظهار سعر الوحدة'), default=True)
    show_quantity = models.BooleanField(_('إظهار الكمية'), default=True)

    # معلومات إضافية
    invoice_title = models.CharField(_('عنوان الفاتورة'), max_length=100, default='فاتورة مبيعات', blank=True, null=True)
    invoice_subtitle = models.CharField(_('العنوان الفرعي'), max_length=200, default='شكراً لتعاملكم معنا', blank=True, null=True)

    # إعدادات التصميم
    logo = models.ImageField(_('شعار الشركة'), upload_to='company_logos/', null=True, blank=True)
    primary_color = models.CharField(_('اللون الرئيسي'), max_length=7, default='#4e73df', blank=True, null=True)
    secondary_color = models.CharField(_('اللون الثانوي'), max_length=7, default='#f8f9fa', blank=True, null=True)
    font_family = models.CharField(_('نوع الخط'), max_length=10, choices=FONT_CHOICES, default='tajawal', blank=True, null=True)

    # إعدادات الطباعة
    paper_size = models.CharField(_('حجم الورق'), max_length=10, choices=PAPER_SIZE_CHOICES, default='a4', blank=True, null=True)
    print_copies = models.PositiveSmallIntegerField(_('عدد النسخ'), default=1)
    margin_top = models.PositiveSmallIntegerField(_('الهامش العلوي'), default=10)
    margin_right = models.PositiveSmallIntegerField(_('الهامش الأيمن'), default=10)
    margin_bottom = models.PositiveSmallIntegerField(_('الهامش السفلي'), default=10)
    margin_left = models.PositiveSmallIntegerField(_('الهامش الأيسر'), default=10)
    auto_print = models.BooleanField(_('طباعة تلقائية'), default=True)
    print_background = models.BooleanField(_('طباعة الخلفيات'), default=True)
    print_header_footer = models.BooleanField(_('طباعة رأس وتذييل الصفحة'), default=True)

    # إعدادات البريد الإلكتروني
    email_subject_template = models.CharField(_('قالب عنوان البريد'), max_length=255, default='فاتورة رقم {invoice_number}', blank=True, null=True)
    email_body_template = models.TextField(_('قالب نص البريد'), default='مرحباً {customer_name},\n\nمرفق فاتورة المبيعات الخاصة بكم رقم {invoice_number}.\n\nشكراً لتعاملكم معنا.\n\n{company_name}', blank=True, null=True)
    auto_send_email = models.BooleanField(_('إرسال تلقائي للبريد'), default=False)

    # إحصائيات
    invoices_count = models.PositiveIntegerField(_('عدد الفواتير المصدرة'), default=0)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)
    updated_by = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_('تم التحديث بواسطة'), related_name='invoice_settings_updates')

    class Meta:
        verbose_name = _('إعداد الفاتورة')
        verbose_name_plural = _('إعدادات الفاتورة')

    def __str__(self):
        return f"إعدادات الفاتورة - {self.prefix}{self.next_number}"

    def save(self, *args, **kwargs):
        # Ensure only one instance exists
        if not self.pk and InvoiceSetting.objects.exists():
            # Update existing instance instead of creating a new one
            existing = InvoiceSetting.objects.first()
            self.pk = existing.pk

        # Check if this is an update to an existing record
        if self.pk:
            # Get the old instance to compare changes
            old_instance = InvoiceSetting.objects.get(pk=self.pk)

            # Create a change log entry if there are changes
            changes = []
            for field in self._meta.fields:
                if field.name not in ['updated_at', 'created_at']:
                    old_value = getattr(old_instance, field.name)
                    new_value = getattr(self, field.name)
                    if old_value != new_value:
                        changes.append({
                            'field': str(field.verbose_name),
                            'old_value': str(old_value),
                            'new_value': str(new_value)
                        })

            if changes:
                # Create a change log entry
                InvoiceSettingChangeLog.objects.create(
                    invoice_setting=self,
                    changes=json.dumps(changes, ensure_ascii=False),
                    user=self.updated_by
                )

        super().save(*args, **kwargs)

class InvoiceSettingChangeLog(models.Model):
    invoice_setting = models.ForeignKey(InvoiceSetting, on_delete=models.CASCADE, related_name='change_logs', verbose_name=_('إعداد الفاتورة'))
    changes = models.TextField(_('التغييرات'))
    user = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_('المستخدم'))
    created_at = models.DateTimeField(_('تاريخ التغيير'), auto_now_add=True)

    class Meta:
        verbose_name = _('سجل تغييرات إعدادات الفاتورة')
        verbose_name_plural = _('سجلات تغييرات إعدادات الفاتورة')
        ordering = ['-created_at']

    def __str__(self):
        return f"تغيير في {self.created_at.strftime('%Y-%m-%d %H:%M')} بواسطة {self.user.username if self.user else 'غير معروف'}"

    def get_changes_dict(self):
        """استرجاع التغييرات كقاموس"""
        try:
            return json.loads(self.changes)
        except:
            return []


class EmailSetting(models.Model):
    smtp_host = models.CharField(_('مضيف SMTP'), max_length=100)
    smtp_port = models.PositiveIntegerField(_('منفذ SMTP'), default=587)
    smtp_username = models.CharField(_('اسم مستخدم SMTP'), max_length=100)
    smtp_password = models.CharField(_('كلمة مرور SMTP'), max_length=100)
    smtp_use_tls = models.BooleanField(_('استخدام TLS'), default=True)
    from_email = models.EmailField(_('البريد الإلكتروني المرسل'))
    from_name = models.CharField(_('اسم المرسل'), max_length=100)
    is_enabled = models.BooleanField(_('مفعل'), default=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('إعداد البريد الإلكتروني')
        verbose_name_plural = _('إعدادات البريد الإلكتروني')

    def __str__(self):
        return self.from_email

    def save(self, *args, **kwargs):
        # Ensure only one instance exists
        if not self.pk and EmailSetting.objects.exists():
            # Update existing instance instead of creating a new one
            existing = EmailSetting.objects.first()
            self.pk = existing.pk
        super().save(*args, **kwargs)

class RestoreLog(models.Model):
    STATUS_CHOICES = (
        ('success', _('ناجح')),
        ('failed', _('فاشل')),
        ('in_progress', _('جاري')),
    )

    backup_log = models.ForeignKey(BackupLog, on_delete=models.SET_NULL, null=True, verbose_name=_('سجل النسخ الاحتياطي'))
    status = models.CharField(_('الحالة'), max_length=15, choices=STATUS_CHOICES)
    is_partial = models.BooleanField(_('استعادة جزئية'), default=False)
    restored_items = models.TextField(_('العناصر المستعادة'), blank=True, null=True)
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)

    class Meta:
        verbose_name = _('سجل الاستعادة')
        verbose_name_plural = _('سجلات الاستعادة')
        ordering = ['-created_at']

    def __str__(self):
        backup_name = self.backup_log.file_name if self.backup_log else "غير معروف"
        return f"استعادة {backup_name} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"

class DeleteLog(models.Model):
    STATUS_CHOICES = (
        ('success', _('ناجح')),
        ('failed', _('فاشل')),
    )

    backup_filename = models.CharField(_('اسم ملف النسخة الاحتياطية'), max_length=255)
    backup_type = models.CharField(_('نوع النسخ الاحتياطي'), max_length=20)
    backup_date = models.DateTimeField(_('تاريخ النسخة الاحتياطية'), blank=True, null=True)
    status = models.CharField(_('الحالة'), max_length=15, choices=STATUS_CHOICES)
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)

    class Meta:
        verbose_name = _('سجل الحذف')
        verbose_name_plural = _('سجلات الحذف')
        ordering = ['-created_at']

    def __str__(self):
        return f"حذف {self.backup_filename} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"

class BackupSetting(models.Model):
    FREQUENCY_CHOICES = (
        ('daily', _('يومي')),
        ('weekly', _('أسبوعي')),
        ('monthly', _('شهري')),
    )

    STORAGE_CHOICES = (
        ('local', _('محلي')),
        ('google_drive', _('Google Drive')),
        ('dropbox', _('Dropbox')),
    )

    ENCRYPTION_ALGORITHM_CHOICES = (
        ('aes256', _('AES-256')),
        ('aes192', _('AES-192')),
        ('aes128', _('AES-128')),
    )

    is_auto_backup = models.BooleanField(_('نسخ احتياطي تلقائي'), default=True)
    frequency = models.CharField(_('التكرار'), max_length=10, choices=FREQUENCY_CHOICES, default='daily')
    backup_time = models.TimeField(_('وقت النسخ الاحتياطي'), default='00:00')
    storage_type = models.CharField(_('نوع التخزين'), max_length=20, choices=STORAGE_CHOICES, default='local')
    storage_path = models.CharField(_('مسار التخزين'), max_length=255, blank=True, null=True)
    retention_days = models.PositiveIntegerField(_('أيام الاحتفاظ'), default=30)
    include_media = models.BooleanField(_('تضمين الوسائط'), default=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    # حقول جديدة لدعم التشفير والميزات المتقدمة
    encrypt_backups = models.BooleanField(_('تشفير النسخ الاحتياطية'), default=False)
    encryption_algorithm = models.CharField(_('خوارزمية التشفير'), max_length=10, choices=ENCRYPTION_ALGORITHM_CHOICES, default='aes256')
    keep_min_backups = models.PositiveIntegerField(_('الحد الأدنى للنسخ الاحتياطية'), default=5, help_text=_('الحد الأدنى لعدد النسخ الاحتياطية للاحتفاظ بها'))
    notify_on_success = models.BooleanField(_('إشعار عند النجاح'), default=False)
    notify_on_failure = models.BooleanField(_('إشعار عند الفشل'), default=True)
    notification_email = models.EmailField(_('البريد الإلكتروني للإشعارات'), blank=True, null=True)

    class Meta:
        verbose_name = _('إعداد النسخ الاحتياطي')
        verbose_name_plural = _('إعدادات النسخ الاحتياطي')

    def __str__(self):
        return f"إعدادات النسخ الاحتياطي - {self.get_frequency_display()}"

    def save(self, *args, **kwargs):
        # Ensure only one instance exists
        if not self.pk and BackupSetting.objects.exists():
            # Update existing instance instead of creating a new one
            existing = BackupSetting.objects.first()
            self.pk = existing.pk
        super().save(*args, **kwargs)

class LanguageSetting(models.Model):
    LANGUAGE_CHOICES = (
        ('ar', _('العربية')),
        ('en', _('الإنجليزية')),
        ('fr', _('الفرنسية')),
    )

    DIRECTION_CHOICES = (
        ('rtl', _('من اليمين إلى اليسار')),
        ('ltr', _('من اليسار إلى اليمين')),
    )

    language = models.CharField(_('اللغة'), max_length=2, choices=LANGUAGE_CHOICES, default='ar')
    direction = models.CharField(_('اتجاه النص'), max_length=3, choices=DIRECTION_CHOICES, default='rtl')
    is_default = models.BooleanField(_('افتراضي'), default=True)
    is_enabled = models.BooleanField(_('مفعل'), default=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('إعداد اللغة')
        verbose_name_plural = _('إعدادات اللغة')

    def __str__(self):
        return self.get_language_display()

    def save(self, *args, **kwargs):
        # Ensure only one default language
        if self.is_default:
            LanguageSetting.objects.exclude(pk=self.pk).update(is_default=False)
        super().save(*args, **kwargs)

class OperationLog(models.Model):
    OPERATION_TYPE_CHOICES = (
        ('backup', _('نسخ احتياطي')),
        ('restore', _('استعادة')),
        ('delete', _('حذف')),
    )

    STATUS_CHOICES = (
        ('success', _('ناجح')),
        ('failed', _('فاشل')),
        ('in_progress', _('جاري')),
    )

    operation_type = models.CharField(_('نوع العملية'), max_length=20, choices=OPERATION_TYPE_CHOICES)
    status = models.CharField(_('الحالة'), max_length=15, choices=STATUS_CHOICES)
    user = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_('المستخدم'))
    file_name = models.CharField(_('اسم الملف'), max_length=255, null=True, blank=True)
    details = models.TextField(_('التفاصيل'), null=True, blank=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    ip_address = models.GenericIPAddressField(_('عنوان IP'), null=True, blank=True)
    backup_log = models.ForeignKey(BackupLog, on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_('سجل النسخ الاحتياطي'))
    restore_log = models.ForeignKey(RestoreLog, on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_('سجل الاستعادة'))

    class Meta:
        verbose_name = _('سجل العمليات')
        verbose_name_plural = _('سجلات العمليات')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.get_operation_type_display()} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"

    @classmethod
    def log_operation(cls, operation_type, status, user=None, file_name=None, details=None, ip_address=None, backup_log=None, restore_log=None):
        """تسجيل عملية في سجل العمليات"""
        return cls.objects.create(
            operation_type=operation_type,
            status=status,
            user=user,
            file_name=file_name,
            details=details,
            ip_address=ip_address,
            backup_log=backup_log,
            restore_log=restore_log
        )
