<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% trans "طباعة الباركود" %} - {{ product.name }}</title>
    <style>
        @media print {
            body {
                margin: 0;
                padding: 0;
            }

            .no-print {
                display: none !important;
            }

            .page-break {
                page-break-after: always;
            }
        }

        body {
            font-family: Arial, sans-serif;
        }

        .print-header {
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .barcode-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
            padding: 10px;
        }

        .barcode-item {
            text-align: center;
            border: 1px dashed #ccc;
            margin-bottom: 10px;
            padding: 5px;
        }

        .barcode-item.small {
            width: 100px;
            height: 70px;
        }

        .barcode-item.medium {
            width: 150px;
            height: 100px;
        }

        .barcode-item.large {
            width: 200px;
            height: 130px;
        }

        .barcode-image {
            max-width: 100%;
            height: auto;
        }

        .barcode-code {
            font-size: 10px;
            margin-top: 2px;
        }

        .barcode-name {
            font-size: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-top: 2px;
        }

        .barcode-price {
            font-weight: bold;
            font-size: 12px;
            margin-top: 2px;
        }

        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background-color: #0d6efd;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }

        .print-button:hover {
            background-color: #0b5ed7;
        }

        .print-controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
        }

        .print-controls button {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }

        .print-now {
            background-color: #198754;
            color: white;
        }

        .cancel-print {
            background-color: #dc3545;
            color: white;
        }
    </style>
</head>
<body>
    <div class="print-header no-print">
        <h1>{% trans "طباعة الباركود" %} - {{ product.name }}</h1>
        <p>{% trans "عدد النسخ:" %} {{ count }} | {% trans "الحجم:" %}
            {% if size == 'small' %}{% trans "صغير" %}
            {% elif size == 'medium' %}{% trans "متوسط" %}
            {% else %}{% trans "كبير" %}
            {% endif %}
        </p>

        <div class="print-controls">
            <button class="print-now" onclick="printBarcodes()">{% trans "طباعة الآن" %}</button>
            <button class="cancel-print" onclick="window.close()">{% trans "إلغاء" %}</button>
        </div>

        <button class="print-button" onclick="printBarcodes()">{% trans "طباعة" %}</button>
    </div>

    <div class="barcode-container">
        {% for i in count_range %}
        <div class="barcode-item {{ size }}">
            <img src="data:image/png;base64,{{ barcode_image }}" alt="Barcode" class="barcode-image">
            <div class="barcode-code">{{ product.code }}</div>
            <div class="barcode-name">{{ product.name }}</div>
            {% if include_price %}
            <div class="barcode-price">{{ product.selling_price }} د.م</div>
            {% endif %}
        </div>
        {% endfor %}
    </div>

    <script>
        // Function to print barcodes
        function printBarcodes() {
            // Print the page
            window.print();
        }

        // Auto print when page loads after a short delay
        window.onload = function() {
            // Check if window was opened by user action (not by script)
            if (window.opener) {
                setTimeout(function() {
                    printBarcodes();
                }, 800);
            }
        };
    </script>
</body>
</html>
