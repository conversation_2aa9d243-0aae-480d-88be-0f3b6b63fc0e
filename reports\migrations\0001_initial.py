# Generated by Django 5.2 on 2025-04-18 16:07

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ReportExport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('report_type', models.CharField(choices=[('sales', 'المبيعات'), ('inventory', 'المخزون'), ('customers', 'العملاء'), ('purchases', 'المشتريات'), ('financial', 'المالية'), ('employees', 'الموظفين'), ('custom', 'مخصص')], max_length=20, verbose_name='نوع التقرير')),
                ('parameters', models.JSONField(blank=True, null=True, verbose_name='المعاملات')),
                ('format', models.CharField(choices=[('pdf', 'PDF'), ('excel', 'Excel'), ('csv', 'CSV')], max_length=10, verbose_name='التنسيق')),
                ('file', models.FileField(upload_to='reports/', verbose_name='الملف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='report_exports', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
            ],
            options={
                'verbose_name': 'تصدير التقرير',
                'verbose_name_plural': 'تصديرات التقارير',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SavedReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('report_type', models.CharField(choices=[('sales', 'المبيعات'), ('inventory', 'المخزون'), ('customers', 'العملاء'), ('purchases', 'المشتريات'), ('financial', 'المالية'), ('employees', 'الموظفين'), ('custom', 'مخصص')], max_length=20, verbose_name='نوع التقرير')),
                ('parameters', models.JSONField(blank=True, null=True, verbose_name='المعاملات')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('is_public', models.BooleanField(default=False, verbose_name='عام')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='saved_reports', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
            ],
            options={
                'verbose_name': 'تقرير محفوظ',
                'verbose_name_plural': 'التقارير المحفوظة',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ScheduledReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('frequency', models.CharField(choices=[('daily', 'يومي'), ('weekly', 'أسبوعي'), ('monthly', 'شهري'), ('quarterly', 'ربع سنوي')], max_length=20, verbose_name='التكرار')),
                ('recipients', models.TextField(help_text='قائمة بريد إلكتروني مفصولة بفواصل', verbose_name='المستلمون')),
                ('subject', models.CharField(max_length=200, verbose_name='الموضوع')),
                ('message', models.TextField(blank=True, null=True, verbose_name='الرسالة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('last_sent', models.DateTimeField(blank=True, null=True, verbose_name='آخر إرسال')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='scheduled_reports', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('saved_report', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedules', to='reports.savedreport', verbose_name='التقرير المحفوظ')),
            ],
            options={
                'verbose_name': 'تقرير مجدول',
                'verbose_name_plural': 'التقارير المجدولة',
                'ordering': ['saved_report__name'],
            },
        ),
    ]
