{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "تعديل بيانات العميل" %} | {{ customer.name }} | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .required-field::after {
        content: " *";
        color: red;
    }

    .form-card {
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    }

    .form-card .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e3e6f0;
    }

    .btn-submit {
        min-width: 120px;
    }

    .help-text {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .customer-info {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .customer-info .label {
        font-weight: bold;
        color: #4e73df;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "تعديل بيانات العميل" %}</h1>
    <div>
        <a href="{% url 'customers:view_customer' customer_id=customer.id %}" class="btn btn-info me-2">
            <i class="fas fa-eye me-1"></i> {% trans "عرض التفاصيل" %}
        </a>
        <a href="{% url 'customers:index' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى قائمة العملاء" %}
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow form-card mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "معلومات العميل" %}</h6>
            </div>
            <div class="card-body">
                <div class="customer-info">
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <div class="label">{% trans "رقم العميل" %}:</div>
                            <div>{{ customer.id }}</div>
                        </div>
                        <div class="col-md-6 mb-2">
                            <div class="label">{% trans "تاريخ الإضافة" %}:</div>
                            <div>{{ customer.created_at|date:"Y-m-d" }}</div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <div class="label">{% trans "إجمالي المشتريات" %}:</div>
                            <div>{{ customer.total_purchases }} {% trans "عملية" %}</div>
                        </div>
                        <div class="col-md-6 mb-2">
                            <div class="label">{% trans "إجمالي المبالغ" %}:</div>
                            <div>{{ customer.total_amount_spent|floatformat:2 }} {% trans "د.م" %}</div>
                        </div>
                    </div>
                </div>

                <form method="post" id="customerForm">
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.name.id_for_label }}" class="form-label required-field">{% trans "الاسم" %}</label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.phone.id_for_label }}" class="form-label required-field">{% trans "رقم الهاتف" %}</label>
                            {{ form.phone }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.phone.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.email.id_for_label }}" class="form-label">{% trans "البريد الإلكتروني" %}</label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.category.id_for_label }}" class="form-label">{% trans "الفئة" %}</label>
                            {{ form.category }}
                            {% if form.category.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.category.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.city.id_for_label }}" class="form-label">{% trans "المدينة" %}</label>
                            {{ form.city }}
                            {% if form.city.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.city.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.credit_limit.id_for_label }}" class="form-label">{% trans "حد الائتمان" %}</label>
                            {{ form.credit_limit }}
                            {% if form.credit_limit.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.credit_limit.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="help-text">{% trans "الرصيد الحالي" %}: {{ customer.balance|floatformat:2 }} {% trans "د.م" %}</div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.address.id_for_label }}" class="form-label">{% trans "العنوان" %}</label>
                        {{ form.address }}
                        {% if form.address.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.address.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">{% trans "ملاحظات" %}</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3 form-check">
                        {{ form.is_active }}
                        <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                            {% trans "نشط" %}
                        </label>
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <button type="submit" class="btn btn-primary btn-submit">
                            <i class="fas fa-save me-1"></i> {% trans "حفظ التغييرات" %}
                        </button>
                        <button type="submit" name="save_and_view" class="btn btn-outline-success btn-submit">
                            <i class="fas fa-eye me-1"></i> {% trans "حفظ وعرض" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- المبيعات الأخيرة -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "المبيعات الأخيرة" %}</h6>
            </div>
            <div class="card-body">
                {% if sales %}
                    <div class="list-group">
                        {% for sale in sales %}
                            <a href="#" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">#{{ sale.id }}</h6>
                                    <small>{{ sale.date|date:"Y-m-d" }}</small>
                                </div>
                                <p class="mb-1">{{ sale.total_amount|floatformat:2 }} {% trans "د.م" %}</p>
                                <small class="text-muted">
                                    {% if sale.status == 'completed' %}
                                        <span class="badge bg-success">{% trans "مكتمل" %}</span>
                                    {% elif sale.status == 'pending' %}
                                        <span class="badge bg-warning">{% trans "معلق" %}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ sale.get_status_display }}</span>
                                    {% endif %}
                                </small>
                            </a>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                        <p>{% trans "لا توجد مبيعات بعد" %}</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- المركبات -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "مركبات العميل" %}</h6>
            </div>
            <div class="card-body">
                {% if vehicles %}
                    <div class="list-group">
                        {% for vehicle in vehicles %}
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ vehicle.make }} {{ vehicle.model }}</h6>
                                    <small>{{ vehicle.year }}</small>
                                </div>
                                {% if vehicle.license_plate %}
                                    <p class="mb-1">{% trans "رقم اللوحة" %}: {{ vehicle.license_plate }}</p>
                                {% endif %}
                                {% if vehicle.vin %}
                                    <small class="text-muted">VIN: {{ vehicle.vin }}</small>
                                {% endif %}
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-car fa-3x mb-3"></i>
                        <p>{% trans "لا توجد مركبات مسجلة" %}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // تنسيق حقل رقم الهاتف
        $('#{{ form.phone.id_for_label }}').on('input', function() {
            var value = $(this).val();
            // إزالة الأحرف غير الرقمية
            value = value.replace(/[^0-9]/g, '');
            $(this).val(value);
        });

        // التحقق من صحة النموذج قبل الإرسال
        $('#customerForm').on('submit', function(e) {
            var name = $('#{{ form.name.id_for_label }}').val();
            var phone = $('#{{ form.phone.id_for_label }}').val();

            if (!name || !phone) {
                e.preventDefault();
                alert("{% trans 'يرجى ملء جميع الحقول المطلوبة' %}");
                return false;
            }

            if (phone.length < 10) {
                e.preventDefault();
                alert("{% trans 'يجب أن يكون رقم الهاتف 10 أرقام على الأقل' %}");
                return false;
            }

            return true;
        });
    });
</script>
{% endblock %}
