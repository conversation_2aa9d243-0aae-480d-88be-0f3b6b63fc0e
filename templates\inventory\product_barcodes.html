{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">{{ title }}</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "Barcodes for Product:" %} {{ product.name }}</h6>
        </div>
        <div class="card-body">
            <a href="{% url 'inventory:add_product_barcode' product.id %}" class="btn btn-primary mb-3">{% trans "Add New Barcode" %}</a>
            
            {% if barcodes %}
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>{% trans "Barcode Number" %}</th>
                            <th>{% trans "Barcode Type" %}</th>
                            <th>{% trans "Primary" %}</th>
                            <th>{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for barcode in barcodes %}
                            <tr>
                                <td>{{ barcode.barcode_number }}</td>
                                <td>{{ barcode.barcode_type.name }}</td>
                                <td>
                                    {% if barcode.is_primary %}
                                        <i class="fas fa-check-circle text-success"></i>
                                    {% else %}
                                        <i class="fas fa-times-circle text-danger"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'inventory:edit_product_barcode' barcode.id %}" class="btn btn-sm btn-warning">{% trans "Edit" %}</a>
                                    <a href="{% url 'inventory:delete_product_barcode' barcode.id %}" class="btn btn-sm btn-danger">{% trans "Delete" %}</a>
                                    <a href="{% url 'inventory:print_product_barcode' barcode.id %}" class="btn btn-sm btn-info">{% trans "Print" %}</a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <p>{% trans "No barcodes found for this product." %}</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}