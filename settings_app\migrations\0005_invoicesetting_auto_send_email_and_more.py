# Generated by Django 5.2 on 2025-04-23 10:51

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('settings_app', '0004_operationlog'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='invoicesetting',
            name='auto_send_email',
            field=models.BooleanField(default=False, verbose_name='إرسال تلقائي للبريد'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='default_notes',
            field=models.TextField(blank=True, null=True, verbose_name='ملاحظات افتراضية'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='email_body_template',
            field=models.TextField(default='مرحباً {customer_name},\n\nمرفق فاتورة المبيعات الخاصة بكم رقم {invoice_number}.\n\nشكراً لتعاملكم معنا.\n\n{company_name}', verbose_name='قالب نص البريد'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='email_subject_template',
            field=models.CharField(default='فاتورة رقم {invoice_number}', max_length=255, verbose_name='قالب عنوان البريد'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='font_family',
            field=models.CharField(choices=[('tajawal', 'Tajawal'), ('cairo', 'Cairo'), ('noto', 'Noto Sans Arabic'), ('droid', 'Droid Arabic Naskh')], default='tajawal', max_length=10, verbose_name='نوع الخط'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='invoices_count',
            field=models.PositiveIntegerField(default=0, verbose_name='عدد الفواتير المصدرة'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='last_reset_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ آخر إعادة تعيين'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='logo',
            field=models.ImageField(blank=True, null=True, upload_to='company_logos/', verbose_name='شعار الشركة'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='number_format',
            field=models.CharField(choices=[('simple', 'بسيط (مثال: INV-1001)'), ('date', 'مع التاريخ (مثال: INV-YYYY-MM-0001)'), ('year', 'مع السنة (مثال: INV-YYYY-0001)'), ('month', 'مع الشهر (مثال: INV-MM-0001)')], default='simple', max_length=10, verbose_name='تنسيق الرقم'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='paper_size',
            field=models.CharField(choices=[('a4', 'A4'), ('a5', 'A5'), ('letter', 'Letter'), ('thermal', 'إيصال حراري (80مم)')], default='a4', max_length=10, verbose_name='حجم الورق'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='primary_color',
            field=models.CharField(default='#4e73df', max_length=7, verbose_name='اللون الرئيسي'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='reset_sequence',
            field=models.CharField(choices=[('never', 'أبداً'), ('yearly', 'سنوياً'), ('monthly', 'شهرياً')], default='never', max_length=10, verbose_name='إعادة تعيين التسلسل'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='secondary_color',
            field=models.CharField(default='#f8f9fa', max_length=7, verbose_name='اللون الثانوي'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='show_qr_code',
            field=models.BooleanField(default=True, verbose_name='إظهار رمز QR'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='show_signature',
            field=models.BooleanField(default=True, verbose_name='إظهار التوقيع'),
        ),
        migrations.AddField(
            model_name='invoicesetting',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='invoice_settings_updates', to=settings.AUTH_USER_MODEL, verbose_name='تم التحديث بواسطة'),
        ),
        migrations.CreateModel(
            name='InvoiceSettingChangeLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('changes', models.TextField(verbose_name='التغييرات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التغيير')),
                ('invoice_setting', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='change_logs', to='settings_app.invoicesetting', verbose_name='إعداد الفاتورة')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'سجل تغييرات إعدادات الفاتورة',
                'verbose_name_plural': 'سجلات تغييرات إعدادات الفاتورة',
                'ordering': ['-created_at'],
            },
        ),
    ]
