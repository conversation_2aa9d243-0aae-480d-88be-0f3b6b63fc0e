from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth.models import User, Group
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_POST
from django.contrib.auth import update_session_auth_hash
import json

from .models import (
    SystemSetting, CompanyInfo, BackupLog, NotificationSetting,
    CurrencySetting, TaxSetting, InvoiceSetting, EmailSetting,
    BackupSetting, LanguageSetting, OperationLog, RestoreLog,
    InvoiceSettingChangeLog
)
import csv
from datetime import datetime
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger

def is_admin(user):
    return user.is_superuser or user.groups.filter(name='Administrators').exists()

@login_required
def index(request):
    return render(request, 'settings_app/index.html')

@login_required
def profile(request):
    user = request.user

    if request.method == 'POST':
        # تحديث معلومات المستخدم
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')
        email = request.POST.get('email')

        # تحديث كلمة المرور إذا تم توفيرها
        current_password = request.POST.get('current_password')
        new_password = request.POST.get('new_password')
        confirm_password = request.POST.get('confirm_password')

        # تحديث المعلومات الأساسية
        user.first_name = first_name
        user.last_name = last_name
        user.email = email

        # تحديث كلمة المرور إذا تم توفيرها
        if new_password:
            if not user.check_password(current_password):
                messages.error(request, _('كلمة المرور الحالية غير صحيحة'))
                return redirect('settings_app:profile')

            if new_password != confirm_password:
                messages.error(request, _('كلمات المرور الجديدة غير متطابقة'))
                return redirect('settings_app:profile')

            user.set_password(new_password)
            update_session_auth_hash(request, user)  # للحفاظ على جلسة المستخدم بعد تغيير كلمة المرور

        user.save()
        messages.success(request, _('تم تحديث الملف الشخصي بنجاح'))
        return redirect('settings_app:profile')

    context = {
        'user_obj': user,
    }
    return render(request, 'settings_app/profile.html', context)

@login_required
@user_passes_test(is_admin)
def general_settings(request):
    # Get or create company info
    company_info, created = CompanyInfo.objects.get_or_create(pk=1)

    if request.method == 'POST':
        # Update company info
        company_info.name = request.POST.get('company_name')
        company_info.address = request.POST.get('address')
        company_info.phone = request.POST.get('phone')
        company_info.email = request.POST.get('email')

        # Handle website field (optional)
        website = request.POST.get('website')
        if website and not (website.startswith('http://') or website.startswith('https://')):
            # Add http:// prefix if not present
            if website.strip():
                website = 'http://' + website
        company_info.website = website if website.strip() else None

        company_info.tax_number = request.POST.get('tax_number')
        company_info.commercial_register = request.POST.get('commercial_register')
        company_info.footer_text = request.POST.get('footer_text')

        # Handle logo upload
        if 'logo' in request.FILES:
            company_info.logo = request.FILES['logo']

        company_info.save()
        messages.success(request, _('تم تحديث معلومات الشركة بنجاح'))
        return redirect('settings_app:general_settings')

    context = {
        'company_info': company_info,
    }
    return render(request, 'settings_app/general_settings.html', context)

@login_required
@user_passes_test(is_admin)
def users(request):
    users_list = User.objects.all().order_by('username')
    context = {
        'users': users_list,
    }
    return render(request, 'settings_app/users.html', context)

@login_required
@user_passes_test(is_admin)
def add_user(request):
    groups = Group.objects.all()

    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        confirm_password = request.POST.get('confirm_password')
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')
        email = request.POST.get('email')
        is_active = request.POST.get('is_active') == 'on'
        is_superuser = request.POST.get('is_superuser') == 'on'
        selected_groups = request.POST.getlist('groups')

        # Validate form
        if User.objects.filter(username=username).exists():
            messages.error(request, _('اسم المستخدم موجود بالفعل'))
        elif password != confirm_password:
            messages.error(request, _('كلمات المرور غير متطابقة'))
        else:
            # Create user
            user = User.objects.create_user(
                username=username,
                password=password,
                first_name=first_name,
                last_name=last_name,
                email=email,
                is_active=is_active,
                is_superuser=is_superuser,
                is_staff=is_superuser,
            )

            # Add to groups
            for group_id in selected_groups:
                group = Group.objects.get(id=group_id)
                user.groups.add(group)

            messages.success(request, _('تم إنشاء المستخدم بنجاح'))
            return redirect('settings_app:users')

    context = {
        'groups': groups,
    }
    return render(request, 'settings_app/add_user.html', context)

@login_required
@user_passes_test(is_admin)
def edit_user(request, user_id):
    user = get_object_or_404(User, id=user_id)
    groups = Group.objects.all()

    if request.method == 'POST':
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')
        email = request.POST.get('email')
        is_active = request.POST.get('is_active') == 'on'
        is_superuser = request.POST.get('is_superuser') == 'on'
        selected_groups = request.POST.getlist('groups')

        # Update user
        user.first_name = first_name
        user.last_name = last_name
        user.email = email
        user.is_active = is_active
        user.is_superuser = is_superuser
        user.is_staff = is_superuser

        # Update password if provided
        new_password = request.POST.get('new_password')
        confirm_password = request.POST.get('confirm_password')

        if new_password:
            if new_password != confirm_password:
                messages.error(request, _('كلمات المرور غير متطابقة'))
                context = {
                    'user_obj': user,
                    'groups': groups,
                }
                return render(request, 'settings_app/edit_user.html', context)
            else:
                user.set_password(new_password)

        user.save()

        # Update groups
        user.groups.clear()
        for group_id in selected_groups:
            group = Group.objects.get(id=group_id)
            user.groups.add(group)

        messages.success(request, _('تم تحديث المستخدم بنجاح'))
        return redirect('settings_app:users')

    context = {
        'user_obj': user,
        'groups': groups,
    }
    return render(request, 'settings_app/edit_user.html', context)

@login_required
@user_passes_test(is_admin)
def backup(request):
    from .backup_utils import create_backup, delete_backup

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'create':
            # إنشاء نسخة احتياطية جديدة
            include_media = request.POST.get('include_media') == 'on'
            encrypt = request.POST.get('encrypt') == 'on'
            storage_type = request.POST.get('storage_type', 'local')
            notes = request.POST.get('notes')
            backup_log = create_backup(
                backup_type='manual',
                include_media=include_media,
                notes=notes,
                encrypt=encrypt,
                storage_type=storage_type,
                user=request.user
            )

            if backup_log.status == 'success':
                # Log the operation
                OperationLog.log_operation(
                    operation_type='backup',
                    status='success',
                    user=request.user,
                    file_name=backup_log.file_name,
                    details=f"تم إنشاء نسخة احتياطية بنجاح: {backup_log.file_name}",
                    ip_address=request.META.get('REMOTE_ADDR'),
                    backup_log=backup_log
                )
                messages.success(request, _('تم إنشاء النسخة الاحتياطية بنجاح'))
            else:
                # Log the failed operation
                OperationLog.log_operation(
                    operation_type='backup',
                    status='failed',
                    user=request.user,
                    details=f"فشل إنشاء النسخة الاحتياطية: {backup_log.notes}",
                    ip_address=request.META.get('REMOTE_ADDR')
                )
                messages.error(request, _('فشل إنشاء النسخة الاحتياطية: {}').format(backup_log.notes))

        elif action == 'delete':
            # حذف نسخة احتياطية
            backup_id = request.POST.get('backup_id')
            backup_log = BackupLog.objects.get(id=backup_id)
            file_name = backup_log.file_name

            if delete_backup(backup_id):
                # Log the operation
                OperationLog.log_operation(
                    operation_type='delete',
                    status='success',
                    user=request.user,
                    file_name=file_name,
                    details=f"تم حذف النسخة الاحتياطية: {file_name}",
                    ip_address=request.META.get('REMOTE_ADDR')
                )
                messages.success(request, _('تم حذف النسخة الاحتياطية بنجاح'))
            else:
                # Log the failed operation
                OperationLog.log_operation(
                    operation_type='delete',
                    status='failed',
                    user=request.user,
                    file_name=file_name,
                    details=f"فشل حذف النسخة الاحتياطية: {file_name}",
                    ip_address=request.META.get('REMOTE_ADDR')
                )
                messages.error(request, _('فشل حذف النسخة الاحتياطية'))

        elif action == 'delete_all':
            # حذف جميع النسخ الاحتياطية
            success_count = 0
            failed_count = 0
            for backup_log in BackupLog.objects.all():
                if delete_backup(backup_log.id):
                    success_count += 1
                else:
                    failed_count += 1

            # Log the operation
            OperationLog.log_operation(
                operation_type='delete',
                status='success' if failed_count == 0 else 'failed',
                user=request.user,
                details=f"تم حذف {success_count} نسخة احتياطية بنجاح، فشل حذف {failed_count} نسخة",
                ip_address=request.META.get('REMOTE_ADDR')
            )

            if failed_count == 0:
                messages.success(request, _('تم حذف جميع النسخ الاحتياطية بنجاح'))
            else:
                messages.warning(request, _('تم حذف {} نسخة احتياطية بنجاح، فشل حذف {} نسخة').format(success_count, failed_count))

        return redirect('settings_app:backup')

    # Get backup settings
    backup_setting = BackupSetting.objects.get_or_create(pk=1)[0]
    backup_logs = BackupLog.objects.all().order_by('-created_at')

    context = {
        'backup_logs': backup_logs,
        'backup_setting': backup_setting,
    }
    return render(request, 'settings_app/backup.html', context)

@login_required
@user_passes_test(is_admin)
def restore(request):
    from .backup_utils import restore_backup

    if request.method == 'POST':
        backup_id = request.POST.get('backup_id')
        partial_restore = request.POST.get('partial_restore') == 'on'
        restore_options = request.POST.getlist('restore_options')

        if backup_id:
            # استعادة النسخة الاحتياطية
            backup_log = BackupLog.objects.get(id=backup_id)
            restore_log = restore_backup(
                backup_id=backup_id,
                partial=partial_restore,
                options=restore_options,
                user=request.user
            )

            if restore_log and restore_log.status == 'success':
                # Log the operation
                OperationLog.log_operation(
                    operation_type='restore',
                    status='success',
                    user=request.user,
                    file_name=backup_log.file_name,
                    details=f"تم استعادة النسخة الاحتياطية بنجاح: {backup_log.file_name}",
                    ip_address=request.META.get('REMOTE_ADDR'),
                    backup_log=backup_log,
                    restore_log=restore_log
                )
                messages.success(request, _('تم استعادة النسخة الاحتياطية بنجاح'))
            else:
                # Log the failed operation
                OperationLog.log_operation(
                    operation_type='restore',
                    status='failed',
                    user=request.user,
                    file_name=backup_log.file_name,
                    details=f"فشل استعادة النسخة الاحتياطية: {backup_log.file_name}",
                    ip_address=request.META.get('REMOTE_ADDR'),
                    backup_log=backup_log
                )
                messages.error(request, _('فشل استعادة النسخة الاحتياطية'))

            return redirect('settings_app:backup')

    # Get selected backup if provided in query params
    backup_id = request.GET.get('backup_id')
    selected_backup = None
    if backup_id:
        selected_backup = get_object_or_404(BackupLog, id=backup_id, status='success')

    backup_logs = BackupLog.objects.filter(status='success').order_by('-created_at')
    context = {
        'backup_logs': backup_logs,
        'selected_backup': selected_backup,
    }
    return render(request, 'settings_app/restore.html', context)

@login_required
@user_passes_test(is_admin)
def delete_user(request, user_id):
    user = get_object_or_404(User, id=user_id)

    # Prevent deleting yourself
    if user == request.user:
        messages.error(request, _('لا يمكنك حذف حسابك الخاص'))
        return redirect('settings_app:users')

    # Delete user
    username = user.username
    user.delete()

    messages.success(request, _('تم حذف المستخدم {} بنجاح').format(username))
    return redirect('settings_app:users')

@login_required
@user_passes_test(is_admin)
def currency_settings(request):
    # Get all currencies or create default one
    currencies = CurrencySetting.objects.all()
    if not currencies.exists():
        CurrencySetting.objects.create(
            currency='MAD',
            symbol='د.م.',
            position='after',
            is_default=True
        )
        currencies = CurrencySetting.objects.all()

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'add':
            currency = request.POST.get('currency')
            symbol = request.POST.get('symbol')
            position = request.POST.get('position')
            decimal_places = request.POST.get('decimal_places', 2)
            thousands_separator = request.POST.get('thousands_separator', ',')
            decimal_separator = request.POST.get('decimal_separator', '.')
            is_default = request.POST.get('is_default') == 'on'

            CurrencySetting.objects.create(
                currency=currency,
                symbol=symbol,
                position=position,
                decimal_places=decimal_places,
                thousands_separator=thousands_separator,
                decimal_separator=decimal_separator,
                is_default=is_default
            )
            messages.success(request, _('تم إضافة العملة بنجاح'))

        elif action == 'edit':
            currency_id = request.POST.get('currency_id')
            currency_obj = get_object_or_404(CurrencySetting, id=currency_id)

            currency_obj.currency = request.POST.get('currency')
            currency_obj.symbol = request.POST.get('symbol')
            currency_obj.position = request.POST.get('position')
            currency_obj.decimal_places = request.POST.get('decimal_places', 2)
            currency_obj.thousands_separator = request.POST.get('thousands_separator', ',')
            currency_obj.decimal_separator = request.POST.get('decimal_separator', '.')
            currency_obj.is_default = request.POST.get('is_default') == 'on'

            currency_obj.save()
            messages.success(request, _('تم تحديث العملة بنجاح'))

        return redirect('settings_app:currency_settings')

    context = {
        'currencies': currencies,
    }
    return render(request, 'settings_app/currency_settings.html', context)

@login_required
@user_passes_test(is_admin)
@require_POST
def delete_currency(request, currency_id):
    currency = get_object_or_404(CurrencySetting, id=currency_id)

    # Don't delete if it's the only currency
    if CurrencySetting.objects.count() <= 1:
        messages.error(request, _('لا يمكن حذف العملة الوحيدة في النظام'))
        return redirect('settings_app:currency_settings')

    # Don't delete default currency
    if currency.is_default:
        messages.error(request, _('لا يمكن حذف العملة الافتراضية، قم بتعيين عملة أخرى كافتراضية أولاً'))
        return redirect('settings_app:currency_settings')

    currency.delete()
    messages.success(request, _('تم حذف العملة بنجاح'))
    return redirect('settings_app:currency_settings')

@login_required
@user_passes_test(is_admin)
def tax_settings(request):
    # Get tax settings or create default
    tax_settings = TaxSetting.objects.all()
    if not tax_settings.exists():
        TaxSetting.objects.create(
            name='ضريبة القيمة المضافة',
            rate=15.00,
            tax_type='exclusive',
            is_enabled=True
        )
        tax_settings = TaxSetting.objects.all()

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'add':
            name = request.POST.get('name')
            rate = request.POST.get('rate')
            tax_type = request.POST.get('tax_type')
            is_enabled = request.POST.get('is_enabled') == 'on'
            tax_number = request.POST.get('tax_number')

            TaxSetting.objects.create(
                name=name,
                rate=rate,
                tax_type=tax_type,
                is_enabled=is_enabled,
                tax_number=tax_number
            )
            messages.success(request, _('تم إضافة الضريبة بنجاح'))

        elif action == 'edit':
            tax_id = request.POST.get('tax_id')
            tax_obj = get_object_or_404(TaxSetting, id=tax_id)

            tax_obj.name = request.POST.get('name')
            tax_obj.rate = request.POST.get('rate')
            tax_obj.tax_type = request.POST.get('tax_type')
            tax_obj.is_enabled = request.POST.get('is_enabled') == 'on'
            tax_obj.tax_number = request.POST.get('tax_number')

            tax_obj.save()
            messages.success(request, _('تم تحديث الضريبة بنجاح'))

        return redirect('settings_app:tax_settings')

    context = {
        'tax_settings': tax_settings,
    }
    return render(request, 'settings_app/tax_settings.html', context)

@login_required
@user_passes_test(is_admin)
@require_POST
def delete_tax(request, tax_id):
    tax = get_object_or_404(TaxSetting, id=tax_id)
    tax.delete()
    messages.success(request, _('تم حذف الضريبة بنجاح'))
    return redirect('settings_app:tax_settings')

@login_required
@user_passes_test(is_admin)
def invoice_settings(request):
    # Get or create invoice settings
    invoice_setting = InvoiceSetting.objects.get_or_create(pk=1)[0]

    # Get tax settings for the form
    tax_settings = TaxSetting.objects.filter(is_enabled=True).first()

    # Get change logs
    change_logs = InvoiceSettingChangeLog.objects.all()[:10]

    # Get invoice count
    from sales.models import Sale
    invoice_count = Sale.objects.count()

    # Get company info
    company_info = CompanyInfo.objects.first()

    if request.method == 'POST':
        action = request.POST.get('action', 'update_settings')

        if action == 'update_settings':
            # Basic settings
            invoice_setting.prefix = request.POST.get('prefix')
            invoice_setting.next_number = request.POST.get('next_number')
            invoice_setting.number_format = request.POST.get('number_format')
            invoice_setting.reset_sequence = request.POST.get('reset_sequence')

            # Content settings
            invoice_setting.footer_text = request.POST.get('footer_text')
            invoice_setting.terms_and_conditions = request.POST.get('terms_and_conditions')
            invoice_setting.default_notes = request.POST.get('default_notes')
            invoice_setting.show_logo = request.POST.get('show_logo') == 'on'
            invoice_setting.show_tax = request.POST.get('show_tax') == 'on'
            invoice_setting.show_customer_info = request.POST.get('show_customer_info') == 'on'
            invoice_setting.show_signature = request.POST.get('show_signature') == 'on'
            invoice_setting.show_qr_code = request.POST.get('show_qr_code') == 'on'

            # Design settings
            if 'logo' in request.FILES:
                invoice_setting.logo = request.FILES['logo']
            invoice_setting.primary_color = request.POST.get('primary_color')
            invoice_setting.secondary_color = request.POST.get('secondary_color')
            invoice_setting.font_family = request.POST.get('font_family')

            # Print settings
            invoice_setting.paper_size = request.POST.get('paper_size')

            # Email settings
            invoice_setting.email_subject_template = request.POST.get('email_subject_template')
            invoice_setting.email_body_template = request.POST.get('email_body_template')
            invoice_setting.auto_send_email = request.POST.get('auto_send_email') == 'on'

            # Update statistics
            invoice_setting.invoices_count = invoice_count
            invoice_setting.updated_by = request.user

            invoice_setting.save()
            messages.success(request, _('تم تحديث إعدادات الفاتورة بنجاح'))

        elif action == 'update_content_settings':
            # حفظ إعدادات المحتوى
            # معلومات الفاتورة
            invoice_setting.show_invoice_number = request.POST.get('show_invoice_number') == 'on'
            invoice_setting.show_customer = request.POST.get('show_customer') == 'on'
            invoice_setting.show_sale_date = request.POST.get('show_sale_date') == 'on'
            invoice_setting.show_employee = request.POST.get('show_employee') == 'on'
            invoice_setting.show_status = request.POST.get('show_status') == 'on'
            invoice_setting.show_payment_method = request.POST.get('show_payment_method') == 'on'
            invoice_setting.show_created_at = request.POST.get('show_created_at') == 'on'
            invoice_setting.show_updated_at = request.POST.get('show_updated_at') == 'on'

            # معلومات المنتجات
            invoice_setting.show_product_code = request.POST.get('show_product_code') == 'on'
            invoice_setting.show_product_description = request.POST.get('show_product_description') == 'on'
            invoice_setting.show_unit_price = request.POST.get('show_unit_price') == 'on'
            invoice_setting.show_quantity = request.POST.get('show_quantity') == 'on'

            # معلومات إضافية
            invoice_setting.invoice_title = request.POST.get('invoice_title')
            invoice_setting.invoice_subtitle = request.POST.get('invoice_subtitle')

            invoice_setting.updated_by = request.user
            invoice_setting.save()
            messages.success(request, _('تم تحديث محتوى الفاتورة بنجاح'))

        elif action == 'update_print_settings':
            # حفظ إعدادات الطباعة
            invoice_setting.paper_size = request.POST.get('paper_size')
            invoice_setting.print_copies = request.POST.get('print_copies', 1)

            # هوامش الصفحة
            invoice_setting.margin_top = request.POST.get('margin_top', 10)
            invoice_setting.margin_right = request.POST.get('margin_right', 10)
            invoice_setting.margin_bottom = request.POST.get('margin_bottom', 10)
            invoice_setting.margin_left = request.POST.get('margin_left', 10)

            # خيارات متقدمة
            invoice_setting.auto_print = request.POST.get('auto_print') == 'on'
            invoice_setting.print_background = request.POST.get('print_background') == 'on'
            invoice_setting.print_header_footer = request.POST.get('print_header_footer') == 'on'

            invoice_setting.updated_by = request.user
            invoice_setting.save()
            messages.success(request, _('تم تحديث إعدادات الطباعة بنجاح'))

        elif action == 'update_tax_settings':
            # حفظ إعدادات الضرائب
            tax_settings = TaxSetting.objects.filter(is_enabled=True).first()
            if not tax_settings:
                tax_settings = TaxSetting()

            tax_settings.name = request.POST.get('tax_name')
            tax_settings.rate = request.POST.get('tax_rate')
            tax_settings.tax_type = request.POST.get('tax_type')
            tax_settings.tax_number = request.POST.get('tax_number')
            tax_settings.is_enabled = request.POST.get('is_enabled') == 'on'

            tax_settings.save()

            # تحديث إعدادات عرض الضريبة في الفاتورة
            invoice_setting.show_tax = request.POST.get('show_tax_column') == 'on'
            invoice_setting.show_tax_summary = request.POST.get('show_tax_summary') == 'on'

            invoice_setting.updated_by = request.user
            invoice_setting.save()
            messages.success(request, _('تم تحديث إعدادات الضريبة بنجاح'))

        elif action == 'update_email_settings':
            # حفظ إعدادات البريد الإلكتروني
            invoice_setting.email_subject_template = request.POST.get('email_subject_template')
            invoice_setting.email_body_template = request.POST.get('email_body_template')
            invoice_setting.auto_send_email = request.POST.get('auto_send_email') == 'on'

            # حفظ الخيارات الإضافية
            # يمكن إضافة حقول جديدة في المستقبل لدعم هذه الخيارات
            # مثل attach_pdf و bcc_admin

            invoice_setting.updated_by = request.user
            invoice_setting.save()
            messages.success(request, _('تم تحديث إعدادات البريد الإلكتروني بنجاح'))

        elif action == 'update_company_info':
            # حفظ معلومات المتجر
            company_info = CompanyInfo.objects.first()
            if not company_info:
                company_info = CompanyInfo()

            company_info.name = request.POST.get('company_name')
            company_info.address = request.POST.get('company_address')
            company_info.phone = request.POST.get('company_phone')
            company_info.email = request.POST.get('company_email')
            company_info.website = request.POST.get('company_website')
            company_info.tax_number = request.POST.get('tax_number')
            company_info.commercial_register = request.POST.get('commercial_register')
            company_info.footer_text = request.POST.get('company_footer_text')

            if 'company_logo' in request.FILES:
                company_info.logo = request.FILES['company_logo']

            company_info.save()
            messages.success(request, _('تم تحديث معلومات المتجر بنجاح'))

        elif action == 'reset_sequence':
            # Reset invoice number sequence
            invoice_setting.next_number = int(request.POST.get('reset_number', 1001))
            invoice_setting.last_reset_date = timezone.now().date()
            invoice_setting.updated_by = request.user
            invoice_setting.save()
            messages.success(request, _('تم إعادة تعيين تسلسل أرقام الفواتير بنجاح'))

        elif action == 'test_email':
            # Send test email with invoice template
            try:
                from django.core.mail import EmailMessage
                from django.template import Template, Context

                # Get email settings
                email_setting = EmailSetting.objects.first()
                if not email_setting or not email_setting.is_enabled:
                    messages.error(request, _('إعدادات البريد الإلكتروني غير مكتملة أو معطلة'))
                    return redirect('settings_app:invoice_settings')

                # Get test email address
                test_email = request.POST.get('test_email')
                if not test_email:
                    messages.error(request, _('يرجى إدخال عنوان بريد إلكتروني للاختبار'))
                    return redirect('settings_app:invoice_settings')

                # Create test context
                context = {
                    'invoice_number': f"{invoice_setting.prefix}TEST",
                    'customer_name': 'عميل تجريبي',
                    'company_name': 'متجر قطع غيار السيارات',
                }

                # Render email templates
                subject_template = Template(invoice_setting.email_subject_template)
                body_template = Template(invoice_setting.email_body_template)
                subject = subject_template.render(Context(context))
                body = body_template.render(Context(context))

                # Send email
                email = EmailMessage(
                    subject=subject,
                    body=body,
                    from_email=f"{email_setting.from_name} <{email_setting.from_email}>",
                    to=[test_email],
                )
                email.send(fail_silently=False)

                messages.success(request, _('تم إرسال بريد إلكتروني تجريبي بنجاح'))
            except Exception as e:
                messages.error(request, _('حدث خطأ أثناء إرسال البريد الإلكتروني: {}').format(str(e)))

        return redirect('settings_app:invoice_settings')

    context = {
        'invoice_setting': invoice_setting,
        'tax_settings': tax_settings,
        'change_logs': change_logs,
        'invoice_count': invoice_count,
        'company_info': company_info,
    }
    return render(request, 'settings_app/invoice_settings.html', context)

@login_required
@user_passes_test(is_admin)
def email_settings(request):
    # Get or create email settings
    email_setting = EmailSetting.objects.get_or_create(
        defaults={
            'smtp_host': 'smtp.gmail.com',
            'smtp_port': 587,
            'smtp_username': '',
            'smtp_password': '',
            'from_email': '',
            'from_name': 'نظام إدارة متجر قطع غيار السيارات',
        }
    )[0]

    if request.method == 'POST':
        email_setting.smtp_host = request.POST.get('smtp_host')
        email_setting.smtp_port = request.POST.get('smtp_port')
        email_setting.smtp_username = request.POST.get('smtp_username')

        # Only update password if provided
        new_password = request.POST.get('smtp_password')
        if new_password:
            email_setting.smtp_password = new_password

        email_setting.smtp_use_tls = request.POST.get('smtp_use_tls') == 'on'
        email_setting.from_email = request.POST.get('from_email')
        email_setting.from_name = request.POST.get('from_name')
        email_setting.is_enabled = request.POST.get('is_enabled') == 'on'

        email_setting.save()
        messages.success(request, _('تم تحديث إعدادات البريد الإلكتروني بنجاح'))
        return redirect('settings_app:email_settings')

    context = {
        'email_setting': email_setting,
    }
    return render(request, 'settings_app/email_settings.html', context)

@login_required
@user_passes_test(is_admin)
def backup_settings(request):
    from .backup_utils import create_backup, cleanup_old_backups

    # Get or create backup settings
    backup_setting = BackupSetting.objects.get_or_create(pk=1)[0]
    backup_logs = BackupLog.objects.all().order_by('-created_at')[:10]

    if request.method == 'POST':
        action = request.POST.get('action', 'update')

        if action == 'update':
            # تحديث إعدادات النسخ الاحتياطي
            backup_setting.is_auto_backup = request.POST.get('is_auto_backup') == 'on'
            backup_setting.frequency = request.POST.get('frequency')
            backup_setting.backup_time = request.POST.get('backup_time')
            backup_setting.storage_type = request.POST.get('storage_type')
            backup_setting.storage_path = request.POST.get('storage_path')
            backup_setting.retention_days = request.POST.get('retention_days')
            backup_setting.include_media = request.POST.get('include_media') == 'on'

            backup_setting.save()

            # Log the operation
            OperationLog.log_operation(
                operation_type='backup',
                status='success',
                user=request.user,
                details=f"تم تحديث إعدادات النسخ الاحتياطي",
                ip_address=request.META.get('REMOTE_ADDR')
            )
            messages.success(request, _('تم تحديث إعدادات النسخ الاحتياطي بنجاح'))

        elif action == 'create':
            # إنشاء نسخة احتياطية جديدة
            include_media = request.POST.get('include_media_manual') == 'on'
            backup_log = create_backup(backup_type='manual', include_media=include_media, user=request.user)

            if backup_log.status == 'success':
                # Log the operation
                OperationLog.log_operation(
                    operation_type='backup',
                    status='success',
                    user=request.user,
                    file_name=backup_log.file_name,
                    details=f"تم إنشاء نسخة احتياطية بنجاح: {backup_log.file_name}",
                    ip_address=request.META.get('REMOTE_ADDR'),
                    backup_log=backup_log
                )
                messages.success(request, _('تم إنشاء النسخة الاحتياطية بنجاح'))
            else:
                # Log the failed operation
                OperationLog.log_operation(
                    operation_type='backup',
                    status='failed',
                    user=request.user,
                    details=f"فشل إنشاء النسخة الاحتياطية: {backup_log.notes}",
                    ip_address=request.META.get('REMOTE_ADDR')
                )
                messages.error(request, _('فشل إنشاء النسخة الاحتياطية: {}').format(backup_log.notes))

        elif action == 'cleanup':
            # تنظيف النسخ الاحتياطية القديمة
            days = int(backup_setting.retention_days)
            count = cleanup_old_backups(days)

            # Log the operation
            OperationLog.log_operation(
                operation_type='delete',
                status='success',
                user=request.user,
                details=f"تم حذف {count} من النسخ الاحتياطية القديمة",
                ip_address=request.META.get('REMOTE_ADDR')
            )
            messages.success(request, _('تم حذف {} من النسخ الاحتياطية القديمة').format(count))

        return redirect('settings_app:backup_settings')

    context = {
        'backup_setting': backup_setting,
        'backup_logs': backup_logs,
    }
    return render(request, 'settings_app/backup_settings.html', context)

@login_required
@user_passes_test(is_admin)
def language_settings(request):
    # Get all languages or create default one
    languages = LanguageSetting.objects.all()
    if not languages.exists():
        LanguageSetting.objects.create(
            language='ar',
            direction='rtl',
            is_default=True,
            is_enabled=True
        )
        languages = LanguageSetting.objects.all()

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'add':
            language = request.POST.get('language')
            direction = request.POST.get('direction')
            is_default = request.POST.get('is_default') == 'on'
            is_enabled = request.POST.get('is_enabled') == 'on'

            LanguageSetting.objects.create(
                language=language,
                direction=direction,
                is_default=is_default,
                is_enabled=is_enabled
            )
            messages.success(request, _('تم إضافة اللغة بنجاح'))

        elif action == 'edit':
            language_id = request.POST.get('language_id')
            language_obj = get_object_or_404(LanguageSetting, id=language_id)

            language_obj.language = request.POST.get('language')
            language_obj.direction = request.POST.get('direction')
            language_obj.is_default = request.POST.get('is_default') == 'on'
            language_obj.is_enabled = request.POST.get('is_enabled') == 'on'

            language_obj.save()
            messages.success(request, _('تم تحديث اللغة بنجاح'))

        return redirect('settings_app:language_settings')

    context = {
        'languages': languages,
    }
    return render(request, 'settings_app/language_settings.html', context)

@login_required
@user_passes_test(is_admin)
@require_POST
def delete_language(request, language_id):
    language = get_object_or_404(LanguageSetting, id=language_id)

    # Don't delete if it's the only language
    if LanguageSetting.objects.count() <= 1:
        messages.error(request, _('لا يمكن حذف اللغة الوحيدة في النظام'))
        return redirect('settings_app:language_settings')

    # Don't delete default language
    if language.is_default:
        messages.error(request, _('لا يمكن حذف اللغة الافتراضية، قم بتعيين لغة أخرى كافتراضية أولاً'))
        return redirect('settings_app:language_settings')

    language.delete()
    messages.success(request, _('تم حذف اللغة بنجاح'))
    return redirect('settings_app:language_settings')

@login_required
@user_passes_test(is_admin)
def backup_logs(request):
    # Get operation logs related to backup operations
    operation_type = request.GET.get('operation_type', '')
    status = request.GET.get('status', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')

    # Base queryset
    logs = OperationLog.objects.all()

    # Apply filters
    if operation_type:
        logs = logs.filter(operation_type=operation_type)
    if status:
        logs = logs.filter(status=status)
    if date_from:
        logs = logs.filter(created_at__gte=date_from)
    if date_to:
        logs = logs.filter(created_at__lte=date_to)

    # Order by created_at descending
    logs = logs.order_by('-created_at')

    # Handle POST requests (clear logs)
    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'clear_logs':
            # Clear all logs
            count = logs.count()
            logs.delete()
            messages.success(request, _(f'تم حذف {count} من السجلات بنجاح'))
            return redirect('settings_app:backup_logs')

    # Pagination
    paginator = Paginator(logs, 25)  # Show 25 logs per page
    page = request.GET.get('page')
    try:
        logs = paginator.page(page)
    except PageNotAnInteger:
        logs = paginator.page(1)
    except EmptyPage:
        logs = paginator.page(paginator.num_pages)

    context = {
        'logs': logs,
    }
    return render(request, 'settings_app/backup_logs.html', context)

@login_required
@user_passes_test(is_admin)
def export_logs(request):
    # Get filtered logs based on request parameters
    operation_type = request.GET.get('operation_type', '')
    status = request.GET.get('status', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')

    # Base queryset
    logs = OperationLog.objects.all()

    # Apply filters
    if operation_type:
        logs = logs.filter(operation_type=operation_type)
    if status:
        logs = logs.filter(status=status)
    if date_from:
        logs = logs.filter(created_at__gte=date_from)
    if date_to:
        logs = logs.filter(created_at__lte=date_to)

    # Order by created_at descending
    logs = logs.order_by('-created_at')

    # Create CSV response
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="backup_logs_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv"'

    # Create CSV writer
    writer = csv.writer(response)
    writer.writerow(['ID', 'نوع العملية', 'التاريخ والوقت', 'المستخدم', 'الملف', 'الحالة', 'التفاصيل'])

    # Write data rows
    for log in logs:
        writer.writerow([
            log.id,
            log.get_operation_type_display(),
            log.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            log.user.username if log.user else '-',
            log.file_name or '-',
            log.get_status_display(),
            log.details or '-'
        ])

    return response

@login_required
@user_passes_test(is_admin)
def notification_settings(request):
    # Get all notification settings
    notifications = NotificationSetting.objects.all()

    # Create default notifications if none exist
    if not notifications.exists():
        default_events = [
            ('low_stock', True, True, None),
            ('new_order', True, True, None),
            ('payment_received', True, True, None),
            ('order_status_change', True, False, None),
        ]

        for event, email, system, template in default_events:
            NotificationSetting.objects.create(
                event=event,
                email_notification=email,
                system_notification=system,
                email_template=template
            )

        notifications = NotificationSetting.objects.all()

    if request.method == 'POST':
        for notification in notifications:
            notification.email_notification = request.POST.get(f'email_{notification.id}') == 'on'
            notification.system_notification = request.POST.get(f'system_{notification.id}') == 'on'
            notification.recipients = request.POST.get(f'recipients_{notification.id}')
            notification.email_template = request.POST.get(f'template_{notification.id}')
            notification.save()

        messages.success(request, _('تم تحديث إعدادات الإشعارات بنجاح'))
        return redirect('settings_app:notification_settings')

    context = {
        'notifications': notifications,
    }
    return render(request, 'settings_app/notification_settings.html', context)
