{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "عرض بيانات الموظف" %} - {{ employee.full_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "بيانات الموظف" %}: {{ employee.full_name }}</h1>
        <div>
            <a href="{% url 'employees:edit_employee' employee.id %}" class="btn btn-primary">
                <i class="fas fa-edit"></i> {% trans "تعديل" %}
            </a>
            <a href="{% url 'employees:index' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> {% trans "العودة إلى قائمة الموظفين" %}
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "المعلومات الشخصية" %}</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        {% if employee.image %}
                        <img src="{{ employee.image.url }}" class="img-profile rounded-circle" style="width: 150px; height: 150px; object-fit: cover;">
                        {% else %}
                        <img src="{% static 'img/undraw_profile.svg' %}" class="img-profile rounded-circle" style="width: 150px; height: 150px; object-fit: cover;">
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <h5 class="font-weight-bold">{{ employee.full_name }}</h5>
                        <p class="text-muted">
                            {% if employee.position %}
                            {{ employee.position.name }} - {{ employee.department.name }}
                            {% else %}
                            {% trans "لا يوجد منصب" %}
                            {% endif %}
                        </p>
                    </div>

                    <div class="mb-2">
                        <strong>{% trans "رقم الموظف" %}:</strong> {{ employee.employee_id }}
                    </div>
                    <div class="mb-2">
                        <strong>{% trans "البريد الإلكتروني" %}:</strong> {{ employee.user.email }}
                    </div>
                    <div class="mb-2">
                        <strong>{% trans "رقم الهاتف" %}:</strong> {{ employee.phone }}
                    </div>
                    <div class="mb-2">
                        <strong>{% trans "الجنس" %}:</strong> {{ employee.get_gender_display }}
                    </div>
                    <div class="mb-2">
                        <strong>{% trans "تاريخ الميلاد" %}:</strong> 
                        {% if employee.date_of_birth %}
                        {{ employee.date_of_birth|date:"Y-m-d" }}
                        {% else %}
                        -
                        {% endif %}
                    </div>
                    <div class="mb-2">
                        <strong>{% trans "رقم الهوية" %}:</strong> 
                        {% if employee.national_id %}
                        {{ employee.national_id }}
                        {% else %}
                        -
                        {% endif %}
                    </div>
                    <div class="mb-2">
                        <strong>{% trans "العنوان" %}:</strong> 
                        {% if employee.address %}
                        {{ employee.address }}
                        {% else %}
                        -
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "معلومات العمل" %}</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>{% trans "تاريخ التوظيف" %}:</strong> {{ employee.hire_date|date:"Y-m-d" }}
                            </div>
                            <div class="mb-3">
                                <strong>{% trans "القسم" %}:</strong> 
                                {% if employee.department %}
                                {{ employee.department.name }}
                                {% else %}
                                -
                                {% endif %}
                            </div>
                            <div class="mb-3">
                                <strong>{% trans "المنصب" %}:</strong> 
                                {% if employee.position %}
                                {{ employee.position.name }}
                                {% else %}
                                -
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>{% trans "الراتب" %}:</strong> {{ employee.salary }} {% trans "د.م" %}
                            </div>
                            <div class="mb-3">
                                <strong>{% trans "الحالة" %}:</strong> 
                                {% if employee.is_active %}
                                <span class="badge badge-success">{% trans "نشط" %}</span>
                                {% else %}
                                <span class="badge badge-danger">{% trans "غير نشط" %}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <strong>{% trans "ملاحظات" %}:</strong> 
                        {% if employee.notes %}
                        {{ employee.notes }}
                        {% else %}
                        -
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "سجل الحضور" %}</h6>
                    <a href="#" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> {% trans "إضافة" %}
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>{% trans "التاريخ" %}</th>
                                    <th>{% trans "الحالة" %}</th>
                                    <th>{% trans "وقت الحضور" %}</th>
                                    <th>{% trans "وقت الانصراف" %}</th>
                                    <th>{% trans "ملاحظات" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in attendance %}
                                <tr>
                                    <td>{{ record.date|date:"Y-m-d" }}</td>
                                    <td>{{ record.get_status_display }}</td>
                                    <td>{{ record.check_in|time:"H:i" }}</td>
                                    <td>{{ record.check_out|time:"H:i" }}</td>
                                    <td>{{ record.notes|default:"-" }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center">{% trans "لا يوجد سجلات حضور" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "الإجازات" %}</h6>
                    <a href="#" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> {% trans "إضافة" %}
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>{% trans "النوع" %}</th>
                                    <th>{% trans "من" %}</th>
                                    <th>{% trans "إلى" %}</th>
                                    <th>{% trans "المدة" %}</th>
                                    <th>{% trans "الحالة" %}</th>
                                    <th>{% trans "السبب" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for leave in leaves %}
                                <tr>
                                    <td>{{ leave.get_leave_type_display }}</td>
                                    <td>{{ leave.start_date|date:"Y-m-d" }}</td>
                                    <td>{{ leave.end_date|date:"Y-m-d" }}</td>
                                    <td>{{ leave.days_count }} {% trans "يوم" %}</td>
                                    <td>
                                        {% if leave.status == 'pending' %}
                                        <span class="badge badge-warning">{{ leave.get_status_display }}</span>
                                        {% elif leave.status == 'approved' %}
                                        <span class="badge badge-success">{{ leave.get_status_display }}</span>
                                        {% elif leave.status == 'rejected' %}
                                        <span class="badge badge-danger">{{ leave.get_status_display }}</span>
                                        {% else %}
                                        <span class="badge badge-secondary">{{ leave.get_status_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ leave.reason|truncatechars:30 }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center">{% trans "لا يوجد إجازات" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
