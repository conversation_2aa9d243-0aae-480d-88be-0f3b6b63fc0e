# Generated by Django 5.2 on 2025-04-18 16:07

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='BackupLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_name', models.CharField(max_length=255, verbose_name='اسم الملف')),
                ('file_path', models.CharField(max_length=255, verbose_name='مسار الملف')),
                ('file_size', models.PositiveIntegerField(blank=True, null=True, verbose_name='حجم الملف')),
                ('backup_type', models.CharField(choices=[('auto', 'تلقائي'), ('manual', 'يدوي')], max_length=10, verbose_name='نوع النسخ الاحتياطي')),
                ('status', models.Char<PERSON><PERSON>(choices=[('success', 'ناجح'), ('failed', 'فاشل')], max_length=10, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'سجل النسخ الاحتياطي',
                'verbose_name_plural': 'سجلات النسخ الاحتياطي',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CompanyInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الشركة')),
                ('logo', models.ImageField(blank=True, null=True, upload_to='company/', verbose_name='الشعار')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(max_length=254, verbose_name='البريد الإلكتروني')),
                ('website', models.URLField(blank=True, null=True, verbose_name='الموقع الإلكتروني')),
                ('tax_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='الرقم الضريبي')),
                ('commercial_register', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم السجل التجاري')),
                ('footer_text', models.TextField(blank=True, null=True, verbose_name='نص التذييل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'معلومات الشركة',
                'verbose_name_plural': 'معلومات الشركة',
            },
        ),
        migrations.CreateModel(
            name='NotificationSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event', models.CharField(choices=[('low_stock', 'مخزون منخفض'), ('new_order', 'طلب جديد'), ('payment_received', 'تم استلام الدفع'), ('order_status_change', 'تغيير حالة الطلب')], max_length=50, unique=True, verbose_name='الحدث')),
                ('email_notification', models.BooleanField(default=True, verbose_name='إشعار بريد إلكتروني')),
                ('system_notification', models.BooleanField(default=True, verbose_name='إشعار نظام')),
                ('email_template', models.TextField(blank=True, null=True, verbose_name='قالب البريد الإلكتروني')),
                ('recipients', models.TextField(blank=True, help_text='قائمة بريد إلكتروني مفصولة بفواصل', null=True, verbose_name='المستلمون')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إعداد الإشعارات',
                'verbose_name_plural': 'إعدادات الإشعارات',
                'ordering': ['event'],
            },
        ),
        migrations.CreateModel(
            name='SystemSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=100, unique=True, verbose_name='المفتاح')),
                ('value', models.TextField(verbose_name='القيمة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('is_public', models.BooleanField(default=True, verbose_name='عام')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إعداد النظام',
                'verbose_name_plural': 'إعدادات النظام',
                'ordering': ['key'],
            },
        ),
    ]
