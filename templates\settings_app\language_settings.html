{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/settings.css' %}">
{% endblock %}

{% block title %}{% trans "إعدادات اللغة" %} | {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "إعدادات اللغة" %}</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addLanguageModal">
            <i class="fas fa-plus-circle me-1"></i> {% trans "إضافة لغة جديدة" %}
        </button>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "اللغات المتاحة" %}</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="languagesTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>{% trans "اللغة" %}</th>
                                    <th>{% trans "اتجاه النص" %}</th>
                                    <th>{% trans "افتراضي" %}</th>
                                    <th>{% trans "الحالة" %}</th>
                                    <th>{% trans "الإجراءات" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for language in languages %}
                                <tr>
                                    <td>{{ language.get_language_display }}</td>
                                    <td>{{ language.get_direction_display }}</td>
                                    <td>
                                        {% if language.is_default %}
                                        <span class="badge bg-success">{% trans "نعم" %}</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{% trans "لا" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if language.is_enabled %}
                                        <span class="badge bg-success">{% trans "مفعل" %}</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{% trans "معطل" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-info edit-language"
                                            data-id="{{ language.id }}"
                                            data-language="{{ language.language }}"
                                            data-direction="{{ language.direction }}"
                                            data-is-default="{{ language.is_default|yesno:'true,false' }}"
                                            data-is-enabled="{{ language.is_enabled|yesno:'true,false' }}"
                                            data-bs-toggle="modal" data-bs-target="#editLanguageModal">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        {% if not language.is_default %}
                                        <button type="button" class="btn btn-sm btn-danger delete-language"
                                            data-id="{{ language.id }}"
                                            data-name="{{ language.get_language_display }}"
                                            data-bs-toggle="modal" data-bs-target="#deleteLanguageModal">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center">{% trans "لا توجد لغات مضافة" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "معلومات مساعدة" %}</h6>
                </div>
                <div class="card-body">

                    <h5 class="text-gray-800 mb-3">{% trans "حول إعدادات اللغة" %}</h5>
                    <p>{% trans "تتيح لك إعدادات اللغة تحديد اللغات المتاحة في النظام واتجاه النص لكل لغة." %}</p>

                    <h6 class="text-gray-800 mb-2">{% trans "اللغة الافتراضية" %}</h6>
                    <p>{% trans "اللغة الافتراضية هي اللغة التي سيتم استخدامها عند تسجيل الدخول لأول مرة أو عند عدم تحديد لغة." %}</p>

                    <h6 class="text-gray-800 mb-2">{% trans "اتجاه النص" %}</h6>
                    <p>{% trans "يحدد اتجاه النص كيفية عرض النصوص في النظام:" %}</p>
                    <ul>
                        <li>{% trans "من اليمين إلى اليسار (RTL): للغات مثل العربية والعبرية والفارسية." %}</li>
                        <li>{% trans "من اليسار إلى اليمين (LTR): للغات مثل الإنجليزية والفرنسية والإسبانية." %}</li>
                    </ul>

                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-1"></i> {% trans "ملاحظة: يجب أن تكون هناك لغة افتراضية واحدة على الأقل في النظام." %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Language Modal -->
<div class="modal fade" id="addLanguageModal" tabindex="-1" aria-labelledby="addLanguageModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{% url 'settings_app:language_settings' %}">
                {% csrf_token %}
                <input type="hidden" name="action" value="add">
                <div class="modal-header">
                    <h5 class="modal-title" id="addLanguageModalLabel">{% trans "إضافة لغة جديدة" %}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="language" class="form-label">{% trans "اللغة" %}</label>
                        <select class="form-select" id="language" name="language" required>
                            <option value="ar">{% trans "العربية" %}</option>
                            <option value="en">{% trans "الإنجليزية" %}</option>
                            <option value="fr">{% trans "الفرنسية" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="direction" class="form-label">{% trans "اتجاه النص" %}</label>
                        <select class="form-select" id="direction" name="direction" required>
                            <option value="rtl">{% trans "من اليمين إلى اليسار (RTL)" %}</option>
                            <option value="ltr">{% trans "من اليسار إلى اليمين (LTR)" %}</option>
                        </select>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="is_default" name="is_default">
                        <label class="form-check-label" for="is_default">
                            {% trans "تعيين كلغة افتراضية" %}
                        </label>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="is_enabled" name="is_enabled" checked>
                        <label class="form-check-label" for="is_enabled">
                            {% trans "تفعيل اللغة" %}
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "إضافة" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Language Modal -->
<div class="modal fade" id="editLanguageModal" tabindex="-1" aria-labelledby="editLanguageModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{% url 'settings_app:language_settings' %}">
                {% csrf_token %}
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="language_id" id="edit_language_id">
                <div class="modal-header">
                    <h5 class="modal-title" id="editLanguageModalLabel">{% trans "تعديل اللغة" %}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_language" class="form-label">{% trans "اللغة" %}</label>
                        <select class="form-select" id="edit_language" name="language" required>
                            <option value="ar">{% trans "العربية" %}</option>
                            <option value="en">{% trans "الإنجليزية" %}</option>
                            <option value="fr">{% trans "الفرنسية" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_direction" class="form-label">{% trans "اتجاه النص" %}</label>
                        <select class="form-select" id="edit_direction" name="direction" required>
                            <option value="rtl">{% trans "من اليمين إلى اليسار (RTL)" %}</option>
                            <option value="ltr">{% trans "من اليسار إلى اليمين (LTR)" %}</option>
                        </select>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="edit_is_default" name="is_default">
                        <label class="form-check-label" for="edit_is_default">
                            {% trans "تعيين كلغة افتراضية" %}
                        </label>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="edit_is_enabled" name="is_enabled">
                        <label class="form-check-label" for="edit_is_enabled">
                            {% trans "تفعيل اللغة" %}
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "حفظ التغييرات" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Language Modal -->
<div class="modal fade" id="deleteLanguageModal" tabindex="-1" aria-labelledby="deleteLanguageModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" id="deleteLanguageForm">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteLanguageModalLabel">{% trans "حذف اللغة" %}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>{% trans "هل أنت متأكد من رغبتك في حذف اللغة" %} <span id="language_name_to_delete"></span>؟</p>
                    <p class="text-danger">{% trans "هذا الإجراء لا يمكن التراجع عنه." %}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-danger">{% trans "حذف" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Language selection changes direction automatically
        const languageSelect = document.getElementById('language');
        const directionSelect = document.getElementById('direction');

        languageSelect.addEventListener('change', function() {
            if (this.value === 'ar') {
                directionSelect.value = 'rtl';
            } else {
                directionSelect.value = 'ltr';
            }
        });

        // Edit Language
        document.querySelectorAll('.edit-language').forEach(function(button) {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const language = this.getAttribute('data-language');
                const direction = this.getAttribute('data-direction');
                const isDefault = this.getAttribute('data-is-default') === 'true';
                const isEnabled = this.getAttribute('data-is-enabled') === 'true';

                document.getElementById('edit_language_id').value = id;
                document.getElementById('edit_language').value = language;
                document.getElementById('edit_direction').value = direction;
                document.getElementById('edit_is_default').checked = isDefault;
                document.getElementById('edit_is_enabled').checked = isEnabled;
            });
        });

        // Delete Language
        document.querySelectorAll('.delete-language').forEach(function(button) {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');

                document.getElementById('language_name_to_delete').textContent = name;
                document.getElementById('deleteLanguageForm').action = "{% url 'settings_app:delete_language' 0 %}".replace('0', id);
            });
        });
    });
</script>
{% endblock %}
