# Generated by Django 5.2 on 2025-04-23 13:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('settings_app', '0006_alter_invoicesetting_email_body_template_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='invoicesetting',
            name='font_family',
            field=models.CharField(blank=True, choices=[('tajawal', 'Tajawal'), ('cairo', 'Cairo'), ('noto', 'Noto Sans Arabic'), ('droid', 'Droid Arabic Naskh')], default='tajawal', max_length=10, null=True, verbose_name='نوع الخط'),
        ),
        migrations.AlterField(
            model_name='invoicesetting',
            name='paper_size',
            field=models.CharField(blank=True, choices=[('a4', 'A4'), ('a5', 'A5'), ('letter', 'Letter'), ('thermal', 'إيصال حراري (80مم)')], default='a4', max_length=10, null=True, verbose_name='حجم الورق'),
        ),
    ]
