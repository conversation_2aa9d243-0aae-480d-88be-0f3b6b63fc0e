import os
import base64
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.backends import default_backend
from django.conf import settings

def generate_key():
    """
    توليد مفتاح تشفير عشوائي
    
    Returns:
        str: مفتاح التشفير المشفر بـ base64
    """
    key = os.urandom(32)  # AES-256 يتطلب مفتاح بطول 32 بايت
    return base64.b64encode(key).decode('utf-8')

def encrypt_file(file_path, key_str, output_path=None):
    """
    تشفير ملف باستخدام خوارزمية AES-256
    
    Args:
        file_path (str): مسار الملف المراد تشفيره
        key_str (str): مفتاح التشفير المشفر بـ base64
        output_path (str, optional): مسار الملف المشفر. إذا لم يتم تحديده، سيتم استخدام اسم الملف الأصلي مع إضافة .enc
        
    Returns:
        str: مسار الملف المشفر
    """
    if output_path is None:
        output_path = file_path + '.enc'
    
    # تحويل المفتاح من base64 إلى بايت
    key = base64.b64decode(key_str)
    
    # توليد متجه التهيئة (IV) عشوائي
    iv = os.urandom(16)
    
    # إنشاء كائن التشفير
    cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
    encryptor = cipher.encryptor()
    
    # إضافة بطانة (padding) للبيانات
    padder = padding.PKCS7(algorithms.AES.block_size).padder()
    
    with open(file_path, 'rb') as f_in, open(output_path, 'wb') as f_out:
        # كتابة متجه التهيئة في بداية الملف
        f_out.write(iv)
        
        # قراءة الملف وتشفيره بأجزاء
        while True:
            chunk = f_in.read(8192)
            if not chunk:
                break
            
            # إضافة بطانة للجزء الأخير فقط
            if len(chunk) < 8192:
                padded_chunk = padder.update(chunk) + padder.finalize()
                encrypted_chunk = encryptor.update(padded_chunk)
            else:
                encrypted_chunk = encryptor.update(chunk)
            
            f_out.write(encrypted_chunk)
        
        # كتابة البيانات النهائية
        if len(chunk) >= 8192:
            f_out.write(encryptor.finalize())
    
    return output_path

def decrypt_file(file_path, key_str, output_path=None):
    """
    فك تشفير ملف مشفر باستخدام خوارزمية AES-256
    
    Args:
        file_path (str): مسار الملف المشفر
        key_str (str): مفتاح التشفير المشفر بـ base64
        output_path (str, optional): مسار الملف بعد فك التشفير. إذا لم يتم تحديده، سيتم استخدام اسم الملف الأصلي بدون .enc
        
    Returns:
        str: مسار الملف بعد فك التشفير
    """
    if output_path is None:
        if file_path.endswith('.enc'):
            output_path = file_path[:-4]
        else:
            output_path = file_path + '.dec'
    
    # تحويل المفتاح من base64 إلى بايت
    key = base64.b64decode(key_str)
    
    with open(file_path, 'rb') as f_in:
        # قراءة متجه التهيئة من بداية الملف
        iv = f_in.read(16)
        
        # إنشاء كائن فك التشفير
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
        decryptor = cipher.decryptor()
        
        # إنشاء كائن إزالة البطانة
        unpadder = padding.PKCS7(algorithms.AES.block_size).unpadder()
        
        with open(output_path, 'wb') as f_out:
            # قراءة الملف وفك تشفيره بأجزاء
            while True:
                chunk = f_in.read(8192)
                if not chunk:
                    break
                
                decrypted_chunk = decryptor.update(chunk)
                
                # إزالة البطانة من الجزء الأخير فقط
                if len(chunk) < 8192:
                    try:
                        unpadded_chunk = unpadder.update(decrypted_chunk) + unpadder.finalize()
                        f_out.write(unpadded_chunk)
                    except Exception as e:
                        # في حالة حدوث خطأ في إزالة البطانة، قد يكون الملف تالفًا أو المفتاح غير صحيح
                        os.remove(output_path)
                        raise ValueError(f"خطأ في فك تشفير الملف: {str(e)}")
                else:
                    f_out.write(decrypted_chunk)
            
            # كتابة البيانات النهائية
            if len(chunk) >= 8192:
                try:
                    f_out.write(unpadder.update(decryptor.finalize()) + unpadder.finalize())
                except Exception as e:
                    os.remove(output_path)
                    raise ValueError(f"خطأ في فك تشفير الملف: {str(e)}")
    
    return output_path

def verify_file_integrity(file_path, checksum):
    """
    التحقق من سلامة الملف باستخدام الـ checksum
    
    Args:
        file_path (str): مسار الملف
        checksum (str): قيمة الـ checksum المتوقعة
        
    Returns:
        bool: True إذا كان الملف سليمًا، False إذا كان تالفًا
    """
    import hashlib
    
    # حساب الـ SHA-256 للملف
    sha256_hash = hashlib.sha256()
    
    with open(file_path, 'rb') as f:
        # قراءة الملف بأجزاء لتجنب استهلاك الذاكرة
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256_hash.update(byte_block)
    
    # مقارنة الـ checksum المحسوب مع القيمة المتوقعة
    calculated_checksum = sha256_hash.hexdigest()
    return calculated_checksum == checksum

def calculate_checksum(file_path):
    """
    حساب الـ checksum للملف باستخدام SHA-256
    
    Args:
        file_path (str): مسار الملف
        
    Returns:
        str: قيمة الـ checksum
    """
    import hashlib
    
    sha256_hash = hashlib.sha256()
    
    with open(file_path, 'rb') as f:
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256_hash.update(byte_block)
    
    return sha256_hash.hexdigest()
