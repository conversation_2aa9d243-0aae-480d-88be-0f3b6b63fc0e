#!/usr/bin/env python
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'auto_parts_pos.settings')
django.setup()

from inventory.models import Product
from inventory.barcode_models import Barcode, BarcodeType

def create_test_barcode():
    # التحقق من المنتجات الموجودة
    products = Product.objects.all()[:3]
    print('Products found:')
    for p in products:
        print(f'Product ID: {p.id}, Name: {p.name}, Price: {p.selling_price}')

    # التحقق من أنواع الباركود
    barcode_types = BarcodeType.objects.all()
    print('\nBarcode types found:')
    for bt in barcode_types:
        print(f'Type: {bt.name}, Code: {bt.code}')

    # إنشاء باركود تجريبي
    if products.exists() and barcode_types.exists():
        product = products.first()
        barcode_type = barcode_types.first()

        # التحقق من وجود باركود مسبق
        existing = Barcode.objects.filter(barcode_number='1282235856942741').first()
        if existing:
            print(f'\nTest barcode already exists: {existing.barcode_number} for product: {existing.product.name}')
        else:
            test_barcode = Barcode.objects.create(
                product=product,
                barcode_type=barcode_type,
                barcode_number='1282235856942741',
                is_primary=True
            )
            print(f'\nCreated test barcode: {test_barcode.barcode_number} for product: {product.name}')
    else:
        print('\nNo products or barcode types found')

if __name__ == '__main__':
    create_test_barcode()
