{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/settings.css' %}">
{% endblock %}

{% block title %}{% trans "الملف الشخصي" %} | {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "الملف الشخصي" %}</h1>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="row">
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "معلومات المستخدم" %}</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <img class="img-profile rounded-circle" src="{% static 'img/user-avatar.png' %}" width="150" height="150">
                        <h5 class="mt-3">{{ user_obj.get_full_name|default:user_obj.username }}</h5>
                        <p class="text-muted">
                            {% for group in user_obj.groups.all %}
                                <span class="badge bg-primary">{{ group.name }}</span>
                            {% endfor %}
                            {% if user_obj.is_superuser %}
                                <span class="badge bg-danger">{% trans "مدير النظام" %}</span>
                            {% endif %}
                        </p>
                    </div>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">
                            <strong>{% trans "اسم المستخدم:" %}</strong> {{ user_obj.username }}
                        </li>
                        <li class="list-group-item">
                            <strong>{% trans "البريد الإلكتروني:" %}</strong> {{ user_obj.email|default:"-" }}
                        </li>
                        <li class="list-group-item">
                            <strong>{% trans "آخر تسجيل دخول:" %}</strong> 
                            {% if user_obj.last_login %}
                                {{ user_obj.last_login|date:"Y-m-d H:i" }}
                            {% else %}
                                {% trans "لم يسجل الدخول بعد" %}
                            {% endif %}
                        </li>
                        <li class="list-group-item">
                            <strong>{% trans "تاريخ الانضمام:" %}</strong> {{ user_obj.date_joined|date:"Y-m-d" }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "تعديل الملف الشخصي" %}</h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="first_name" class="form-label">{% trans "الاسم الأول" %}</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" value="{{ user_obj.first_name }}">
                            </div>
                            <div class="col-md-6">
                                <label for="last_name" class="form-label">{% trans "الاسم الأخير" %}</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" value="{{ user_obj.last_name }}">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">{% trans "البريد الإلكتروني" %}</label>
                            <input type="email" class="form-control" id="email" name="email" value="{{ user_obj.email }}">
                        </div>
                        
                        <hr class="my-4">
                        <h5>{% trans "تغيير كلمة المرور" %}</h5>
                        <p class="text-muted small">{% trans "اترك الحقول فارغة إذا كنت لا ترغب في تغيير كلمة المرور" %}</p>
                        
                        <div class="mb-3">
                            <label for="current_password" class="form-label">{% trans "كلمة المرور الحالية" %}</label>
                            <input type="password" class="form-control" id="current_password" name="current_password">
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="new_password" class="form-label">{% trans "كلمة المرور الجديدة" %}</label>
                                <input type="password" class="form-control" id="new_password" name="new_password">
                            </div>
                            <div class="col-md-6">
                                <label for="confirm_password" class="form-label">{% trans "تأكيد كلمة المرور الجديدة" %}</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                            </div>
                        </div>
                        
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> {% trans "حفظ التغييرات" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
