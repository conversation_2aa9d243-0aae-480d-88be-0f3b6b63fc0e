from django.urls import path
from . import views

urlpatterns = [
    path('', views.index, name='index'),
    path('add/', views.add_employee, name='add_employee'),
    path('edit/<int:employee_id>/', views.edit_employee, name='edit_employee'),
    path('view/<int:employee_id>/', views.view_employee, name='view_employee'),
    path('delete/<int:employee_id>/', views.delete_employee, name='delete_employee'),
    path('attendance/', views.attendance, name='attendance'),
    path('roles/', views.roles, name='roles'),
    path('roles-permissions/', views.roles_permissions, name='roles_permissions'),

    # وظائف استيراد وتصدير البيانات
    path('export/', views.export_employees, name='export_employees'),
    path('import/', views.import_employees, name='import_employees'),
    path('download-template/', views.download_template, name='download_template'),

    # وظائف AJAX
    path('reset-password/', views.reset_password, name='reset_password'),
    path('toggle-status/', views.toggle_status, name='toggle_status'),
    path('bulk-delete/', views.bulk_delete, name='bulk_delete'),
    path('bulk-activate/', views.bulk_activate, name='bulk_activate'),
    path('bulk-deactivate/', views.bulk_deactivate, name='bulk_deactivate'),
]
