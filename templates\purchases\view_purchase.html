{% extends 'base.html' %}
{% load i18n %}
{% load custom_filters %}

{% block title %}{% trans "عرض طلب الشراء" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .purchase-header {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .purchase-info {
        margin-bottom: 0;
    }

    .purchase-info dt {
        font-weight: bold;
    }

    .purchase-info dd {
        margin-bottom: 0.5rem;
    }

    .status-badge {
        font-size: 1rem;
        padding: 0.5rem 0.75rem;
    }

    .payment-badge {
        font-size: 0.9rem;
        padding: 0.4rem 0.6rem;
    }

    .product-image-small {
        width: 50px;
        height: 50px;
        object-fit: contain;
        border-radius: 4px;
    }

    .nav-tabs .nav-link {
        font-weight: bold;
    }

    .nav-tabs .nav-link.active {
        border-bottom: 3px solid #4e73df;
    }

    .payment-card {
        transition: all 0.3s;
    }

    .payment-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .invoice-card {
        transition: all 0.3s;
    }

    .invoice-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    @media print {
        .no-print {
            display: none !important;
        }

        .card {
            border: none !important;
            box-shadow: none !important;
        }

        .card-header {
            background-color: white !important;
            border-bottom: 1px solid #ddd !important;
        }

        .purchase-header {
            background-color: white !important;
            padding: 0 !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4 no-print">
    <h1 class="h3 mb-0 text-gray-800">{% trans "عرض طلب الشراء" %}</h1>
    <div>
        <button type="button" class="btn btn-secondary me-2" onclick="window.print()">
            <i class="fas fa-print me-1"></i> {% trans "طباعة" %}
        </button>
        <a href="{% url 'purchases:purchase_orders' %}" class="btn btn-primary">
            <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى طلبات الشراء" %}
        </a>
    </div>
</div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show no-print" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<!-- Purchase Header -->
<div class="purchase-header">
    <div class="row">
        <div class="col-md-6">
            <h2>{{ purchase.reference_number }}</h2>
            <p class="text-muted">{% trans "تاريخ الطلب" %}: {{ purchase.date|date:"Y-m-d" }}</p>
        </div>
        <div class="col-md-6 text-md-end">
            <div class="mb-2">
                {% if purchase.status == 'pending' %}
                <span class="badge bg-warning text-dark status-badge">{% trans "معلق" %}</span>
                {% elif purchase.status == 'received' %}
                <span class="badge bg-success status-badge">{% trans "تم الاستلام" %}</span>
                {% elif purchase.status == 'cancelled' %}
                <span class="badge bg-danger status-badge">{% trans "ملغي" %}</span>
                {% endif %}
            </div>
            <div>
                {% if purchase.payment_status == 'unpaid' %}
                <span class="badge bg-danger payment-badge">{% trans "غير مدفوع" %}</span>
                {% elif purchase.payment_status == 'partial' %}
                <span class="badge bg-warning text-dark payment-badge">{% trans "مدفوع جزئياً" %}</span>
                {% elif purchase.payment_status == 'paid' %}
                <span class="badge bg-success payment-badge">{% trans "مدفوع بالكامل" %}</span>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-6">
            <h5>{% trans "معلومات المورد" %}</h5>
            <dl class="row purchase-info">
                <dt class="col-sm-4">{% trans "اسم المورد" %}</dt>
                <dd class="col-sm-8">
                    <a href="{% url 'purchases:view_supplier' supplier_id=purchase.supplier.id %}" class="no-print">
                        {{ purchase.supplier.name }}
                    </a>
                    <span class="d-none d-print-inline">{{ purchase.supplier.name }}</span>
                </dd>

                <dt class="col-sm-4">{% trans "رقم الهاتف" %}</dt>
                <dd class="col-sm-8">{{ purchase.supplier.phone }}</dd>

                {% if purchase.supplier.email %}
                <dt class="col-sm-4">{% trans "البريد الإلكتروني" %}</dt>
                <dd class="col-sm-8">{{ purchase.supplier.email }}</dd>
                {% endif %}

                {% if purchase.supplier.address %}
                <dt class="col-sm-4">{% trans "العنوان" %}</dt>
                <dd class="col-sm-8">{{ purchase.supplier.address }}</dd>
                {% endif %}
            </dl>
        </div>
        <div class="col-md-6">
            <h5>{% trans "معلومات الطلب" %}</h5>
            <dl class="row purchase-info">
                <dt class="col-sm-5">{% trans "تاريخ التسليم المتوقع" %}</dt>
                <dd class="col-sm-7">
                    {% if purchase.expected_delivery_date %}
                        {{ purchase.expected_delivery_date|date:"Y-m-d" }}
                    {% else %}
                        -
                    {% endif %}
                </dd>

                <dt class="col-sm-5">{% trans "تاريخ التسليم الفعلي" %}</dt>
                <dd class="col-sm-7">
                    {% if purchase.actual_delivery_date %}
                        {{ purchase.actual_delivery_date|date:"Y-m-d" }}
                    {% else %}
                        -
                    {% endif %}
                </dd>

                <dt class="col-sm-5">{% trans "الموظف" %}</dt>
                <dd class="col-sm-7">{{ purchase.employee.get_full_name|default:purchase.employee.username }}</dd>

                <dt class="col-sm-5">{% trans "تاريخ الإنشاء" %}</dt>
                <dd class="col-sm-7">{{ purchase.created_at|date:"Y-m-d H:i" }}</dd>
            </dl>
        </div>
    </div>

    <div class="row mt-3 no-print">
        <div class="col-12">
            <div class="btn-group">
                {% if purchase.status == 'pending' %}
                <a href="{% url 'purchases:edit_purchase' purchase_id=purchase.id %}" class="btn btn-primary">
                    <i class="fas fa-edit me-1"></i> {% trans "تعديل" %}
                </a>
                <a href="{% url 'purchases:receive_purchase' purchase_id=purchase.id %}" class="btn btn-success">
                    <i class="fas fa-check me-1"></i> {% trans "استلام" %}
                </a>
                <a href="{% url 'purchases:delete_purchase' purchase_id=purchase.id %}" class="btn btn-danger">
                    <i class="fas fa-trash me-1"></i> {% trans "حذف" %}
                </a>
                {% endif %}

                <a href="{% url 'purchases:add_invoice' purchase_id=purchase.id %}" class="btn btn-info">
                    <i class="fas fa-file-invoice me-1"></i> {% trans "إضافة فاتورة" %}
                </a>

                {% if purchase.payment_status != 'paid' %}
                <a href="{% url 'purchases:add_payment' purchase_id=purchase.id %}" class="btn btn-warning">
                    <i class="fas fa-money-bill-wave me-1"></i> {% trans "إضافة دفعة" %}
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Tabs Navigation -->
<ul class="nav nav-tabs mb-4 no-print" id="purchaseTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="items-tab" data-bs-toggle="tab" data-bs-target="#items" type="button" role="tab" aria-controls="items" aria-selected="true">
            <i class="fas fa-box me-1"></i> {% trans "المنتجات" %}
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="invoices-tab" data-bs-toggle="tab" data-bs-target="#invoices" type="button" role="tab" aria-controls="invoices" aria-selected="false">
            <i class="fas fa-file-invoice me-1"></i> {% trans "الفواتير" %} <span class="badge bg-info">{{ invoices|length }}</span>
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="payments-tab" data-bs-toggle="tab" data-bs-target="#payments" type="button" role="tab" aria-controls="payments" aria-selected="false">
            <i class="fas fa-money-bill-wave me-1"></i> {% trans "المدفوعات" %} <span class="badge bg-info">{{ payments|length }}</span>
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="notes-tab" data-bs-toggle="tab" data-bs-target="#notes" type="button" role="tab" aria-controls="notes" aria-selected="false">
            <i class="fas fa-sticky-note me-1"></i> {% trans "الملاحظات" %}
        </button>
    </li>
</ul>

<!-- Tab Content -->
<div class="tab-content" id="purchaseTabsContent">
    <!-- Items Tab -->
    <div class="tab-pane fade show active" id="items" role="tabpanel" aria-labelledby="items-tab">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "المنتجات" %}</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th width="50px">{% trans "صورة" %}</th>
                                <th>{% trans "المنتج" %}</th>
                                <th width="100px">{% trans "الكمية" %}</th>
                                <th width="100px">{% trans "الكمية المستلمة" %}</th>
                                <th width="150px">{% trans "سعر الوحدة" %}</th>
                                <th width="150px">{% trans "المجموع" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in items %}
                            <tr>
                                <td>
                                    {% if item.product.image %}
                                    <img src="{{ item.product.image.url }}" alt="{{ item.product.name }}" class="product-image-small">
                                    {% else %}
                                    <div class="text-center">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ item.product.code }}</strong> - {{ item.product.name }}
                                </td>
                                <td>{{ item.quantity }}</td>
                                <td>
                                    {% if purchase.status == 'received' %}
                                        {{ item.received_quantity }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>{{ item.unit_price|floatformat:2 }} ر.س</td>
                                <td>{{ item.subtotal|floatformat:2 }} ر.س</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="5" class="text-end"><strong>{% trans "المجموع الفرعي" %}</strong></td>
                                <td>{{ purchase.subtotal|floatformat:2 }} ر.س</td>
                            </tr>
                            <tr>
                                <td colspan="5" class="text-end"><strong>{% trans "الضريبة" %} ({{ purchase.tax_rate }}%)</strong></td>
                                <td>{{ purchase.tax_amount|floatformat:2 }} ر.س</td>
                            </tr>
                            <tr>
                                <td colspan="5" class="text-end"><strong>{% trans "تكلفة الشحن" %}</strong></td>
                                <td>{{ purchase.shipping_cost|floatformat:2 }} ر.س</td>
                            </tr>
                            <tr>
                                <td colspan="5" class="text-end"><strong>{% trans "المجموع الكلي" %}</strong></td>
                                <td><strong>{{ purchase.total_amount|floatformat:2 }} ر.س</strong></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoices Tab -->
    <div class="tab-pane fade" id="invoices" role="tabpanel" aria-labelledby="invoices-tab">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "الفواتير" %}</h6>
                <a href="{% url 'purchases:add_invoice' purchase_id=purchase.id %}" class="btn btn-sm btn-primary no-print">
                    <i class="fas fa-plus me-1"></i> {% trans "إضافة فاتورة" %}
                </a>
            </div>
            <div class="card-body">
                {% if invoices %}
                <div class="row">
                    {% for invoice in invoices %}
                    <div class="col-md-6 mb-4">
                        <div class="card invoice-card h-100">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">{{ invoice.invoice_number }}</h5>
                                    <span class="badge {% if invoice.status == 'pending' %}bg-warning text-dark{% elif invoice.status == 'verified' %}bg-info{% elif invoice.status == 'paid' %}bg-success{% elif invoice.status == 'cancelled' %}bg-danger{% endif %}">
                                        {% if invoice.status == 'pending' %}{% trans "معلقة" %}
                                        {% elif invoice.status == 'verified' %}{% trans "تم التحقق" %}
                                        {% elif invoice.status == 'paid' %}{% trans "مدفوعة" %}
                                        {% elif invoice.status == 'cancelled' %}{% trans "ملغية" %}
                                        {% endif %}
                                    </span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-6">
                                        <strong>{% trans "تاريخ الفاتورة" %}:</strong><br>
                                        {{ invoice.invoice_date|date:"Y-m-d" }}
                                    </div>
                                    <div class="col-6">
                                        <strong>{% trans "تاريخ الاستحقاق" %}:</strong><br>
                                        {{ invoice.due_date|date:"Y-m-d"|default:"-" }}
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-12">
                                        <strong>{% trans "المبلغ" %}:</strong><br>
                                        {{ invoice.amount|floatformat:2 }} ر.س
                                    </div>
                                </div>
                                {% if invoice.notes %}
                                <div class="row mb-3">
                                    <div class="col-12">
                                        <strong>{% trans "ملاحظات" %}:</strong><br>
                                        {{ invoice.notes }}
                                    </div>
                                </div>
                                {% endif %}
                                {% if invoice.invoice_file %}
                                <div class="row mb-3">
                                    <div class="col-12">
                                        <a href="{{ invoice.invoice_file.url }}" target="_blank" class="btn btn-sm btn-info">
                                            <i class="fas fa-file-download me-1"></i> {% trans "تحميل الفاتورة" %}
                                        </a>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                            <div class="card-footer no-print">
                                <div class="btn-group w-100">
                                    <a href="{% url 'purchases:view_invoice' invoice_id=invoice.id %}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye me-1"></i> {% trans "عرض" %}
                                    </a>
                                    <a href="{% url 'purchases:edit_invoice' invoice_id=invoice.id %}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit me-1"></i> {% trans "تعديل" %}
                                    </a>
                                    <a href="{% url 'purchases:delete_invoice' invoice_id=invoice.id %}" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash me-1"></i> {% trans "حذف" %}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-info">
                    {% trans "لا توجد فواتير مرتبطة بهذا الطلب." %}
                    <a href="{% url 'purchases:add_invoice' purchase_id=purchase.id %}" class="alert-link no-print">{% trans "إضافة فاتورة" %}</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Payments Tab -->
    <div class="tab-pane fade" id="payments" role="tabpanel" aria-labelledby="payments-tab">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "المدفوعات" %}</h6>
                {% if purchase.payment_status != 'paid' %}
                <a href="{% url 'purchases:add_payment' purchase_id=purchase.id %}" class="btn btn-sm btn-primary no-print">
                    <i class="fas fa-plus me-1"></i> {% trans "إضافة دفعة" %}
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if payments %}
                <div class="row">
                    {% for payment in payments %}
                    <div class="col-md-6 mb-4">
                        <div class="card payment-card h-100">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">{% trans "دفعة" %} #{{ payment.id }}</h5>
                                    <span class="badge bg-success">{{ payment.amount|floatformat:2 }} ر.س</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-6">
                                        <strong>{% trans "تاريخ الدفع" %}:</strong><br>
                                        {{ payment.payment_date|date:"Y-m-d" }}
                                    </div>
                                    <div class="col-6">
                                        <strong>{% trans "طريقة الدفع" %}:</strong><br>
                                        {% if payment.payment_method == 'cash' %}{% trans "نقدي" %}
                                        {% elif payment.payment_method == 'bank_transfer' %}{% trans "تحويل بنكي" %}
                                        {% elif payment.payment_method == 'check' %}{% trans "شيك" %}
                                        {% elif payment.payment_method == 'credit_card' %}{% trans "بطاقة ائتمان" %}
                                        {% endif %}
                                    </div>
                                </div>
                                {% if payment.invoice %}
                                <div class="row mb-3">
                                    <div class="col-12">
                                        <strong>{% trans "الفاتورة" %}:</strong><br>
                                        <a href="{% url 'purchases:view_invoice' invoice_id=payment.invoice.id %}" class="no-print">
                                            {{ payment.invoice.invoice_number }}
                                        </a>
                                        <span class="d-none d-print-inline">{{ payment.invoice.invoice_number }}</span>
                                    </div>
                                </div>
                                {% endif %}
                                {% if payment.reference %}
                                <div class="row mb-3">
                                    <div class="col-12">
                                        <strong>{% trans "المرجع" %}:</strong><br>
                                        {{ payment.reference }}
                                    </div>
                                </div>
                                {% endif %}
                                {% if payment.notes %}
                                <div class="row mb-3">
                                    <div class="col-12">
                                        <strong>{% trans "ملاحظات" %}:</strong><br>
                                        {{ payment.notes }}
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                            <div class="card-footer no-print">
                                <div class="btn-group w-100">
                                    <a href="{% url 'purchases:edit_payment' payment_id=payment.id %}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit me-1"></i> {% trans "تعديل" %}
                                    </a>
                                    <a href="{% url 'purchases:delete_payment' payment_id=payment.id %}" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash me-1"></i> {% trans "حذف" %}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Payment Summary -->
                <div class="card mt-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{% trans "ملخص المدفوعات" %}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-bordered">
                                    <tr>
                                        <th>{% trans "المبلغ الإجمالي" %}</th>
                                        <td>{{ purchase.total_amount|floatformat:2 }} ر.س</td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "المبلغ المدفوع" %}</th>
                                        <td>{{ payments|sum:'amount'|floatformat:2 }} ر.س</td>
                                    </tr>
                                    <tr>
                                        <th>{% trans "المبلغ المتبقي" %}</th>
                                        <td>{{ purchase.total_amount|sub:payments|sum:'amount'|floatformat:2 }} ر.س</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <div class="progress" style="height: 30px;">
                                    {% with paid_percentage=payments|sum:'amount'|div:purchase.total_amount|mul:100 %}
                                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ paid_percentage }}%;" aria-valuenow="{{ paid_percentage }}" aria-valuemin="0" aria-valuemax="100">{{ paid_percentage|floatformat:0 }}%</div>
                                    {% endwith %}
                                </div>
                                <div class="text-center mt-2">
                                    {% if purchase.payment_status == 'unpaid' %}
                                    <span class="badge bg-danger">{% trans "غير مدفوع" %}</span>
                                    {% elif purchase.payment_status == 'partial' %}
                                    <span class="badge bg-warning text-dark">{% trans "مدفوع جزئياً" %}</span>
                                    {% elif purchase.payment_status == 'paid' %}
                                    <span class="badge bg-success">{% trans "مدفوع بالكامل" %}</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="alert alert-info">
                    {% trans "لا توجد مدفوعات مرتبطة بهذا الطلب." %}
                    {% if purchase.payment_status != 'paid' %}
                    <a href="{% url 'purchases:add_payment' purchase_id=purchase.id %}" class="alert-link no-print">{% trans "إضافة دفعة" %}</a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Notes Tab -->
    <div class="tab-pane fade" id="notes" role="tabpanel" aria-labelledby="notes-tab">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "الملاحظات" %}</h6>
            </div>
            <div class="card-body">
                {% if purchase.notes %}
                <div class="p-3 bg-light rounded">
                    {{ purchase.notes|linebreaks }}
                </div>
                {% else %}
                <div class="alert alert-info">
                    {% trans "لا توجد ملاحظات لهذا الطلب." %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip();

        // Show active tab from URL hash
        var hash = window.location.hash;
        if (hash) {
            $('.nav-tabs a[href="' + hash + '"]').tab('show');
        }

        // Update URL hash when tab changes
        $('.nav-tabs a').on('shown.bs.tab', function (e) {
            window.location.hash = e.target.hash;
        });
    });
</script>
{% endblock %}
