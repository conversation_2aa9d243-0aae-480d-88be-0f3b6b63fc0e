{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "حذف الإيراد" %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card shadow">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0">{% trans "حذف الإيراد" %}</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <h5>{% trans "هل أنت متأكد من حذف هذا الإيراد؟" %}</h5>
                <p>{% trans "هذا الإجراء لا يمكن التراجع عنه. سيتم خصم المبلغ من رصيد الحساب." %}</p>
            </div>
            
            <div class="card mb-4">
                <div class="card-body">
                    <h6 class="card-subtitle mb-3 text-muted">{% trans "تفاصيل الإيراد" %}</h6>
                    <table class="table table-sm">
                        <tr>
                            <th>{% trans "التاريخ" %}</th>
                            <td>{{ income.date }}</td>
                        </tr>
                        <tr>
                            <th>{% trans "الفئة" %}</th>
                            <td>{{ income.category.name }}</td>
                        </tr>
                        <tr>
                            <th>{% trans "المبلغ" %}</th>
                            <td class="text-success">{{ income.amount }} {% trans "د.م" %}</td>
                        </tr>
                        <tr>
                            <th>{% trans "الحساب" %}</th>
                            <td>{{ income.account.name }}</td>
                        </tr>
                        <tr>
                            <th>{% trans "الوصف" %}</th>
                            <td>{{ income.description|default:"-" }}</td>
                        </tr>
                        <tr>
                            <th>{% trans "المرجع" %}</th>
                            <td>{{ income.reference|default:"-" }}</td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <form method="post" action="{% url 'finance:delete_income' income.id %}">
                {% csrf_token %}
                <div class="form-group">
                    <button type="submit" class="btn btn-danger">{% trans "نعم، حذف الإيراد" %}</button>
                    <a href="{% url 'finance:income' %}" class="btn btn-secondary">{% trans "إلغاء" %}</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
