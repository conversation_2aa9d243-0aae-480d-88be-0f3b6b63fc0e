<!DOCTYPE html>
<html>
<head>
    <title>Test Barcode AJAX</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Test Barcode AJAX</h1>
    <input type="text" id="barcodeInput" placeholder="Enter barcode" value="1282235856942741">
    <button onclick="testBarcode()">Test Barcode</button>
    <div id="result"></div>

    <script>
        function testBarcode() {
            var barcode = $('#barcodeInput').val();
            console.log('Testing barcode:', barcode);
            
            $.ajax({
                url: "http://127.0.0.1:8000/inventory/barcode/scan/",
                type: 'POST',
                data: {
                    'barcode_number': barcode,
                    'csrfmiddlewaretoken': 'test'  // This will fail but we can see the error
                },
                success: function(data) {
                    console.log('Success:', data);
                    $('#result').html('<div style="color: green;">Success: ' + JSON.stringify(data) + '</div>');
                },
                error: function(xhr, status, error) {
                    console.log('Error:', xhr.responseText);
                    $('#result').html('<div style="color: red;">Error: ' + xhr.responseText + '</div>');
                }
            });
        }
    </script>
</body>
</html>
