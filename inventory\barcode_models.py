from django.db import models
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from .models import Product

class BarcodeType(models.Model):
    """نموذج لأنواع الباركود المدعومة في النظام"""
    name = models.CharField(_('الاسم'), max_length=50)
    code = models.CharField(_('الرمز'), max_length=20, unique=True)
    description = models.TextField(_('الوصف'), blank=True, null=True)
    is_active = models.BooleanField(_('نشط'), default=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('نوع الباركود')
        verbose_name_plural = _('أنواع الباركود')
        ordering = ['name']

    def __str__(self):
        return self.name

class Barcode(models.Model):
    """نموذج للباركود المرتبط بالمنتجات"""
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='barcodes', verbose_name=_('المنتج'))
    barcode_type = models.ForeignKey(BarcodeType, on_delete=models.CASCADE, related_name='barcodes', verbose_name=_('نوع الباركود'))
    barcode_number = models.CharField(_('رقم الباركود'), max_length=100, unique=True)
    is_primary = models.BooleanField(_('باركود أساسي'), default=False, help_text=_('تحديد ما إذا كان هذا هو الباركود الرئيسي للمنتج'))
    is_active = models.BooleanField(_('نشط'), default=True)
    include_price = models.BooleanField(_('تضمين السعر'), default=True, help_text=_('تضمين سعر المنتج في الباركود المطبوع'))
    include_name = models.BooleanField(_('تضمين الاسم'), default=True, help_text=_('تضمين اسم المنتج في الباركود المطبوع'))
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_barcodes', verbose_name=_('تم الإنشاء بواسطة'))
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('باركود')
        verbose_name_plural = _('الباركودات')
        ordering = ['-created_at']
        # ضمان أن كل منتج له باركود أساسي واحد فقط
        constraints = [
            models.UniqueConstraint(fields=['product'], condition=models.Q(is_primary=True), name='unique_primary_barcode_per_product')
        ]

    def __str__(self):
        return f"{self.barcode_number} - {self.product.name}"

    def save(self, *args, **kwargs):
        # إذا كان هذا هو الباركود الأساسي، تأكد من أن جميع الباركودات الأخرى للمنتج ليست أساسية
        if self.is_primary:
            Barcode.objects.filter(product=self.product, is_primary=True).exclude(pk=self.pk).update(is_primary=False)
        super().save(*args, **kwargs)

class BarcodeLog(models.Model):
    """نموذج لتسجيل عمليات الباركود"""
    ACTION_CHOICES = (
        ('create', _('إنشاء')),
        ('update', _('تحديث')),
        ('delete', _('حذف')),
        ('print', _('طباعة')),
        ('scan', _('مسح')),
    )

    barcode = models.ForeignKey(Barcode, on_delete=models.CASCADE, related_name='logs', verbose_name=_('الباركود'), null=True, blank=True)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='barcode_logs', verbose_name=_('المنتج'))
    action = models.CharField(_('الإجراء'), max_length=20, choices=ACTION_CHOICES)
    barcode_number = models.CharField(_('رقم الباركود'), max_length=100)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='barcode_logs', verbose_name=_('المستخدم'))
    details = models.TextField(_('التفاصيل'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)

    class Meta:
        verbose_name = _('سجل الباركود')
        verbose_name_plural = _('سجلات الباركود')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.get_action_display()} - {self.barcode_number} - {self.created_at}"

class BarcodeSettings(models.Model):
    """نموذج لإعدادات الباركود في النظام"""
    default_barcode_type = models.ForeignKey(BarcodeType, on_delete=models.SET_NULL, null=True, blank=True, related_name='default_settings', verbose_name=_('نوع الباركود الافتراضي'))
    default_include_price = models.BooleanField(_('تضمين السعر افتراضيًا'), default=True)
    default_include_name = models.BooleanField(_('تضمين الاسم افتراضيًا'), default=True)
    label_width = models.PositiveIntegerField(_('عرض الملصق (مم)'), default=50)
    label_height = models.PositiveIntegerField(_('ارتفاع الملصق (مم)'), default=30)
    labels_per_row = models.PositiveIntegerField(_('عدد الملصقات في الصف'), default=3)
    labels_per_column = models.PositiveIntegerField(_('عدد الملصقات في العمود'), default=8)
    margin_top = models.PositiveIntegerField(_('الهامش العلوي (مم)'), default=10)
    margin_right = models.PositiveIntegerField(_('الهامش الأيمن (مم)'), default=10)
    margin_bottom = models.PositiveIntegerField(_('الهامش السفلي (مم)'), default=10)
    margin_left = models.PositiveIntegerField(_('الهامش الأيسر (مم)'), default=10)
    updated_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='updated_barcode_settings', verbose_name=_('تم التحديث بواسطة'))
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('إعدادات الباركود')
        verbose_name_plural = _('إعدادات الباركود')

    def __str__(self):
        return _('إعدادات الباركود')

    @classmethod
    def get_settings(cls):
        """الحصول على إعدادات الباركود الحالية أو إنشاء إعدادات افتراضية"""
        settings, created = cls.objects.get_or_create(pk=1)
        return settings